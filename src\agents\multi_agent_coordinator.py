#!/usr/bin/env python3
"""
多智能体协调器
负责协调查询规划智能体和各个专业查询智能体，实现完整的多模态查询流程
"""

import asyncio
import time
import uuid
from typing import List, Dict, Any, Optional, Callable, Set

from .query_planner_agent import QueryPlannerAgent
from .structured_data_agent import StructuredDataAgent
from .structural_relationship_agent import StructuralRelationshipAgent
from .geometry_semantic_agent import GeometrySemanticAgent
from .data_models import (
    BaseTask, QueryResult, SearchResultItem,
    QueryPlannerTask, QueryPlan,
    UnifiedStructuredDataTask,
    UnifiedStructuralQueryTask,
    UnifiedGeometrySemanticTask
)


class MultiAgentCoordinator:
    """多智能体协调器"""
    
    def __init__(self):
        """初始化协调器和各个智能体"""
        self.query_planner = QueryPlannerAgent()
        self.structured_agent = StructuredDataAgent()
        self.structural_agent = StructuralRelationshipAgent()
        self.geometry_semantic_agent = GeometrySemanticAgent()
        
        # 智能体映射
        self.agents = {
            "structured_data": self.structured_agent,
            "structural_relationship": self.structural_agent,
            "geometry_semantic": self.geometry_semantic_agent
        }
        
        # 标记是否已初始化
        self.initialized = False
        
        # 事件回调集合
        self._event_callbacks: Set[Callable] = set()
    
    async def initialize(self):
        """初始化所有智能体，预先连接数据库并加载模型"""
        if self.initialized:
            return
            
        print("🚀 正在初始化多智能体系统...")
        print("📡 连接数据库并加载模型...")
        
        # 并行初始化所有智能体
        await asyncio.gather(
            self.structured_agent.connect(),
            self.structural_agent.connect(),
            self.geometry_semantic_agent.connect()
        )
        
        print("✅ 多智能体系统初始化完成！")
        self.initialized = True
    
    def register_event_callback(self, callback: Callable):
        """注册事件回调函数"""
        self._event_callbacks.add(callback)
    
    def unregister_event_callback(self, callback: Callable):
        """取消注册事件回调函数"""
        if callback in self._event_callbacks:
            self._event_callbacks.remove(callback)
    
    async def _publish_event(self, event_type: str, message: str, data: Dict[str, Any] = None):
        """发布事件给所有注册的回调函数"""
        event_data = {
            "event_type": event_type,
            "message": message,
            "timestamp": time.time()
        }
        
        if data:
            event_data["data"] = data
        
        # 调用所有注册的回调函数
        for callback in list(self._event_callbacks):
            try:
                await callback(event_data)
            except Exception as e:
                print(f"回调函数执行出错: {e}")
    
    async def execute_multi_modal_query(
        self, 
        query_text: str, 
        shape_vector: Optional[List[float]] = None,
        top_k: int = 10
    ) -> QueryResult:
        """
        执行多模态查询
        
        Args:
            query_text: 用户查询文本
            shape_vector: 形状特征向量（可选）
            top_k: 最终返回结果数量
            
        Returns:
            QueryResult: 最终融合后的查询结果
        """
        # 确保系统已初始化
        if not self.initialized:
            await self.initialize()
            
        start_time = time.time()
        task_id = str(uuid.uuid4())
        
        try:
            # 第一步：生成查询计划
            await self._publish_event("query_start", "🧠 开始查询规划...")
            
            planner_task = QueryPlannerTask(
                task_id=str(uuid.uuid4()),
                query_text=query_text,
                shape_vector=shape_vector,
                top_k=top_k
            )
            
            planner_result = await self.query_planner.execute_task(planner_task)
            
            if planner_result.status != 'success':
                error_msg = f"查询规划失败: {planner_result.error_message}"
                await self._publish_event("error", error_msg)
                return QueryResult(
                    task_id=task_id,
                    status='failure',
                    error_message=error_msg,
                    execution_time=time.time() - start_time
                )
            
            # 解析查询计划
            query_plan = self.query_planner.parse_query_plan_from_result(planner_result)
            if not query_plan:
                error_msg = "无法解析查询计划"
                await self._publish_event("error", error_msg)
                return QueryResult(
                    task_id=task_id,
                    status='failure',
                    error_message=error_msg,
                    execution_time=time.time() - start_time
                )
            
            # 发送查询计划事件
            plan_message = f"📋 查询计划生成成功，包含 {len(query_plan.steps)} 个步骤"
            plan_data = {
                "intent_analysis": query_plan.intent_analysis,
                "steps": query_plan.steps,
                "fusion_strategy": query_plan.result_fusion
            }
            await self._publish_event("plan_created", plan_message, plan_data)
            
            # 输出查询计划详情
            print(f"📋 查询计划生成成功，包含 {len(query_plan.steps)} 个步骤")
            print(f"   意图分析: {query_plan.intent_analysis}")
            print(f"   查询计划详情:")
            for i, step in enumerate(query_plan.steps, 1):
                step_message = f"步骤{i}: {step['step_id']} -> {step['agent_type']}"
                await self._publish_event("plan_step", step_message, step)
                
                print(f"      步骤{i}: {step['step_id']} -> {step['agent_type']}")
                print(f"         参数: {step['task_params']}")
                if step.get('depends_on'):
                    print(f"         依赖: {step['depends_on']}")
                if step.get('use_previous_results'):
                    print(f"         使用前置结果: {step['use_previous_results']}")
            print(f"   融合策略: {query_plan.result_fusion}")
            print("\n")
            
            # 第二步：按计划执行查询步骤
            step_results = {}
            
            for step in query_plan.steps:
                step_id = step["step_id"]
                agent_type = step["agent_type"]
                task_params = step["task_params"]
                depends_on = step.get("depends_on")
                use_previous_results = step.get("use_previous_results", False)
                
                # 发送步骤开始事件
                step_start_msg = f"🔍 执行步骤 {step_id} - {agent_type}"
                await self._publish_event("step_start", step_start_msg, {
                    "step_id": step_id,
                    "agent_type": agent_type,
                    "task_params": task_params
                })
                
                print(f"🔍 执行步骤 {step_id} - {agent_type}")
                
                # 处理依赖关系
                id_list = None
                if use_previous_results and depends_on and depends_on in step_results:
                    previous_result = step_results[depends_on]
                    if previous_result.status == 'success' and previous_result.results:
                        id_list = [item.uuid for item in previous_result.results]
                        id_list_msg = f"使用前置步骤结果作为过滤条件，ID数量: {len(id_list)}"
                        await self._publish_event("step_info", id_list_msg, {"id_list": id_list})
                        print(f"   使用前置步骤结果作为过滤条件，ID数量: {len(id_list)}")
                
                # 创建具体的任务对象
                agent_task = await self._create_agent_task(
                    agent_type, task_params, shape_vector, id_list
                )
                
                # 执行查询
                agent = self.agents[agent_type]
                result = await agent.execute_task(agent_task)
                
                # 发送步骤完成事件
                step_complete_msg = f"步骤 {step_id} 完成，状态: {result.status}, 结果数: {result.total_results}"
                await self._publish_event("step_complete", step_complete_msg, {
                    "step_id": step_id,
                    "status": result.status,
                    "total_results": result.total_results,
                    "error_message": result.error_message
                })
                
                step_results[step_id] = result
                
                print(f"   步骤 {step_id} 完成，状态: {result.status}, 结果数: {result.total_results}")
                
                if result.status != 'success':
                    error_msg = f"步骤失败: {result.error_message}"
                    await self._publish_event("step_error", error_msg, {
                        "step_id": step_id,
                        "error_message": result.error_message
                    })
                    print(f"   ⚠️ 步骤失败: {result.error_message}")
            
            # 第三步：融合结果
            await self._publish_event("fusion_start", "🔄 开始结果融合...", {
                "fusion_strategy": query_plan.result_fusion
            })
            print(f"🔄 开始结果融合...")
            
            final_results = await self._fuse_results(step_results, query_plan.result_fusion, top_k)
            
            # 发送模型路径添加事件
            if final_results:
                await self._publish_event("model_paths", "获取模型路径...", {
                    "count": len(final_results)
                })
                
                # 发送结果融合完成事件
                fusion_complete_msg = f"多模态查询完成，返回 {len(final_results)} 个结果"
                await self._publish_event("fusion_complete", fusion_complete_msg, {
                    "result_count": len(final_results)
                })
            
            execution_time = time.time() - start_time            
            return QueryResult(
                task_id=task_id,
                status='success',
                results=final_results,
                execution_time=execution_time,
                total_results=len(final_results)
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e)
            await self._publish_event("error", f"❌ 查询失败: {error_msg}")
            return QueryResult(
                task_id=task_id,
                status='failure',
                error_message=error_msg,
                execution_time=execution_time
            )
    
    async def _create_agent_task(
        self, 
        agent_type: str, 
        task_params: Dict[str, Any], 
        shape_vector: Optional[List[float]],
        id_list: Optional[List[str]]
    ) -> BaseTask:
        """创建特定智能体的任务对象"""
        
        task_id = str(uuid.uuid4())
        
        # 复制参数以避免修改原始参数
        params = task_params.copy()
        
        # 添加id_list到参数中
        if id_list:
            params["id_list"] = id_list
        
        if agent_type == "structured_data":
            return UnifiedStructuredDataTask(
                task_id=task_id,
                **params
            )
        elif agent_type == "structural_relationship":
            return UnifiedStructuralQueryTask(
                task_id=task_id,
                **params
            )
        elif agent_type == "geometry_semantic":
            # 如果有形状向量，添加到参数中
            if shape_vector:
                params["shape_vector"] = shape_vector
                shape_vector_msg = f"添加形状向量到geometry_semantic任务，向量维度: {len(shape_vector)}"
                await self._publish_event("step_info", shape_vector_msg)
                print(f"   添加形状向量到geometry_semantic任务，向量维度: {len(shape_vector)}")
            
            # 移除可能存在的字符串占位符
            if "shape_vector" in params and isinstance(params["shape_vector"], str):
                if shape_vector:
                    params["shape_vector"] = shape_vector
                else:
                    del params["shape_vector"]
                    
            return UnifiedGeometrySemanticTask(
                task_id=task_id,
                **params
            )
        else:
            raise ValueError(f"不支持的智能体类型: {agent_type}")
    
    async def _fuse_results(
        self, 
        step_results: Dict[str, QueryResult], 
        fusion_config: Dict[str, Any],
        top_k: int
    ) -> List[SearchResultItem]:
        """融合多个步骤的查询结果"""
        
        strategy = fusion_config.get("strategy", "union")
        weights = fusion_config.get("weights", {})
        
        # 收集所有成功的结果
        all_results = []
        successful_results = {}
        
        for step_id, result in step_results.items():
            if result.status == 'success' and result.results:
                successful_results[step_id] = result.results
                all_results.extend(result.results)
        
        # 根据融合策略处理结果
        if strategy == "intersection" and len(successful_results) > 1:
            # 交集: 只保留在所有步骤中都出现的结果
            results_lists = list(successful_results.values())
            fused_results = self._intersect_results(results_lists, top_k)
            await self._publish_event("fusion_info", f"使用交集策略融合结果，共{len(fused_results)}项")
            return fused_results
            
        elif strategy == "weighted" and len(successful_results) > 1:
            # 加权融合
            fused_results = await self._weighted_fusion(successful_results, weights, top_k)
            await self._publish_event("fusion_info", f"使用加权策略融合结果，共{len(fused_results)}项")
            return fused_results
            
        else:
            # 默认使用并集
            fused_results = self._union_results(all_results, top_k)
            await self._publish_event("fusion_info", f"使用并集策略融合结果，共{len(fused_results)}项")
            return fused_results
    
    def _intersect_results(self, results_lists: List[List[SearchResultItem]], top_k: int) -> List[SearchResultItem]:
        """计算结果交集"""
        if not results_lists:
            return []
        
        # 以第一个结果列表为基准
        base_results = results_lists[0]
        base_uuids = {item.uuid for item in base_results}
        
        # 找出在所有列表中都出现的UUID
        common_uuids = base_uuids
        for results in results_lists[1:]:
            current_uuids = {item.uuid for item in results}
            common_uuids = common_uuids.intersection(current_uuids)
        
        # 构建交集结果
        intersection_results = []
        for item in base_results:
            if item.uuid in common_uuids:
                intersection_results.append(item)
        
        return intersection_results[:top_k]
    
    def _union_results(self, all_results: List[SearchResultItem], top_k: int) -> List[SearchResultItem]:
        """计算结果并集并去重"""
        seen_uuids = set()
        union_results = []
        
        for item in all_results:
            if item.uuid not in seen_uuids:
                seen_uuids.add(item.uuid)
                union_results.append(item)
        
        # 按相似度分数排序（如果有的话）
        union_results.sort(key=lambda x: x.similarity_score or 0, reverse=True)
        
        return union_results[:top_k]
    
    async def _weighted_fusion(
        self, 
        step_results: Dict[str, List[SearchResultItem]], 
        weights: Dict[str, float], 
        top_k: int
    ) -> List[SearchResultItem]:
        """加权融合多个步骤的查询结果"""
        
        # 如果没有提供权重，使用默认权重
        if not weights:
            weights = {step_id: 1.0 for step_id in step_results.keys()}
        
        # 对于没有在权重中指定的步骤，使用默认权重1.0
        for step_id in step_results.keys():
            if step_id not in weights:
                weights[step_id] = 1.0
        
        # 收集所有结果并计算加权分数
        all_items = {}
        
        for step_id, results in step_results.items():
            weight = weights.get(step_id, 1.0)
            
            for item in results:
                if item.uuid not in all_items:
                    # 第一次遇到该结果，初始化
                    all_items[item.uuid] = {
                        "item": item,
                        "weighted_score": item.similarity_score * weight,
                        "count": 1,
                        "sources": [step_id]
                    }
                else:
                    # 已存在该结果，累加权重分数
                    all_items[item.uuid]["weighted_score"] += item.similarity_score * weight
                    all_items[item.uuid]["count"] += 1
                    all_items[item.uuid]["sources"].append(step_id)
        
        # 计算最终分数并排序
        final_items = []
        for uuid, data in all_items.items():
            item = data["item"]
            # 使用加权平均作为最终分数
            item.similarity_score = data["weighted_score"] / data["count"]
            item.metadata = {
                "sources": data["sources"],
                "weights": [weights.get(s, 1.0) for s in data["sources"]]
            }
            final_items.append(item)
        
        # 按相似度分数降序排序
        final_items.sort(key=lambda x: x.similarity_score, reverse=True)
        
        # 限制返回数量
        return final_items[:top_k]
    
    async def disconnect(self):
        """断开所有智能体连接"""
        if self.initialized:
            print("🔌 断开所有智能体连接...")
            await asyncio.gather(
                self.structured_agent.disconnect(),
                self.structural_agent.disconnect(),
                self.geometry_semantic_agent.disconnect()
            )
            self.initialized = False
            print("👋 所有连接已断开")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.disconnect()
