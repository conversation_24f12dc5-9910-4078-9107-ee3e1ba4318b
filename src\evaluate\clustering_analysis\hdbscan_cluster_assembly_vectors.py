#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用 HDBSCAN 对装配体向量表示进行无监督聚类，并给出常见内部评估指标。

支持分析从extract_assembly_vectors.py生成的装配体向量表示文件。

用法示例：

# 基本聚类分析
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py \
    --embed dataset/assembly_representations.pkl

# 自定义聚类参数
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py \
    --embed dataset/assembly_representations.pkl \
    --min_cluster_size 10 --metric cosine --norm 1

# 使用 PCA 降维
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py \
    --embed dataset/assembly_representations.pkl \
    --pca 1 --pca-components 32

# 自定义输出文件名
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py \
    --embed dataset/assembly_representations.pkl \
    --out dataset/my_assembly_vector_labels.pkl

指标：
• silhouette_score 通常范围在 [-1, 1] 之间，越接近 1 表示类内相似度越高、类间区分度越好。
• Davies-Bouldin 越小越好。
• Calinski-Harabasz 越大越好。
"""
from __future__ import annotations

import argparse
import pickle
from collections import Counter
from pathlib import Path
from typing import Tuple

import numpy as np
from sklearn.preprocessing import normalize
from sklearn import metrics
from sklearn.decomposition import PCA
import psycopg2
from psycopg2.extras import RealDictCursor
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.config import Config

try:
    import hdbscan  # type: ignore
except ImportError as e:
    raise ImportError(
        "未安装 hdbscan。请先运行 `pip install hdbscan` 或 `conda install -c conda-forge hdbscan`"
    ) from e


def load_assembly_vector_representations(pkl_path: Path) -> Tuple[np.ndarray, list[str]]:
    """
    从 assembly_representations.pkl 文件中读取装配体向量表示
    
    Args:
        pkl_path: assembly_representations.pkl 文件路径
        
    Returns:
        features: 特征矩阵 (N, D)
        assembly_ids: 装配体ID列表
    """
    with pkl_path.open("rb") as f:
        data: dict = pickle.load(f)
    
    if not isinstance(data, dict):
        raise ValueError(f"数据格式错误：期望dict，但得到{type(data)}")
    
    if not data:
        raise ValueError("数据为空")
    
    # 提取装配体ID和向量
    assembly_ids = list(data.keys())
    vectors = list(data.values())
    
    # 转换为numpy数组
    features = np.array(vectors)
    
    print(f"加载了 {len(assembly_ids)} 个装配体的向量表示")
    print(f"向量维度: {features.shape[1]}")
    print(f"向量范围: [{features.min():.4f}, {features.max():.4f}]")
    print(f"向量均值: {features.mean():.4f}")
    print(f"向量标准差: {features.std():.4f}")
    
    # 检查零向量
    zero_vectors = np.sum(np.all(features == 0, axis=1))
    if zero_vectors > 0:
        print(f"警告: 发现 {zero_vectors} 个零向量 ({zero_vectors/len(vectors)*100:.1f}%)")
    
    return features, assembly_ids


def apply_pca(X: np.ndarray, n_components: int = 64) -> Tuple[np.ndarray, PCA]:
    """应用 PCA 降维"""
    # 确保降维后的维度不超过原始维度
    n_components = min(n_components, X.shape[1], X.shape[0])
    pca = PCA(n_components=n_components, random_state=42)
    X_reduced = pca.fit_transform(X)
    return X_reduced, pca


def run_hdbscan(
    X: np.ndarray,
    min_cluster_size: int = 5,
    min_samples: int | None = None,
    metric: str = "euclidean",
    **kwargs,
) -> hdbscan.HDBSCAN:
    """运行HDBSCAN聚类"""
    clusterer = hdbscan.HDBSCAN(
        min_cluster_size=min_cluster_size,
        min_samples=min_samples,
        metric=metric,
        cluster_selection_method="eom",
        **kwargs,
    )
    clusterer.fit(X)
    return clusterer


def eval_internal_metrics(X: np.ndarray, labels: np.ndarray) -> dict[str, float | str]:
    """计算 silhouette、DBI、CHI；若簇数不足 2，则返回 N/A"""
    # 过滤掉噪声标签 -1
    mask = labels != -1
    if mask.sum() < 2 or len(set(labels[mask])) < 2:
        return {"silhouette": "N/A", "dbi": "N/A", "chi": "N/A"}

    try:
        sil = metrics.silhouette_score(X[mask], labels[mask], metric="cosine")
    except Exception:
        sil = "err"
    try:
        dbi = metrics.davies_bouldin_score(X[mask], labels[mask])
    except Exception:
        dbi = "err"
    try:
        chi = metrics.calinski_harabasz_score(X[mask], labels[mask])
    except Exception:
        chi = "err"
    return {"silhouette": sil, "dbi": dbi, "chi": chi}


def get_assembly_additional_info_from_db(assembly_ids):
    """从PostgreSQL数据库中获取装配体的额外信息"""
    try:
        # 连接数据库
        conn = psycopg2.connect(**Config.POSTGRES_CONFIG)
        
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询装配体的基本信息
            placeholders = ','.join(['%s'] * len(assembly_ids))
            query = f"""
                SELECT uuid, name, description, length, width, height, 
                       volume, area, part_count, mass, density
                FROM cad_rag.assemblies 
                WHERE uuid IN ({placeholders})
            """
            cursor.execute(query, assembly_ids)
            results = cursor.fetchall()
            
            # 构建字典映射
            assembly_info = {row['uuid']: row for row in results}
            
            print(f"从数据库获取了 {len(assembly_info)} 个装配体的详细信息")
            return assembly_info
            
    except Exception as e:
        print(f"从数据库获取装配体信息失败: {e}")
        return {}
    finally:
        if 'conn' in locals():
            conn.close()


def analyze_clustering_results(labels: np.ndarray, assembly_ids: list[str]):
    """分析聚类结果"""
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    noise_ratio = (labels == -1).mean()
    
    print(f"\n=== 聚类结果分析 ===")
    print(f"总装配体数量: {len(assembly_ids)}")
    print(f"簇数: {n_clusters}")
    print(f"噪声点数量: {(labels == -1).sum()}")
    print(f"噪声比例: {noise_ratio:.2%}")
    
    # 簇大小分布
    cnt = Counter(labels)
    if -1 in cnt:
        noise_count = cnt.pop(-1)
        print(f"噪声点: {noise_count}")
    
    if cnt:
        print("\n簇大小分布:")
        for cluster_id, size in cnt.most_common(10):
            print(f"  簇 {cluster_id}: {size} 个装配体")
        
        # 统计信息
        cluster_sizes = list(cnt.values())
        print(f"\n簇大小统计:")
        print(f"  最大簇: {max(cluster_sizes)}")
        print(f"  最小簇: {min(cluster_sizes)}")
        print(f"  平均簇大小: {np.mean(cluster_sizes):.1f}")
        print(f"  簇大小标准差: {np.std(cluster_sizes):.1f}")


def find_similar_assemblies_in_cluster(labels: np.ndarray, assembly_ids: list[str], 
                                     features: np.ndarray, target_assembly_id: str, 
                                     top_k: int = 5):
    """在同一簇中找到与目标装配体最相似的装配体"""
    if target_assembly_id not in assembly_ids:
        print(f"目标装配体 {target_assembly_id} 不在数据中")
        return
    
    target_idx = assembly_ids.index(target_assembly_id)
    target_cluster = labels[target_idx]
    
    if target_cluster == -1:
        print(f"装配体 {target_assembly_id} 被分类为噪声点")
        return
    
    # 找到同一簇中的所有装配体
    cluster_mask = labels == target_cluster
    cluster_indices = np.where(cluster_mask)[0]
    cluster_assembly_ids = [assembly_ids[i] for i in cluster_indices]
    cluster_features = features[cluster_indices]
    
    print(f"\n装配体 {target_assembly_id} 在簇 {target_cluster} 中")
    print(f"该簇包含 {len(cluster_assembly_ids)} 个装配体")
    
    # 计算与目标装配体的相似度
    target_feature = features[target_idx]
    similarities = []
    
    for i, idx in enumerate(cluster_indices):
        if idx != target_idx:
            similarity = np.dot(target_feature, features[idx]) / (
                np.linalg.norm(target_feature) * np.linalg.norm(features[idx])
            )
            similarities.append((assembly_ids[idx], similarity))
    
    # 按相似度排序
    similarities.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n与 {target_assembly_id} 最相似的装配体（同一簇内）:")
    for i, (aid, sim) in enumerate(similarities[:top_k], 1):
        print(f"  {i}. {aid}: {sim:.4f}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="使用 HDBSCAN 对装配体向量表示进行聚类")
    parser.add_argument("--embed", default="dataset/assembly_representations.pkl", 
                       help="装配体向量表示 pickle 文件路径")
    parser.add_argument("--out", default="dataset/hdbscan_assembly_vector_labels.pkl", 
                       help="输出标签 pickle 文件路径")
    parser.add_argument("--min_cluster_size", type=int, default=5, 
                       help="HDBSCAN 的 min_cluster_size")
    parser.add_argument("--min_samples", type=int, default=None, 
                       help="HDBSCAN 的 min_samples")
    parser.add_argument("--metric", default="euclidean", 
                       help="距离度量，默认 euclidean，可选 cosine")
    parser.add_argument("--norm", type=int, choices=[0, 1], default=1, 
                       help="是否做 L2 归一化 (1/0)")
    parser.add_argument("--pca", type=int, choices=[0, 1], default=0, 
                       help="是否使用 PCA 降维 (1/0)")
    parser.add_argument("--pca-components", type=int, default=64, 
                       help="PCA 降维后的维度")
    parser.add_argument("--analyze", action='store_true', 
                       help="进行详细的聚类结果分析")
    parser.add_argument("--find-similar", 
                       help="查找与指定装配体ID相似的装配体")
    
    args = parser.parse_args()

    embed_path = Path(args.embed)
    if not embed_path.exists():
        raise FileNotFoundError(f"未找到装配体向量文件: {embed_path}")

    # 载入装配体向量表示
    print("加载装配体向量表示...")
    X, assembly_ids = load_assembly_vector_representations(embed_path)
    
    # 从数据库获取装配体额外信息
    print("\n从数据库获取装配体详细信息...")
    assembly_info = get_assembly_additional_info_from_db(assembly_ids)

    # 可选归一化
    if args.norm:
        X = normalize(X)
        print("已做 L2 归一化")

    # 可选 PCA 降维
    pca_model = None
    if args.pca:
        original_dim = X.shape[1]
        X, pca_model = apply_pca(X, n_components=args.pca_components)
        print(f"已使用 PCA 从 {original_dim} 维降到 {X.shape[1]} 维，保留方差比例: {pca_model.explained_variance_ratio_.sum():.4f}")

    # 聚类
    print("\n开始装配体向量 HDBSCAN 聚类...")
    clusterer = run_hdbscan(
        X,
        min_cluster_size=args.min_cluster_size,
        min_samples=args.min_samples,
        metric=args.metric,
    )
    labels = clusterer.labels_

    # 基本聚类结果
    analyze_clustering_results(labels, assembly_ids)

    # 评估指标
    print("\n=== 聚类质量评估 ===")
    metrics_dict = eval_internal_metrics(X, labels)
    for k, v in metrics_dict.items():
        print(f"{k}: {v}")

    # 详细分析
    if args.analyze:
        # 显示每个簇的一些样本
        cnt = Counter(labels)
        if -1 in cnt:
            cnt.pop(-1)
        
        print(f"\n=== 簇样本展示 ===")
        for cluster_id, size in list(cnt.most_common(5)):
            cluster_mask = labels == cluster_id
            cluster_assembly_ids = [assembly_ids[i] for i, mask in enumerate(cluster_mask) if mask]
            print(f"\n簇 {cluster_id} (大小: {size}):")
            for i, aid in enumerate(cluster_assembly_ids[:5]):
                print(f"  {aid}")
            if size > 5:
                print(f"  ... 还有 {size-5} 个装配体")

    # 查找相似装配体
    if args.find_similar:
        find_similar_assemblies_in_cluster(labels, assembly_ids, X, args.find_similar)

    # 保存结果
    out_path = Path(args.out)
    out_path.parent.mkdir(parents=True, exist_ok=True)
    
    with out_path.open("wb") as f:
        pickle.dump({
            "labels": labels,
            "assembly_ids": assembly_ids,
            "assembly_info": assembly_info,
            "feature_type": "assembly_vectors",
            "params": {
                "min_cluster_size": args.min_cluster_size,
                "min_samples": args.min_samples,
                "metric": args.metric,
                "norm": bool(args.norm),
                "pca": bool(args.pca),
                "pca_components": args.pca_components,
                "input_file": str(embed_path),
            },
            "cluster_persistence": getattr(clusterer, "cluster_persistence_", None),
            "pca_model": pca_model,
            "metrics": metrics_dict,
        }, f)
    
    print(f"\n聚类结果已保存到: {out_path}")
    print(f"包含 {len(set(labels)) - (1 if -1 in labels else 0)} 个簇")


if __name__ == "__main__":
    main()
