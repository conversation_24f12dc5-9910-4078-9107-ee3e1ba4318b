# 统一智能体接口使用指南

## 概述

三个查询智能体现在都使用统一的 `execute_task` 函数作为查询入口，并返回标准化的 `QueryResult` 格式。

## 智能体类型

### 1. 结构化数据查询智能体 (StructuredDataAgent)

**功能**: 将自然语言查询转换为 SQL 语句，在 PostgreSQL 数据库中查询并返回结果。

**输入任务模型**: `UnifiedStructuredDataTask`

- `query_text`: 自然语言查询文本
- `id_list`: 可选的 ID 列表，用于缩小查询范围
- `top_k`: 返回结果数量（默认 50）

**使用示例**:

```python
from src.agent.structured_data_agent import StructuredDataAgent
from src.agent.data_models import UnifiedStructuredDataTask

agent = StructuredDataAgent()

# 创建查询任务
task = UnifiedStructuredDataTask(
    task_id="unique_task_id",
    query_text="查找质量大于100克的钢制零件",
    id_list=["part_001", "part_002"],  # 可选
    top_k=20
)

# 执行查询
result = await agent.execute_task(task)
```

### 2. 结构关系查询智能体 (StructuralRelationshipAgent)

**功能**: 将自然语言查询转换为 Cypher 语句，在 Neo4j 图数据库中查询装配层次结构和连接关系。

**输入任务模型**: `UnifiedStructuralQueryTask`

- `query_text`: 自然语言查询文本，描述装配层次结构和连接关系
- `id_list`: 可选的 ID 列表，用于缩小查询范围
- `top_k`: 返回结果数量（默认 50）

**使用示例**:

```python
from src.agent.structural_relationship_agent import StructuralRelationshipAgent
from src.agent.data_models import UnifiedStructuralQueryTask

agent = StructuralRelationshipAgent()

# 创建查询任务
task = UnifiedStructuralQueryTask(
    task_id="unique_task_id",
    query_text="查找包含轴承的装配体",
    id_list=["assembly_001", "assembly_002"],  # 可选
    top_k=15
)

# 执行查询
result = await agent.execute_task(task)
```

### 3. 几何和语义查询智能体 (GeometrySemanticAgent)

**功能**: 使用 Milvus 向量数据库进行相似度搜索，支持文本语义搜索、形状几何搜索和混合搜索。

**输入任务模型**: `UnifiedGeometrySemanticTask`

- `query_text`: 自然语言查询文本（可选）
- `shape_vector`: 形状特征向量（可选）
- `id_list`: 可选的 ID 列表，用于缩小查询范围
- `top_k`: 返回结果数量（默认 5）
- `shape_weight`: 形状搜索权重（默认 0.3）
- `use_reranker`: 是否使用重排序器（默认 True）

**注意**: `query_text` 和 `shape_vector` 不可同时为空。

**使用示例**:

```python
from src.agent.geometry_semantic_agent import GeometrySemanticAgent
from src.agent.data_models import UnifiedGeometrySemanticTask

agent = GeometrySemanticAgent()

# 纯文本语义搜索
task1 = UnifiedGeometrySemanticTask(
    task_id="unique_task_id_1",
    query_text="圆形零件",
    top_k=10
)

# 纯形状几何搜索
task2 = UnifiedGeometrySemanticTask(
    task_id="unique_task_id_2",
    shape_vector=[0.1] * 768,  # 768维向量
    top_k=10
)

# 混合搜索
task3 = UnifiedGeometrySemanticTask(
    task_id="unique_task_id_3",
    query_text="圆形零件",
    shape_vector=[0.1] * 768,
    shape_weight=0.4,
    top_k=10
)

# 执行查询
result1 = await agent.execute_task(task1)
result2 = await agent.execute_task(task2)
result3 = await agent.execute_task(task3)
```

## 统一的查询结果格式

所有智能体都返回 `QueryResult` 对象，包含以下字段：

```python
class QueryResult:
    task_id: str                    # 任务ID
    status: str                     # 'success' 或 'failure'
    error_message: str | None       # 错误信息（如果有）
    results: List[SearchResultItem] # 标准化的搜索结果列表
    execution_time: float | None    # 查询执行时间（秒）
    total_results: int              # 结果总数
```

每个搜索结果项 (`SearchResultItem`) 包含：

```python
class SearchResultItem:
    rank: int                       # 结果排名
    uuid: str                       # 项目ID
    name: str                       # 项目名称
    description: str                # 项目描述
    similarity_score: float | None  # 相似度分数（适用于向量搜索）
    search_type: str                # 搜索类型：geometry, semantic, hybrid, structured, structural
    metadata: Dict[str, Any]        # 额外的元数据
```

## 完整使用示例

```python
import asyncio
import uuid
from src.agent.structured_data_agent import StructuredDataAgent
from src.agent.data_models import UnifiedStructuredDataTask

async def example_usage():
    # 创建智能体实例
    agent = StructuredDataAgent()

    try:
        # 创建查询任务
        task = UnifiedStructuredDataTask(
            task_id=str(uuid.uuid4()),
            query_text="查找铝制零件，质量在50到200克之间",
            top_k=25
        )

        # 执行查询
        result = await agent.execute_task(task)

        # 处理结果
        if result.status == 'success':
            print(f"查询成功，找到 {result.total_results} 个结果")
            print(f"执行时间: {result.execution_time:.2f} 秒")

            for item in result.results[:5]:  # 显示前5个结果
                print(f"- {item.name} (ID: {item.uuid})")
                print(f"  搜索类型: {item.search_type}")
                if item.similarity_score:
                    print(f"  相似度: {item.similarity_score:.3f}")
        else:
            print(f"查询失败: {result.error_message}")

    finally:
        # 断开连接
        await agent.disconnect()

# 运行示例
asyncio.run(example_usage())
```

## 主要改进

1. **统一接口**: 所有智能体都使用 `execute_task` 函数
2. **标准化输入**: 使用统一的任务模型，包含自然语言查询和可选 ID 列表
3. **标准化输出**: 所有智能体返回相同格式的 `QueryResult`
4. **智能 ID 过滤**: LLM 直接生成包含 ID 过滤条件的完整查询语句，避免后期拼接导致的语法错误
5. **执行时间跟踪**: 自动记录查询执行时间
6. **错误处理**: 统一的错误处理和状态报告
7. **代码简化**: 删除了冗余的方法，保持核心功能

### ID 过滤机制

当提供`id_list`参数时，系统会：

- **结构化数据智能体**: 在提示中告诉 LLM 添加 `uuid IN ('id1', 'id2', ...)` 条件
- **结构关系智能体**: 在提示中告诉 LLM 添加 `n.id IN ['id1', 'id2', ...]` 条件
- **几何语义智能体**: 在搜索结果中进行后过滤

这种方法让 LLM 生成语法正确的完整查询语句，避免了手动拼接 SQL/Cypher 时可能出现的语法错误。

## 测试

运行测试脚本验证统一接口：

```bash
python test_unified_agents.py
```

这将测试所有三个智能体的 `execute_task` 函数，包括 ID 过滤功能。
