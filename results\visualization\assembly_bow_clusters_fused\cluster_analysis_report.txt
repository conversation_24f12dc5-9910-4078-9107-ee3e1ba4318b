装配体聚类分析报告
==================================================

簇 0:
  装配体数量: 123
  平均零件数: 9.4 ± 12.1
  零件数范围: 3 - 82
  主要零件类型: [np.int64(209), np.int64(190), np.int64(138), np.int64(68), np.int64(237)]
  装配体示例: ['34675_769b3cf9', '36853_7efccb8c', '23378_8d6426ae']
  熵值: 3.739

簇 1:
  装配体数量: 7
  平均零件数: 8.9 ± 5.7
  零件数范围: 3 - 19
  主要零件类型: [np.int64(320), np.int64(222), np.int64(171), np.int64(262), np.int64(272)]
  装配体示例: ['31645_11901b20', '120130_796ac4ae', '23121_cbdf3ebc']
  熵值: 1.538

