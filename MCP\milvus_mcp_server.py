#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Milvus装配体检索MCP服务

使用fastmcp库实现的模型上下文协议(MCP)服务器，
提供Milvus向量数据库中装配体的检索功能。
"""

import logging
import sys
import os
from pathlib import Path
from typing import Any, Optional, List, Dict
import json
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastmcp import FastMCP
from pydantic import Field

# 导入项目模块
from src.utils.database.milvus_utils import MilvusManager
from src.config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 初始化MCP服务器
mcp = FastMCP("Milvus Assembly Search")

# 全局Milvus管理器实例 - 直接初始化
logger.info("正在初始化Milvus管理器...")
try:
    import torch
    device = "cuda" if torch.cuda.is_available() else "cpu"
except ImportError:
    device = "cpu"

logger.info(f"使用设备: {device}")
milvus_manager = MilvusManager(use_reranker=True, device=device)
logger.info("Milvus管理器初始化完成")


@mcp.tool()
def search_assemblies_by_text(
    query_text: str = Field(description="搜索查询文本，支持自然语言描述，例如：'圆柱形零件'、'齿轮装配体'等"),
    top_k: int = Field(default=5, description="返回结果数量，控制搜索返回的装配体数量", ge=1, le=100),
    use_reranker: bool = Field(default=True, description="是否使用重排序器。启用后会使用更先进的模型对初步搜索结果进行重新排序")
) -> Dict[str, Any]:
    """
    基于文本描述搜索装配体
    
    此工具接受自然语言描述作为输入，在装配体数据库中进行语义搜索，
    返回最相关的装配体结果。支持使用重排序器提高搜索精度。
      Returns:
        Dict[str, Any]: 搜索结果字典，包含以下字段：
            - results (List[Dict]): 搜索结果列表，每个结果包含：
                - rank (int): 结果排名（1开始）
                - assembly_id (str): 装配体唯一标识符
                - description (str): 装配体描述文本
                - score (float): 相似度评分（0-1之间，越高越相似）
            - message (str, 仅错误时): 错误信息描述
    
    """
    try:
        logger.info(f"开始文本搜索: {query_text}")
        
        # 使用全局Milvus管理器
        global milvus_manager
        
        # 设置重排序器使用状态
        milvus_manager.use_reranker = use_reranker
        
        # 执行搜索
        results = milvus_manager.search_assemblies_by_text(
            collection_name="assembly_collection",
            query_text=query_text,
            top_k=top_k
        )
        
        # 格式化返回结果 - 新的字典格式
        formatted_results = []
        for i, result in enumerate(results):
            formatted_results.append({
                "rank": i + 1,
                "assembly_id": result["id"],
                "description": result["description"],
                "score": float(result["score"])
            })
        
        response = {
            "status": "success",
            "query": query_text,
            "top_k": top_k,
            "use_reranker": use_reranker,
            "total_results": len(formatted_results),
            "results": formatted_results
        }
        
        logger.info(f"文本搜索完成，返回 {len(formatted_results)} 个结果")
        return response
        
    except Exception as e:
        error_msg = f"文本搜索失败: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": error_msg,
            "query": query_text
        }


@mcp.tool()
def search_assemblies_by_shape(
    query_vector: List[float] = Field(description="查询形状向量，必须是768维的浮点数列表。通常通过CLIP或其他深度学习模型从3D模型中提取"),
    top_k: int = Field(default=5, description="返回结果数量，控制搜索返回的相似装配体数量", ge=1, le=100)
) -> Dict[str, Any]:
    """
    基于形状向量搜索装配体
    
    此工具使用预先提取的形状特征向量进行搜索，能够找到几何形状相似的装配体。
    形状向量通常通过3D模型的几何特征提取获得。
          Returns:
        Dict[str, Any]: 搜索结果字典，包含以下字段：
            - results (List[Dict]): 搜索结果列表，每个结果包含：
                - rank (int): 结果排名（1开始）
                - assembly_id (str): 装配体唯一标识符
                - description (str): 装配体描述文本
                - score (float): 形状相似度评分（0-1之间，越高越相似）
            - message (str, 仅错误时): 错误信息描述
    """
    try:
        logger.info(f"开始形状搜索，向量维度: {len(query_vector)}")
        
        # 验证向量维度
        if len(query_vector) != 768:
            raise ValueError(f"形状向量维度应为768，但收到 {len(query_vector)}")
        
        # 使用全局Milvus管理器
        global milvus_manager
        
        # 执行搜索
        results = milvus_manager.search_assemblies_by_shape(
            collection_name="assembly_collection",
            query_vector=query_vector,
            top_k=top_k
        )
        
        # 格式化返回结果 - 新的字典格式
        formatted_results = []
        for i, result in enumerate(results):
            formatted_results.append({
                "rank": i + 1,
                "assembly_id": result["id"],
                "description": result["description"],
                "score": float(result["score"])
            })
        
        response = {
            "status": "success",
            "top_k": top_k,
            "vector_dimension": len(query_vector),
            "total_results": len(formatted_results),
            "results": formatted_results
        }
        
        logger.info(f"形状搜索完成，返回 {len(formatted_results)} 个结果")
        return response
        
    except Exception as e:
        error_msg = f"形状搜索失败: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": error_msg,
            "vector_dimension": len(query_vector) if query_vector else 0
        }


@mcp.tool()
def search_assemblies_hybrid(
    query_text: str = Field(description="搜索查询文本，支持自然语言描述装配体的功能或特征"),
    query_vector: Optional[List[float]] = Field(default=None, description="查询形状向量（可选），768维浮点数列表。如果不提供，则仅执行文本搜索"),
    top_k: int = Field(default=5, description="返回结果数量，控制搜索返回的装配体数量", ge=1, le=100),
    shape_weight: float = Field(default=0.3, description="形状搜索权重，范围0-1。0表示仅文本搜索，1表示仅形状搜索，0.3表示文本权重0.7+形状权重0.3", ge=0.0, le=1.0)
) -> Dict[str, Any]:
    """
    混合搜索装配体（文本+形状）
    
    此工具结合了文本语义搜索和形状相似性搜索的优势，通过加权融合两种搜索结果，
    提供更准确和全面的装配体检索功能。特别适用于需要同时考虑功能描述和几何形状的场景。
      Returns:
        Dict[str, Any]: 搜索结果字典，包含以下字段：
            - results (List[Dict]): 搜索结果列表，每个结果包含：
                - rank (int): 结果排名（1开始）
                - assembly_id (str): 装配体唯一标识符
                - description (str): 装配体描述文本
                - score (float): 综合相似度评分（0-1之间，融合文本和形状相似度）
            - message (str, 仅错误时): 错误信息描述
    
    使用场景:
        - 当您有装配体的文本描述和形状信息时，使用混合搜索获得最佳结果
        - 调整shape_weight来平衡文本和形状的重要性
        - 如果只有文本描述，将query_vector设为null或使用文本搜索工具
    """
    try:
        logger.info(f"开始混合搜索: {query_text}")
        
        # 使用全局Milvus管理器
        global milvus_manager
        
        # 验证形状向量维度（如果提供）
        if query_vector and len(query_vector) != 768:
            raise ValueError(f"形状向量维度应为768，但收到 {len(query_vector)}")
        
        # 执行混合搜索
        results = milvus_manager.search_assemblies_hybrid_with_shape(
            collection_name="assembly_collection",
            query_text=query_text,
            query_shape_vector=query_vector,
            top_k=top_k,
            shape_weight=shape_weight
        )
        
        # 格式化返回结果 - 新的字典格式
        formatted_results = []
        for i, result in enumerate(results):
            formatted_results.append({
                "rank": i + 1,
                "assembly_id": result["id"],
                "description": result["description"],
                "score": float(result["score"])
            })
        
        response = {
            "status": "success",
            "query": query_text,
            "top_k": top_k,
            "shape_weight": shape_weight,
            "has_shape_vector": query_vector is not None,
            "vector_dimension": len(query_vector) if query_vector else 0,
            "total_results": len(formatted_results),
            "results": formatted_results
        }
        
        logger.info(f"混合搜索完成，返回 {len(formatted_results)} 个结果")
        return response
        
    except Exception as e:
        error_msg = f"混合搜索失败: {str(e)}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": error_msg,
            "query": query_text
        }


def main():
    """主函数，启动MCP服务器"""
    try:
        logger.info("启动Milvus装配体检索MCP服务器...")
        
        # 显示配置信息
        Config.print_config()
        
        # Milvus管理器已在模块加载时初始化
        logger.info("Milvus管理器已就绪")
        
        # 运行MCP服务器
        mcp.run(transport="sse", host="0.0.0.0", port=8001)
        
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器运行出错: {e}")
        sys.exit(1)
    finally:
        # 清理资源
        logger.info("清理Milvus管理器资源...")
        # 注意：这里可以添加更多清理逻辑


if __name__ == "__main__":
    main()
