# 多模态查询系统详细指南

## 概述

本系统支持**图片**和**文字**两种输入模态，能够智能识别查询意图并选择最适合的检索策略。系统现在支持Assembly和Part节点的`description`和`description_embedding`属性，实现了更丰富的语义查询能力。

## 输入模态类型

### 1. 图片输入模态

**特点**: 直接使用图片的视觉特征进行形状相似性检索

**处理流程**:
```
图片 → 特征提取 → shape_embedding → 向量相似性搜索
```

**查询类型**: `vector_similarity`

**使用的嵌入**: `shape_embedding`

**示例**:
```python
image_query = MultimodalQuery(
    text_query="查找相似形状的零件",  # 可选描述
    image_data=image_embedding_vector,  # 图片特征向量
    query_intent="基于图片查找形状相似的零件"
)
```

### 2. 文字输入模态

**特点**: 根据文本内容智能识别查询意图，支持多种查询类型

#### 2.1 元数据查询 (metadata)

**触发条件**: 文本中包含属性相关关键词

**处理流程**:
```
文本 → 属性提取 → 精确/模糊匹配 → 结果返回
```

**支持的属性提取**:
- **材料**: 铝、钢、塑料、钛、碳纤维等
- **类别**: 机械、汽车、航空、电子等
- **行业**: 航空、汽车、机械等
- **名称**: 发动机、轮子、螺栓、齿轮等

**示例**:
```python
metadata_query = MultimodalQuery(
    text_query="查找铝合金材料的汽车零件",
    query_intent="基于材料和类别查找零件"
)
```

#### 2.2 结构查询 (structure)

**触发条件**: 文本中包含结构关系关键词

**关键词**: 包含、组成、结构、关系、子装配、零件、特征、由、构成

**处理流程**:
```
文本 → 关系识别 → 图遍历查询 → 结构结果
```

**示例**:
```python
structure_query = MultimodalQuery(
    text_query="查找包含螺栓特征的装配体结构",
    query_intent="基于结构关系查找装配体"
)
```

#### 2.3 语义相似性查询 (vector_similarity)

**触发条件**: 文本中包含语义相关关键词

**关键词**: 相似、类似、像、功能、用途、描述

**处理流程**:
```
文本 → 文本嵌入 → description_embedding → 语义相似性搜索
```

**使用的嵌入**: `description_embedding`

**示例**:
```python
semantic_query = MultimodalQuery(
    text_query="查找功能类似发动机的装配体",
    query_intent="基于功能描述查找相似装配体"
)
```

#### 2.4 混合查询 (hybrid)

**触发条件**: 文本包含多种查询类型的特征

**执行策略**:
1. **并行执行**: 同时执行多种查询类型，最后融合结果
2. **顺序执行**: 按优先级顺序执行，后续查询使用前序结果过滤

**过滤链机制**:
```
元数据查询 → 结果集A → 结构查询(过滤A) → 结果集B → 语义查询(过滤B) → 最终结果
```

**示例**:
```python
hybrid_query = MultimodalQuery(
    text_query="查找钢材制造的发动机装配体结构",
    filters={"material": "steel"},
    query_intent="先按材料过滤，再查找发动机相关结构"
)
```

## 编排智能体的查询分解

### 智能分解流程

1. **输入模态识别**
   - 检测是否包含图片数据
   - 分析文本内容特征

2. **查询意图理解**
   - 使用LLM分析查询意图
   - 提取关键属性和关系

3. **策略规划**
   - 确定查询类型组合
   - 设计执行顺序和依赖关系
   - 决定是否需要融合

### 分解结果格式

```json
{
  "intent": "查询意图描述",
  "input_modality": "图像/文字",
  "sub_queries": [
    {
      "type": "metadata|structure|vector_similarity",
      "description": "子查询描述",
      "target_entities": ["Assembly", "Part"],
      "required_attributes": ["material", "category"],
      "priority": 1,
      "depends_on": [],
      "embedding_type": "shape_embedding|description_embedding",
      "cypher_hints": "Cypher查询提示"
    }
  ],
  "execution_strategy": {
    "parallel": true,
    "fusion_required": true,
    "filter_chain": false
  }
}
```

## 多模态查询智能体的Cypher生成

### 1. 元数据查询的Cypher

**基础模板**:
```cypher
MATCH (n)
WHERE (n:Assembly OR n:Part) AND n.material = $material
RETURN n, labels(n) as types
LIMIT 50
```

**过滤链支持**:
```cypher
MATCH (n)
WHERE (n:Assembly OR n:Part) AND n.material = $material 
  AND n.id IN $previous_ids
RETURN n, labels(n) as types
```

### 2. 结构查询的Cypher

**装配体结构查询**:
```cypher
MATCH (a:Assembly)
OPTIONAL MATCH (a)-[:hasSubAssembly]->(sub:SubAssembly)
OPTIONAL MATCH (a)-[:hasPart]->(p:Part)
OPTIONAL MATCH (p)-[:hasFeature]->(f:Feature)
RETURN a, collect(distinct sub) as subassemblies, 
       collect(distinct p) as parts, collect(distinct f) as features
```

### 3. 向量相似性查询的Cypher

**形状相似性查询**:
```cypher
CALL db.index.vector.queryNodes(
    'shape_embedding_index',
    $top_k,
    $query_vector
) YIELD node AS n, score
WHERE (n:Assembly OR n:Part) AND score >= $similarity_threshold
RETURN n.id, n.name, n.description, score
ORDER BY score DESC
```

**语义相似性查询**:
```cypher
MATCH (n)
WHERE (n:Assembly OR n:Part) AND n.description_embedding IS NOT NULL
WITH n, gds.similarity.cosine($text_embedding, n.description_embedding) AS similarity
WHERE similarity >= $similarity_threshold
RETURN n.id, n.name, n.description, similarity
ORDER BY similarity DESC
```

## 使用示例

### 基本使用

```python
from src.agent import MultiAgentSystem, MultimodalQuery

# 创建系统
mas = MultiAgentSystem()
await mas.start()

# 图片查询
image_query = MultimodalQuery(
    image_data=image_embedding,
    query_intent="查找相似形状"
)

# 文字查询
text_query = MultimodalQuery(
    text_query="查找铝合金发动机零件",
    query_intent="材料和功能查询"
)

# 执行查询
result = await mas.process_multimodal_query_advanced(text_query)
```

### 高级使用

```python
# 自定义策略
strategy = RetrievalStrategy(
    query_types=[QueryType.METADATA, QueryType.VECTOR_SIMILARITY],
    parallel_execution=False,  # 顺序执行
    fusion_required=True
)

result = await mas.process_multimodal_query_advanced(query, strategy)
```

## 性能优化建议

1. **索引优化**
   - 确保shape_embedding和description_embedding有向量索引
   - 为常用属性创建普通索引

2. **查询优化**
   - 使用适当的相似度阈值
   - 限制结果数量避免过载

3. **缓存策略**
   - 缓存常用的文本嵌入
   - 复用查询结果

## 扩展开发

### 添加新的查询类型

1. 在QueryType枚举中添加新类型
2. 在编排智能体中添加识别逻辑
3. 在查询智能体中实现执行器
4. 更新Cypher查询模板

### 自定义属性提取

```python
def custom_attribute_extractor(text):
    # 实现自定义属性提取逻辑
    return extracted_attributes
```

这个增强的多模态查询系统为CAD知识图谱提供了强大而灵活的查询能力，支持从简单的属性查找到复杂的语义相似性搜索。
