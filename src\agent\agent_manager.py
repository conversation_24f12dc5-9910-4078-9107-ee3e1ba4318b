"""
智能体管理器 (Agent Manager)
协调和管理三个专业检索智能体的工作
"""

import logging
import asyncio
import uuid
from typing import List, Dict, Any, Optional, Union
from src.agent.geometry_semantic_agent import GeometrySemanticAgent
from src.agent.structured_data_agent import StructuredDataAgent
from src.agent.structural_relationship_agent import StructuralRelationshipAgent
from src.agent.data_models import (
    BaseTask, QueryResult, EnhancedQueryResult, SearchResultItem,
    TextSearchTask, ShapeSearchTask, HybridSearchTask,
    Text2SQLTask, Text2CypherTask, PropertyFilterTask,
    MultiModalQueryTask
)

logger = logging.getLogger(__name__)


class AgentManager:
    """
    智能体管理器
    
    负责协调三个专业检索智能体的工作：
    1. GeometrySemanticAgent: 几何和语义查询智能体
    2. StructuredDataAgent: 结构化数据查询智能体  
    3. StructuralRelationshipAgent: 结构关系智能体
    """
    
    def __init__(self):
        self.geometry_semantic_agent = GeometrySemanticAgent()
        self.structured_data_agent = StructuredDataAgent()
        self.structural_relationship_agent = StructuralRelationshipAgent()
        self.connected = False
        
    async def connect_all(self):
        """连接所有智能体"""
        try:
            logger.info("正在连接所有智能体...")
            
            # 并行连接所有智能体
            await asyncio.gather(
                self.geometry_semantic_agent.connect(),
                self.structured_data_agent.connect(),
                self.structural_relationship_agent.connect()
            )
            
            self.connected = True
            logger.info("所有智能体连接成功")
            
        except Exception as e:
            logger.error(f"连接智能体失败: {e}")
            raise
    
    async def disconnect_all(self):
        """断开所有智能体连接"""
        try:
            await asyncio.gather(
                self.geometry_semantic_agent.disconnect(),
                self.structured_data_agent.disconnect(),
                self.structural_relationship_agent.disconnect()
            )
            
            self.connected = False
            logger.info("所有智能体已断开连接")
            
        except Exception as e:
            logger.error(f"断开连接失败: {e}")
    
    async def execute_text_search(
        self, 
        query_text: str, 
        top_k: int = 5, 
        use_reranker: bool = True
    ) -> EnhancedQueryResult:
        """
        执行文本语义搜索
        
        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            use_reranker: 是否使用重排序器
            
        Returns:
            增强的查询结果
        """
        try:
            if not self.connected:
                await self.connect_all()
            
            # 执行文本搜索
            results = await self.geometry_semantic_agent.search_by_text(
                query_text, top_k, use_reranker
            )
            
            # 转换为标准格式
            search_items = []
            for result in results:
                search_items.append(SearchResultItem(
                    rank=result["rank"],
                    uuid=result["assembly_id"],
                    description=result["description"],
                    similarity_score=result["similarity_score"],
                    search_type=result["search_type"]
                ))
            
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='success',
                query_info={"query_text": query_text, "top_k": top_k, "use_reranker": use_reranker},
                total_results=len(search_items),
                results=search_items
            )
            
        except Exception as e:
            logger.error(f"文本搜索失败: {e}")
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='failure',
                error_message=str(e),
                query_info={"query_text": query_text}
            )
    
    async def execute_shape_search(
        self, 
        shape_vector: List[float], 
        top_k: int = 5
    ) -> EnhancedQueryResult:
        """
        执行形状几何搜索
        
        Args:
            shape_vector: 形状特征向量
            top_k: 返回结果数量
            
        Returns:
            增强的查询结果
        """
        try:
            if not self.connected:
                await self.connect_all()
              # 执行形状搜索
            results = await self.geometry_semantic_agent.search_by_shape(
                shape_vector, top_k
            )
            
            # 转换为标准格式
            search_items = []
            for result in results:
                search_items.append(SearchResultItem(
                    rank=result["rank"],
                    uuid=result["assembly_id"],
                    name=result.get("name", result["assembly_id"]),  # 使用assembly_id作为默认name
                    description=result["description"],
                    similarity_score=result["similarity_score"],
                    search_type=result["search_type"]
                ))
            
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='success',
                query_info={"vector_dimension": len(shape_vector), "top_k": top_k},
                total_results=len(search_items),
                results=search_items
            )
            
        except Exception as e:
            logger.error(f"形状搜索失败: {e}")
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='failure',
                error_message=str(e),
                query_info={"vector_dimension": len(shape_vector) if shape_vector else 0}
            )
    
    async def execute_hybrid_search(
        self,
        query_text: str,
        shape_vector: Optional[List[float]] = None,
        top_k: int = 5,
        shape_weight: float = 0.3
    ) -> EnhancedQueryResult:
        """
        执行混合搜索（文本+形状）
        
        Args:
            query_text: 查询文本
            shape_vector: 形状特征向量（可选）
            top_k: 返回结果数量
            shape_weight: 形状搜索权重
            
        Returns:
            增强的查询结果
        """
        try:
            if not self.connected:
                await self.connect_all()
            
            # 执行混合搜索
            results = await self.geometry_semantic_agent.search_hybrid_with_shape(
                query_text, shape_vector, top_k, shape_weight
            )
            
            # 转换为标准格式
            search_items = []
            for result in results:
                search_items.append(SearchResultItem(
                    rank=result["rank"],
                    uuid=result["assembly_id"],
                    description=result["description"],
                    similarity_score=result["similarity_score"],
                    search_type=result["search_type"],
                    metadata={
                        "has_shape_vector": result.get("has_shape_vector", False),
                        "shape_weight": result.get("shape_weight", shape_weight)
                    }
                ))
            
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='success',
                query_info={
                    "query_text": query_text,
                    "has_shape_vector": shape_vector is not None,
                    "shape_weight": shape_weight,
                    "top_k": top_k
                },
                total_results=len(search_items),
                results=search_items
            )
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='failure',
                error_message=str(e),
                query_info={"query_text": query_text}
            )
    
    async def execute_structured_query(
        self,
        query_text: str,
        generate_sql: bool = True
    ) -> EnhancedQueryResult:
        """
        执行结构化数据查询
        
        Args:
            query_text: 自然语言查询或SQL语句
            generate_sql: 是否自动生成SQL
            
        Returns:
            增强的查询结果
        """
        try:
            if not self.connected:
                await self.connect_all()
            
            # 执行结构化查询
            results = await self.structured_data_agent.query_by_text(
                query_text, generate_sql
            )
            
            # 转换为标准格式
            search_items = []
            for i, result in enumerate(results):
                search_items.append(SearchResultItem(
                    rank=i + 1,
                    uuid=result.get("uuid", "unknown"),
                    name=result.get("name", ""),
                    description=result.get("description", ""),
                    similarity_score=1.0,  # 结构化查询没有相似度概念
                    search_type="structured",
                    metadata=result
                ))
            
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='success',
                query_info={"query_text": query_text, "generate_sql": generate_sql},
                total_results=len(search_items),
                results=search_items
            )
            
        except Exception as e:
            logger.error(f"结构化查询失败: {e}")
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='failure',
                error_message=str(e),
                query_info={"query_text": query_text}
            )
    
    async def execute_structural_query(
        self,
        query_text: str,
        generate_cypher: bool = True
    ) -> EnhancedQueryResult:
        """
        执行结构关系查询
        
        Args:
            query_text: 自然语言查询或Cypher语句
            generate_cypher: 是否自动生成Cypher
            
        Returns:
            增强的查询结果
        """
        try:
            if not self.connected:
                await self.connect_all()
            
            # 执行结构关系查询
            results = await self.structural_relationship_agent.query_by_text(
                query_text, generate_cypher
            )
            
            # 转换为标准格式
            search_items = []
            for i, result in enumerate(results):
                # 提取节点ID和名称（如果是节点对象）
                item_id = "unknown"
                item_name = ""
                item_desc = ""
                
                # 尝试从结果中提取有用信息
                if isinstance(result, dict):
                    # 查找可能的ID字段
                    for key in ["part_id", "assembly_id", "id", "uuid"]:
                        if key in result:
                            item_id = str(result[key])
                            break
                    
                    # 查找可能的名称字段
                    for key in ["name", "part_name", "assembly_name", "component_name"]:
                        if key in result:
                            item_name = str(result[key])
                            break
                    
                    # 查找可能的描述字段
                    for key in ["description", "type", "labels"]:
                        if key in result:
                            item_desc = str(result[key])
                            break
                
                search_items.append(SearchResultItem(
                    rank=i + 1,
                    uuid=item_id,
                    name=item_name,
                    description=item_desc,
                    similarity_score=1.0,  # 图查询没有相似度概念
                    search_type="structural",
                    metadata=result
                ))
            
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='success',
                query_info={"query_text": query_text, "generate_cypher": generate_cypher},
                total_results=len(search_items),
                results=search_items
            )
            
        except Exception as e:
            logger.error(f"结构关系查询失败: {e}")
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='failure',
                error_message=str(e),
                query_info={"query_text": query_text}
            )
    
    async def execute_property_filter(
        self,
        material: Optional[str] = None,
        mass_min: Optional[float] = None,
        mass_max: Optional[float] = None,
        volume_min: Optional[float] = None,
        volume_max: Optional[float] = None,
        has_holes: Optional[bool] = None,
        limit: int = 50
    ) -> EnhancedQueryResult:
        """
        执行基于属性的过滤查询
        
        Args:
            material: 材料类型
            mass_min: 最小质量
            mass_max: 最大质量
            volume_min: 最小体积
            volume_max: 最大体积
            has_holes: 是否有孔
            limit: 返回结果数量限制
            
        Returns:
            增强的查询结果
        """
        try:
            if not self.connected:
                await self.connect_all()
            
            # 执行属性过滤查询
            results = await self.structured_data_agent.query_parts_by_properties(
                material=material,
                mass_min=mass_min,
                mass_max=mass_max,
                volume_min=volume_min,
                volume_max=volume_max,
                has_holes=has_holes,
                limit=limit
            )
            
            # 转换为标准格式
            search_items = []
            for i, result in enumerate(results):
                search_items.append(SearchResultItem(
                    rank=i + 1,
                    uuid=result.get("uuid", "unknown"),
                    name=result.get("name", ""),
                    description=result.get("description", ""),
                    similarity_score=1.0,
                    search_type="property_filter",
                    metadata=result
                ))
            
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='success',
                query_info={
                    "material": material,
                    "mass_range": [mass_min, mass_max],
                    "volume_range": [volume_min, volume_max],
                    "has_holes": has_holes,
                    "limit": limit
                },
                total_results=len(search_items),
                results=search_items
            )
            
        except Exception as e:
            logger.error(f"属性过滤查询失败: {e}")
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='failure',
                error_message=str(e),
                query_info={"material": material}
            )
    
    async def execute_multimodal_query(
        self,
        text_query: Optional[str] = None,
        shape_vector: Optional[List[float]] = None,
        material_filter: Optional[str] = None,
        mass_range: Optional[tuple] = None,
        structural_query: Optional[str] = None,
        fusion_strategy: str = "weighted",
        top_k: int = 10
    ) -> EnhancedQueryResult:
        """
        执行多模态查询，融合多种查询类型的结果
        
        Args:
            text_query: 文本查询
            shape_vector: 形状向量
            material_filter: 材料过滤
            mass_range: 质量范围 (min, max)
            structural_query: 结构关系查询
            fusion_strategy: 融合策略
            top_k: 最终返回结果数量
            
        Returns:
            增强的查询结果
        """
        try:
            if not self.connected:
                await self.connect_all()
            
            all_results = []
            query_info = {
                "fusion_strategy": fusion_strategy,
                "top_k": top_k,
                "queries_executed": []
            }
            
            # 1. 执行向量搜索（如果有文本或形状查询）
            if text_query or shape_vector:
                if text_query and shape_vector:
                    # 混合搜索
                    vector_result = await self.execute_hybrid_search(
                        text_query, shape_vector, top_k * 2
                    )
                    query_info["queries_executed"].append("hybrid_search")
                elif text_query:
                    # 文本搜索
                    vector_result = await self.execute_text_search(
                        text_query, top_k * 2
                    )
                    query_info["queries_executed"].append("text_search")
                else:
                    # 形状搜索
                    vector_result = await self.execute_shape_search(
                        shape_vector, top_k * 2
                    )
                    query_info["queries_executed"].append("shape_search")
                
                if vector_result.status == 'success':
                    all_results.extend(vector_result.results)
            
            # 2. 执行结构化查询（如果有属性过滤）
            if material_filter or mass_range:
                mass_min, mass_max = mass_range if mass_range else (None, None)
                structured_result = await self.execute_property_filter(
                    material=material_filter,
                    mass_min=mass_min,
                    mass_max=mass_max,
                    limit=top_k * 2
                )
                query_info["queries_executed"].append("property_filter")
                
                if structured_result.status == 'success':
                    all_results.extend(structured_result.results)
            
            # 3. 执行结构关系查询（如果有）
            if structural_query:
                structural_result = await self.execute_structural_query(
                    structural_query
                )
                query_info["queries_executed"].append("structural_query")
                
                if structural_result.status == 'success':
                    all_results.extend(structural_result.results)
            
            # 4. 融合结果
            fused_results = self._fuse_results(all_results, fusion_strategy, top_k)
            
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='success',
                query_info=query_info,
                total_results=len(fused_results),
                results=fused_results
            )
            
        except Exception as e:
            logger.error(f"多模态查询失败: {e}")
            return EnhancedQueryResult(
                task_id=str(uuid.uuid4()),
                status='failure',
                error_message=str(e),
                query_info={"fusion_strategy": fusion_strategy}
            )
    
    def _fuse_results(
        self,
        results: List[SearchResultItem],
        strategy: str,
        top_k: int
    ) -> List[SearchResultItem]:
        """
        融合多个查询的结果
        
        Args:
            results: 所有查询结果
            strategy: 融合策略
            top_k: 返回结果数量
            
        Returns:
            融合后的结果列表
        """
        if not results:
            return []
        
        if strategy == "weighted":
            # 加权融合：根据不同查询类型给予不同权重
            weights = {
                "semantic": 0.4,
                "geometry": 0.3,
                "hybrid": 0.5,
                "structured": 0.2,
                "structural": 0.3,
                "property_filter": 0.2
            }
            
            # 重新计算分数
            for result in results:
                weight = weights.get(result.search_type, 0.3)
                result.similarity_score = result.similarity_score * weight
        
        elif strategy == "sequential":
            # 序列融合：按查询类型优先级排序
            priority = {
                "hybrid": 1,
                "semantic": 2,
                "geometry": 3,
                "structural": 4,
                "structured": 5,
                "property_filter": 6
            }
            
            results.sort(key=lambda x: (
                priority.get(x.search_type, 10),
                -x.similarity_score
            ))
        
        # 去重（基于ID）
        seen_ids = set()
        unique_results = []
        for result in results:
            if result.uuid not in seen_ids:
                seen_ids.add(result.uuid)
                unique_results.append(result)
        
        # 重新排序并限制数量
        if strategy == "weighted":
            unique_results.sort(key=lambda x: -x.similarity_score)
        
        # 重新设置排名
        for i, result in enumerate(unique_results[:top_k]):
            result.rank = i + 1
        
        return unique_results[:top_k]
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect_all()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect_all()
