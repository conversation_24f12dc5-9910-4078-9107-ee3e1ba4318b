#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用不同的降维算法(TSNE, PCA, UMAP)对零件特征进行可视化。

支持分析不同类型的特征：
- structural: 结构化特征
- shape: 形状特征  
- semantic: 语义特征
- fused_features: 融合特征

用法示例：

# 使用UMAP对形状特征进行降维可视化
python evaluate_scripts/clustering_analysis/visualize_part_embeddings.py \
    --feature_path dataset/part_features.pkl \
    --feature_type shape \
    --method umap \
    --output_dir visualization_results/shape_umap

# 使用TSNE对融合特征进行降维可视化
python evaluate_scripts/clustering_analysis/visualize_part_embeddings.py \
    --feature_path dataset/part_features.pkl \
    --feature_type fused_features \
    --method tsne \
    --output_dir visualization_results/fused_tsne

# 使用PCA对结构化特征进行降维可视化
python evaluate_scripts/clustering_analysis/visualize_part_embeddings.py \
    --feature_path dataset/part_features.pkl \
    --feature_type structural \
    --method pca \
    --output_dir visualization_results/structural_pca
"""
import os
import sys
import pickle
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap
from pathlib import Path
import random
from PIL import Image
import matplotlib.gridspec as gridspec
import argparse

def load_part_features(feature_path="dataset/part_features.pkl", feature_type="shape"):
    """
    从 part_features.pkl 文件中加载指定类型的特征
    
    参数:
        feature_path (str): 特征文件路径
        feature_type (str): 特征类型，可选 'structural', 'shape', 'semantic', 'fused_features'
        
    返回:
        tuple: (特征向量数组, 零件ID列表)
    """
    print(f"正在加载特征向量: {feature_path}, 特征类型: {feature_type}")
    
    with open(feature_path, 'rb') as f:
        features = pickle.load(f)
    
    # 检查特征类型是否存在
    if feature_type not in features:
        available_types = [k for k in features.keys() if isinstance(features[k], np.ndarray)]
        raise KeyError(f"特征类型 '{feature_type}' 不存在。可用类型: {available_types}")
    
    feature_vectors = features[feature_type]
    part_ids = features.get('part_ids', [])
    
    # 验证特征和ID数量匹配
    if len(part_ids) != feature_vectors.shape[0]:
        print(f"警告: 零件ID数量 ({len(part_ids)}) 与特征数量 ({feature_vectors.shape[0]}) 不匹配")
        # 如果没有part_ids，创建默认ID
        if not part_ids:
            part_ids = [f"part_{i}" for i in range(feature_vectors.shape[0])]
        else:
            # 截断或扩展part_ids以匹配特征数量
            if len(part_ids) > feature_vectors.shape[0]:
                part_ids = part_ids[:feature_vectors.shape[0]]
            else:
                part_ids.extend([f"part_{i}" for i in range(len(part_ids), feature_vectors.shape[0])])
    
    print(f"加载了 {feature_vectors.shape[0]} 个 {feature_type} 特征，维度: {feature_vectors.shape[1]}")
    return feature_vectors, part_ids


def load_features(feature_path="dataset/clip_features.pkl"):
    """
    兼容旧版本的加载函数，用于读取 clip_features.pkl 格式的文件
    
    参数:
        feature_path (str): 特征向量文件路径
        
    返回:
        dict: 加载的特征向量字典
    """
    print(f"正在加载特征向量: {feature_path} (旧格式)")
    with open(feature_path, 'rb') as f:
        features = pickle.load(f)
    
    return features

def prepare_data_for_visualization(features):
    """
    兼容旧版本的数据准备函数，用于处理 clip_features.pkl 格式的数据
    
    参数:
        features (dict): 特征向量字典
        
    返回:
        tuple: (展平的特征向量数组, 零件名称列表, 装配体名称列表)
    """
    all_features = []
    all_part_names = []
    all_assembly_names = []
    
    for assembly_name, parts in features.items():
        for part_name, feature_vector in parts.items():
            # 确保特征向量是一维数组
            if len(feature_vector.shape) > 1 and feature_vector.shape[0] == 1:
                feature_vector = feature_vector[0]
            
            all_features.append(feature_vector)
            all_part_names.append(part_name)
            all_assembly_names.append(assembly_name)
    
    return np.array(all_features), all_part_names, all_assembly_names

def apply_dimensionality_reduction(features, method='tsne', n_components=2, random_state=42):
    """
    应用降维算法
    
    参数:
        features (numpy.ndarray): 特征向量数组
        method (str): 降维方法，可选 'tsne', 'pca', 或 'umap'
        n_components (int): 降维后的维度
        random_state (int): 随机种子
        
    返回:
        numpy.ndarray: 降维后的特征向量
    """
    print(f"使用 {method.upper()} 进行降维到 {n_components} 维...")
    print(f"原始特征维度: {features.shape}")
    
    if method.lower() == 'tsne':
        # 对于TSNE，如果特征维度太高，先用PCA降维
        if features.shape[1] > 50:
            print("特征维度过高，先使用PCA降维到50维")
            pca = PCA(n_components=50, random_state=random_state)
            features = pca.fit_transform(features)
        reducer = TSNE(n_components=n_components, random_state=random_state, perplexity=30, n_iter=2000)
    elif method.lower() == 'pca':
        # 确保PCA的组件数不超过特征维度和样本数
        n_components = min(n_components, features.shape[1], features.shape[0])
        reducer = PCA(n_components=n_components, random_state=random_state)
    elif method.lower() == 'umap':
        reducer = umap.UMAP(n_components=n_components, random_state=random_state)
    else:
        raise ValueError(f"不支持的降维方法: {method}")
    
    reduced_features = reducer.fit_transform(features)
    print(f"降维后特征维度: {reduced_features.shape}")
    return reduced_features

def visualize_2d(reduced_features, part_names, assembly_names, output_dir="visualization_results", 
                color_by='part_id', figsize=(12, 10), save_fig=True, interactive=False, 
                max_parts_to_label=50, original_data_dir=None, feature_type="unknown", method="unknown"):
    """
    2D可视化降维后的特征向量
    
    参数:
        reduced_features (numpy.ndarray): 降维后的特征向量
        part_names (list): 零件名称列表
        assembly_names (list): 装配体名称列表
        output_dir (str): 输出目录
        color_by (str): 着色方式，'assembly'、'part_id'或'random'
        figsize (tuple): 图形大小
        save_fig (bool): 是否保存图形
        interactive (bool): 是否添加交互功能
        max_parts_to_label (int): 最大标记零件数量
        original_data_dir (str): 原始数据目录，用于显示零件图片
        feature_type (str): 特征类型
        method (str): 降维方法
    """
    os.makedirs(output_dir, exist_ok=True)
    
    plt.figure(figsize=figsize)
    ax = plt.subplot(111)
    
    # 根据着色方式分配颜色
    if color_by == 'assembly':
        unique_assemblies = list(set(assembly_names))
        color_dict = {assembly: plt.cm.tab20(i % 20) for i, assembly in enumerate(unique_assemblies)}
        colors = [color_dict[assembly] for assembly in assembly_names]
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                          label=assembly[:15] + ('...' if len(assembly) > 15 else ''), 
                          markerfacecolor=color, markersize=8) 
                          for assembly, color in color_dict.items()]
        legend_title = "装配体"
    elif color_by == 'part_id':
        # 根据零件ID的不同前缀或模式进行着色
        unique_prefixes = list(set([name.split('_')[0] if '_' in name else name[:3] for name in part_names]))
        color_dict = {prefix: plt.cm.tab20(i % 20) for i, prefix in enumerate(unique_prefixes)}
        colors = [color_dict[name.split('_')[0] if '_' in name else name[:3]] for name in part_names]
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                          label=prefix, markerfacecolor=color, markersize=8) 
                          for prefix, color in color_dict.items()]
        legend_title = "零件类型"
    else:  # random coloring
        colors = [plt.cm.tab20(random.randint(0, 19)) for _ in range(len(part_names))]
        legend_elements = []
        legend_title = None
    
    # 绘制散点图
    scatter = ax.scatter(reduced_features[:, 0], reduced_features[:, 1], c=colors, alpha=0.7)
    
    # 添加标签（如果零件数量不太多）
    if len(part_names) <= max_parts_to_label:
        for i, (x, y) in enumerate(reduced_features):
            plt.annotate(part_names[i], (x, y), fontsize=8)
    
    # 添加图例
    if legend_elements:
        if len(legend_elements) > 20:
            # 如果类别太多，只显示前20个
            legend_elements = legend_elements[:20]
        plt.legend(handles=legend_elements, title=legend_title, loc='best', fontsize='small')
    
    plt.title(f"{feature_type} 特征的二维降维可视化 ({method.upper()})")
    plt.xlabel("维度 1")
    plt.ylabel("维度 2")
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图形
    if save_fig:
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "part_vectors_2d.png"), dpi=300)
        print(f"已保存可视化图片到 {os.path.join(output_dir, 'part_vectors_2d.png')}")
    
    # 添加交互功能（如果启用）
    if interactive and original_data_dir:
        def on_click(event):
            if event.inaxes != ax:
                return
            
            # 查找最近的点
            distances = np.sqrt((reduced_features[:, 0] - event.xdata)**2 + 
                               (reduced_features[:, 1] - event.ydata)**2)
            closest_idx = np.argmin(distances)
            
            # 获取点的信息
            assembly = assembly_names[closest_idx]
            part = part_names[closest_idx]
            
            # 查找并显示零件图片
            img_path = os.path.join(original_data_dir, assembly, f"{part}.png")
            if os.path.exists(img_path):
                plt.figure(figsize=(5, 5))
                plt.imshow(Image.open(img_path))
                plt.title(f"装配体: {assembly}\n零件: {part}")
                plt.axis('off')
                plt.show()
            else:
                print(f"找不到图片: {img_path}")
        
        plt.gcf().canvas.mpl_connect('button_press_event', on_click)
        print("提示: 点击图上的点可以查看对应的零件图片")
    
    plt.show()

def visualize_3d(reduced_features, part_names, assembly_names, output_dir="visualization_results", 
                color_by='assembly', figsize=(12, 10), save_fig=True):
    """
    3D可视化降维后的特征向量
    
    参数:
        reduced_features (numpy.ndarray): 降维后的特征向量
        part_names (list): 零件名称列表
        assembly_names (list): 装配体名称列表
        output_dir (str): 输出目录
        color_by (str): 着色方式，'assembly'或'random'
        figsize (tuple): 图形大小
        save_fig (bool): 是否保存图形
    """
    os.makedirs(output_dir, exist_ok=True)
    
    fig = plt.figure(figsize=figsize)
    ax = fig.add_subplot(111, projection='3d')
    
    # 根据装配体或随机分配颜色
    if color_by == 'assembly':
        unique_assemblies = list(set(assembly_names))
        color_dict = {assembly: plt.cm.tab20(i % 20) for i, assembly in enumerate(unique_assemblies)}
        colors = [color_dict[assembly] for assembly in assembly_names]
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                          label=assembly[:15] + ('...' if len(assembly) > 15 else ''), 
                          markerfacecolor=color, markersize=8) 
                          for assembly, color in color_dict.items()]
        legend_title = "装配体"
    else:  # random coloring
        colors = [plt.cm.tab20(random.randint(0, 19)) for _ in range(len(part_names))]
        legend_elements = []
        legend_title = None
    
    # 绘制3D散点图
    scatter = ax.scatter(
        reduced_features[:, 0], 
        reduced_features[:, 1], 
        reduced_features[:, 2], 
        c=colors, 
        alpha=0.7
    )
    
    # 添加图例
    if legend_elements:
        if len(legend_elements) > 20:
            # 如果装配体太多，只显示前20个
            legend_elements = legend_elements[:20]
            plt.legend(handles=legend_elements, title=legend_title, loc='best', fontsize='small')
        else:
            plt.legend(handles=legend_elements, title=legend_title, loc='best')
    
    plt.title("零件形状向量的三维降维可视化")
    ax.set_xlabel("维度 1")
    ax.set_ylabel("维度 2")
    ax.set_zlabel("维度 3")
    
    # 保存图形
    if save_fig:
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "part_vectors_3d.png"), dpi=300)
        print(f"已保存可视化图片到 {os.path.join(output_dir, 'part_vectors_3d.png')}")
    
    plt.show()

def main():
    # 配置参数
    feature_path = "dataset/clip_features.pkl"  # 特征向量文件路径
    output_dir = "visualization_results"  # 输出目录
    
    # 加载特征向量
    features = load_features(feature_path)
    
    # 准备数据
    feature_array, part_names, assembly_names = prepare_data_for_visualization(features)
    print(f"加载了 {len(feature_array)} 个零件的特征向量")
def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零件特征降维可视化")
    parser.add_argument("--feature_path", default="dataset/part_features.pkl", help="特征文件路径")
    parser.add_argument("--feature_type", default="structural", 
                       choices=['structural', 'shape', 'semantic', 'fused_features'],
                       help="要可视化的特征类型")
    parser.add_argument("--method", default="umap", 
                       choices=['tsne', 'pca', 'umap'],
                       help="降维方法")
    parser.add_argument("--output_dir", default="visualization_results", help="输出目录")
    parser.add_argument("--color_by", default="part_id", 
                       choices=['assembly', 'part_id', 'random'],
                       help="着色方式")
    parser.add_argument("--max_parts_to_label", type=int, default=50, help="最大标记零件数量")
    parser.add_argument("--figsize", nargs=2, type=float, default=[12, 10], help="图形大小")
    parser.add_argument("--legacy_format", action="store_true", help="使用旧版本的 clip_features.pkl 格式")
    parser.add_argument("--n_components", type=int, default=2, help="降维后的维度")
    parser.add_argument("--random_state", type=int, default=42, help="随机种子")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = os.path.join(args.output_dir, f"{args.feature_type}_{args.method}")
    
    try:
        if args.legacy_format:
            # 使用旧版本格式
            features = load_features(args.feature_path)
            feature_array, part_names, assembly_names = prepare_data_for_visualization(features)
            feature_type = "clip_features"
        else:
            # 使用新版本格式
            feature_array, part_names = load_part_features(args.feature_path, args.feature_type)
            assembly_names = ['unknown'] * len(part_names)  # 新格式中没有装配信息
            feature_type = args.feature_type
        
        # 应用降维
        reduced_features = apply_dimensionality_reduction(
            feature_array, 
            method=args.method, 
            n_components=args.n_components,
            random_state=args.random_state
        )
        
        # 生成可视化
        if args.n_components == 2:
            visualize_2d(
                reduced_features, 
                part_names, 
                assembly_names, 
                output_dir=output_dir,
                color_by=args.color_by,
                figsize=tuple(args.figsize),
                save_fig=True,
                max_parts_to_label=args.max_parts_to_label,
                feature_type=feature_type,
                method=args.method
            )
        elif args.n_components == 3:
            # 如果需要3D可视化，可以在这里添加
            print("3D可视化功能尚未实现")
        else:
            print(f"不支持 {args.n_components} 维可视化")
            
        print(f"\n降维可视化完成！输出目录: {output_dir}")
        
    except Exception as e:
        print(f"可视化过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()