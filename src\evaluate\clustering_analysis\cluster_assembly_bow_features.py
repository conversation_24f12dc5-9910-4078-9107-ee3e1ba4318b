#!/usr/bin/env python3
"""
装配体词袋(Bag-of-Parts)聚类脚本

基于装配体的词袋特征进行聚类分析：
- 支持多种聚类算法(HDBSCAN, K-Means, 层次聚类)
- 支持多种距离度量(欧氏距离, 余弦距离)
- 支持降维(PCA, UMAP)
"""

import argparse
import pickle
from pathlib import Path
import numpy as np
from sklearn.cluster import KMeans
from sklearn.cluster import AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.metrics.pairwise import cosine_distances
import hdbscan
import umap
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_bow_features(feature_file):
    """加载词袋特征"""
    logger.info(f"正在加载词袋特征: {feature_file}")
    
    with open(feature_file, 'rb') as f:
        bow_data = pickle.load(f)
    
    features = bow_data['features']
    assembly_names = bow_data['assembly_names']
    
    logger.info(f"加载了 {len(assembly_names)} 个装配体的 {features.shape[1]} 维特征")
    
    return features, assembly_names, bow_data

def apply_dimensionality_reduction(features, method='pca', n_components=50, **kwargs):
    """应用降维"""
    if method == 'none':
        return features, None
    
    logger.info(f"应用 {method.upper()} 降维到 {n_components} 维...")
    
    if method == 'pca':
        reducer = PCA(n_components=n_components, random_state=42)
        reduced_features = reducer.fit_transform(features)
    elif method == 'umap':
        reducer = umap.UMAP(n_components=n_components, random_state=42, **kwargs)
        reduced_features = reducer.fit_transform(features)
    else:
        raise ValueError(f"不支持的降维方法: {method}")
    
    logger.info(f"降维完成，原始维度: {features.shape[1]} -> 降维后: {reduced_features.shape[1]}")
    
    return reduced_features, reducer

def perform_clustering(features, algorithm='hdbscan', **params):
    """执行聚类"""
    logger.info(f"使用 {algorithm.upper()} 进行聚类...")
    logger.info(f"聚类参数: {params}")
    
    if algorithm == 'hdbscan':
        clusterer = hdbscan.HDBSCAN(**params)
        cluster_labels = clusterer.fit_predict(features)
        cluster_model = clusterer
        
    elif algorithm == 'kmeans':
        n_clusters = params.get('n_clusters', 8)
        clusterer = KMeans(n_clusters=n_clusters, random_state=42, **params)
        cluster_labels = clusterer.fit_predict(features)
        cluster_model = clusterer
        
    elif algorithm == 'hierarchical':
        n_clusters = params.get('n_clusters', 8)
        linkage = params.get('linkage', 'ward')
        clusterer = AgglomerativeClustering(n_clusters=n_clusters, linkage=linkage)
        cluster_labels = clusterer.fit_predict(features)
        cluster_model = clusterer
        
    else:
        raise ValueError(f"不支持的聚类算法: {algorithm}")
    
    return cluster_labels, cluster_model

def evaluate_clustering(features, labels, algorithm='hdbscan'):
    """评估聚类结果"""
    logger.info("评估聚类结果...")
    
    # 统计聚类信息
    unique_labels = np.unique(labels)
    n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
    n_noise = np.sum(labels == -1) if -1 in labels else 0
    
    logger.info(f"聚类数量: {n_clusters}")
    logger.info(f"噪声点数量: {n_noise}")
    
    # 聚类大小分布
    if n_clusters > 0:
        valid_labels = labels[labels != -1] if -1 in labels else labels
        unique_valid, counts = np.unique(valid_labels, return_counts=True)
        
        logger.info(f"聚类大小统计:")
        logger.info(f"  平均大小: {np.mean(counts):.2f}")
        logger.info(f"  大小范围: {np.min(counts)} - {np.max(counts)}")
        logger.info(f"  最大的5个聚类大小: {sorted(counts, reverse=True)[:5]}")
    
    # 计算聚类质量指标
    evaluation_metrics = {}
    
    if n_clusters > 1 and len(np.unique(labels)) > 1:
        try:
            # 轮廓系数 (不包括噪声点)
            if n_noise < len(labels):
                valid_indices = labels != -1
                if np.sum(valid_indices) > 1 and len(np.unique(labels[valid_indices])) > 1:
                    silhouette = silhouette_score(features[valid_indices], labels[valid_indices])
                    evaluation_metrics['silhouette_score'] = silhouette
                    logger.info(f"轮廓系数: {silhouette:.4f}")
            
            # Calinski-Harabasz指数
            if len(np.unique(labels)) > 1:
                ch_score = calinski_harabasz_score(features, labels)
                evaluation_metrics['calinski_harabasz_score'] = ch_score
                logger.info(f"Calinski-Harabasz指数: {ch_score:.4f}")
                
        except Exception as e:
            logger.warning(f"计算评估指标时出错: {e}")
    
    return evaluation_metrics

def analyze_cluster_content(labels, assembly_names, top_n=10):
    """分析聚类内容"""
    logger.info("分析聚类内容...")
    
    unique_labels = np.unique(labels)
    cluster_analysis = {}
    
    for label in unique_labels:
        if label == -1:
            continue
            
        cluster_assemblies = [assembly_names[i] for i in range(len(labels)) if labels[i] == label]
        cluster_size = len(cluster_assemblies)
        
        cluster_analysis[label] = {
            'size': cluster_size,
            'assemblies': cluster_assemblies[:top_n],  # 只保存前N个作为示例
            'assembly_count': cluster_size
        }
        
        logger.info(f"聚类 {label}: {cluster_size} 个装配体")
        if cluster_size <= 5:
            logger.info(f"  装配体: {cluster_assemblies}")
        else:
            logger.info(f"  前5个装配体: {cluster_assemblies[:5]}")
    
    return cluster_analysis

def save_clustering_results(labels, assembly_names, cluster_model, evaluation_metrics,
                          cluster_analysis, output_file, algorithm, params, 
                          bow_data, reducer=None):
    """保存聚类结果"""
    logger.info(f"保存聚类结果到: {output_file}")
    
    clustering_results = {
        'labels': labels,
        'assembly_names': assembly_names,
        'algorithm': algorithm,
        'params': params,
        'evaluation_metrics': evaluation_metrics,
        'cluster_analysis': cluster_analysis,
        'cluster_model': cluster_model,
        'reducer': reducer,
        'source_bow_data': {
            'feature_type': bow_data.get('feature_type', 'unknown'),
            'num_assemblies': bow_data.get('num_assemblies', len(assembly_names)),
            'num_clusters': bow_data.get('num_clusters', 0),
            'feature_dim': bow_data.get('feature_dim', 0),
            'params': bow_data.get('params', {})
        }
    }
    
    with open(output_file, 'wb') as f:
        pickle.dump(clustering_results, f)
    
    logger.info(f"已保存 {len(assembly_names)} 个装配体的聚类结果")

def main():
    parser = argparse.ArgumentParser(description='装配体词袋特征聚类')
    parser.add_argument('--feature-file', type=str,
                       default='dataset/assembly_bow_features.pkl',
                       help='词袋特征文件')
    parser.add_argument('--output-file', type=str,
                       default='dataset/assembly_bow_clustering_results.pkl',
                       help='输出聚类结果文件')
    
    # 降维参数
    parser.add_argument('--reduce-dim', type=str, choices=['none', 'pca', 'umap'],
                       default='none', help='降维方法')
    parser.add_argument('--n-components', type=int, default=50,
                       help='降维后的维数')
    
    # 聚类算法参数
    parser.add_argument('--algorithm', type=str, 
                       choices=['hdbscan', 'kmeans', 'hierarchical'],
                       default='hdbscan', help='聚类算法')
    
    # HDBSCAN参数
    parser.add_argument('--min-cluster-size', type=int, default=5,
                       help='HDBSCAN最小聚类大小')
    parser.add_argument('--min-samples', type=int, default=None,
                       help='HDBSCAN最小样本数')
    parser.add_argument('--metric', type=str, default='euclidean',
                       help='距离度量')
    
    # K-Means和层次聚类参数
    parser.add_argument('--n-clusters', type=int, default=8,
                       help='K-Means和层次聚类的聚类数量')
    parser.add_argument('--linkage', type=str, default='ward',
                       choices=['ward', 'complete', 'average', 'single'],
                       help='层次聚类连接方式')
    
    # 分析参数
    parser.add_argument('--show-analysis', action='store_true',
                       help='显示详细聚类分析')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not Path(args.feature_file).exists():
        logger.error(f"特征文件不存在: {args.feature_file}")
        return
    
    try:
        # 加载词袋特征
        features, assembly_names, bow_data = load_bow_features(args.feature_file)
        
        # 应用降维
        if args.reduce_dim != 'none':
            features, reducer = apply_dimensionality_reduction(
                features, args.reduce_dim, args.n_components)
        else:
            reducer = None
        
        # 准备聚类参数
        cluster_params = {}
        if args.algorithm == 'hdbscan':
            cluster_params = {
                'min_cluster_size': args.min_cluster_size,
                'metric': args.metric
            }
            if args.min_samples is not None:
                cluster_params['min_samples'] = args.min_samples
                
        elif args.algorithm == 'kmeans':
            cluster_params = {
                'n_clusters': args.n_clusters
            }
            
        elif args.algorithm == 'hierarchical':
            cluster_params = {
                'n_clusters': args.n_clusters,
                'linkage': args.linkage
            }
        
        # 执行聚类
        cluster_labels, cluster_model = perform_clustering(
            features, args.algorithm, **cluster_params)
        
        # 评估聚类结果
        evaluation_metrics = evaluate_clustering(features, cluster_labels, args.algorithm)
        
        # 分析聚类内容
        cluster_analysis = None
        if args.show_analysis:
            cluster_analysis = analyze_cluster_content(cluster_labels, assembly_names)
        
        # 保存结果
        all_params = {
            'algorithm': args.algorithm,
            'reduce_dim': args.reduce_dim,
            'n_components': args.n_components,
            **cluster_params
        }
        
        save_clustering_results(
            cluster_labels, assembly_names, cluster_model, evaluation_metrics,
            cluster_analysis, args.output_file, args.algorithm, all_params,
            bow_data, reducer)
        
        logger.info("装配体词袋聚类完成!")
        
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        raise

if __name__ == "__main__":
    main()
