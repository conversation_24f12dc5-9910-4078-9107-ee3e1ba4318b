#!/usr/bin/env python3
"""
查询规划智能体
负责将用户的自然、模糊且多模态的查询分解为结构化的查询计划
"""

import json
import uuid
import time
from typing import List, Dict, Any, Optional

from ..models.LLM import MultiProviderLLM
from .data_models import (
    BaseTask, QueryResult, SearchResultItem,
    QueryPlan, QueryPlannerTask,
    UnifiedStructuredDataTask,
    UnifiedStructuralQueryTask, 
    UnifiedGeometrySemanticTask
)


class QueryPlannerAgent:
    """查询规划智能体 - 将用户查询分解为结构化的查询计划"""
    
    def __init__(self, llm: Optional[MultiProviderLLM] = None):
        """初始化查询规划智能体"""
        self.llm = llm or MultiProviderLLM()
        
        # 智能体能力描述
        self.agent_capabilities = {
            "structured_data": {
                "description": "结构化数据查询智能体，处理数据库表中的字段查询",
                "supported_fields": [
                    "物理属性: mass, volume, density, length, width, height, area",
                    "材料属性: material", 
                    "数值属性: hole_count, part_count",
                    "分类属性: industry, category"
                ],
                "task_type": "UnifiedStructuredDataTask"
            },
            "geometry_semantic": {
                "description": "几何和语义查询智能体，处理功能描述、外形描述等文本语义信息和形状特征向量",
                "capabilities": [
                    "文本语义搜索",
                    "形状相似性搜索", 
                    "混合搜索（文本+形状）"
                ],
                "task_type": "UnifiedGeometrySemanticTask"
            },
            "structural_relationship": {
                "description": "结构关系查询智能体，处理组件包含关系、装配体层次结构、连接关系",
                "capabilities": [
                    "装配体层次结构查询",
                    "零件包含关系查询",
                    "连接关系查询"
                ],
                "task_type": "UnifiedStructuralQueryTask"
            }
        }
    
    def _get_query_planning_prompt(self, query_text: str, has_shape_vector: bool = False) -> str:
        """构建查询规划的提示词"""
        
        prompt = f"""你是一个查询规划智能体，负责将用户的多模态查询分解为结构化的查询计划。

用户查询: {query_text}
{'用户提供了形状向量' if has_shape_vector else '用户未提供形状向量'}

系统中有三个专业智能体：

1. **结构化数据查询智能体** (structured_data)
   - 处理数据库表中存在的字段查询
   - 支持字段：物理属性(mass, volume, density, length, width, height, area)、材料属性(material)、数值属性(hole_count, part_count)、分类属性(industry, category)
   - 任务类型：UnifiedStructuredDataTask

2. **几何和语义查询智能体** (geometry_semantic)  
   - 处理功能描述、外形描述、模型名称等有意义的文本语义信息和形状特征向量
   - 语义查询要求：必须包含具体的功能描述、用途描述、外观特征等有意义的文本，不要使用"形状相似"、"类似模型"等无意义描述
   - 形状查询：当用户提供了形状向量时，可以进行形状相似性搜索
   - 支持：文本语义搜索、形状相似性搜索、混合搜索
   - 任务类型：UnifiedGeometrySemanticTask

3. **结构关系查询智能体** (structural_relationship)
   - 处理组件包含关系、装配体层次结构、连接关系
   - 支持：装配体层次结构查询、零件包含关系查询、连接关系查询
   - 任务类型：UnifiedStructuralQueryTask

请分析用户查询意图，生成一个结构化的查询计划。查询计划应包含：

1. **intent_analysis**: 查询意图分析
   - structured_data_intent: 是否涉及结构化数据查询（材料、尺寸、质量等）
   - geometry_semantic_intent: 是否涉及几何形状或语义描述查询
   - structural_relationship_intent: 是否涉及结构关系查询（装配、包含、连接等）

2. **steps**: 查询步骤列表，每个步骤包含：
   - step_id: 步骤ID
   - agent_type: 使用的智能体类型
   - task_params: 任务参数
   - depends_on: 依赖的前置步骤ID（可选）
   - use_previous_results: 是否使用前置步骤的结果作为id_list过滤

3. **result_fusion**: 结果融合策略
   - strategy: 融合策略 (intersection, union, weighted)
   - weights: 各步骤结果的权重（如果使用weighted策略）

请以JSON格式返回查询计划，确保JSON格式正确。

**重要规则：**
1. 对于geometry_semantic智能体：
   - 如果用户提供了形状向量，优先使用形状搜索（可以不设置query_text或使用简单的功能描述）
   - 如果没有形状向量，只有当查询包含具体的功能描述、用途描述、外观特征时才使用语义搜索
   - 不要使用"形状相似"、"类似模型"、"相近模型"等无意义的描述作为query_text
   - 如果查询中没有具体的语义信息且没有形状向量，可以跳过geometry_semantic步骤
   - **必须设置合理的top_k值**（通常在5-50之间，根据查询需求调整）

2. 对于structured_data和structural_relationship智能体：
   - **不要在task_params中设置top_k参数**，让系统返回所有符合条件的结果
   - 只需要设置query_text参数

注意事项：
- 如果用户提供了形状向量，在geometry_semantic智能体的task_params中不要包含具体的shape_vector值，系统会自动处理
- 对于几何和语义查询，可以包含query_text、top_k、use_reranker等参数
- 对于结构化数据查询，只包含query_text参数，不要设置top_k（让系统返回所有符合条件的结果）
- 对于结构关系查询，只包含query_text参数，不要设置top_k（让系统返回所有符合条件的结果）

示例查询计划格式：
```json
{{
    "intent_analysis": {{
        "structured_data_intent": true,
        "geometry_semantic_intent": true, 
        "structural_relationship_intent": true
    }},
    "steps": [
        {{
            "step_id": "step_1",
            "agent_type": "structured_data",
            "task_params": {{
                "query_text": "材质为Steel的零件"
            }},
            "depends_on": null,
            "use_previous_results": false
        }},
        {{
            "step_id": "step_2",
            "agent_type": "structural_relationship", 
            "task_params": {{
                "query_text": "包含圆柱形零件的装配体"
            }},
            "depends_on": "step_1",
            "use_previous_results": true
        }},
        {{
            "step_id": "step_3", 
            "agent_type": "geometry_semantic",
            "task_params": {{
                "query_text": "圆形零件",
                "top_k": 20,
                "use_reranker": true
            }},
            "depends_on": "step_2",
            "use_previous_results": true
        }}
    ],
    "result_fusion": {{
        "strategy": "intersection",
        "weights": {{}}
    }}
}}
```

请严格按照以上格式生成查询计划，只返回JSON，不要包含其他文本。
"""
        return prompt
    
    async def generate_query_plan(self, task: QueryPlannerTask) -> QueryPlan:
        """生成查询计划"""        
        # 构建提示词
        prompt = self._get_query_planning_prompt(
            task.query_text, 
            task.shape_vector is not None
        )
        
        # 调用LLM生成查询计划
        messages = [{"role": "user", "content": prompt}]
        
        try:
            response = self.llm.chat(messages)
            
            # 检查响应是否为空
            if not response or not response.strip():
                raise ValueError(f"LLM返回空响应")
            
            # 尝试清理响应中的非JSON内容
            response_clean = response.strip()
            if response_clean.startswith("```json"):
                response_clean = response_clean[7:]
            if response_clean.endswith("```"):
                response_clean = response_clean[:-3]
            response_clean = response_clean.strip()
            
            # 解析JSON响应
            plan_data = json.loads(response_clean)
            
            # 创建查询计划对象
            query_plan = QueryPlan(
                plan_id=str(uuid.uuid4()),
                query_text=task.query_text,
                intent_analysis=plan_data["intent_analysis"],
                steps=plan_data["steps"],
                result_fusion=plan_data["result_fusion"]
            )
            
            return query_plan
            
        except json.JSONDecodeError as e:
            raise ValueError(f"LLM返回的JSON格式无效: {e}")
        except KeyError as e:
            raise ValueError(f"查询计划缺少必要字段: {e}")
        except Exception as e:
            raise RuntimeError(f"生成查询计划失败: {e}")
    
    async def execute_task(self, task: QueryPlannerTask) -> QueryResult:
        """执行查询规划任务"""
        start_time = time.time()
        
        try:
            # 生成查询计划
            query_plan = await self.generate_query_plan(task)
            
            # 创建成功结果
            execution_time = time.time() - start_time
            
            # 将查询计划转换为SearchResultItem格式以便与其他智能体保持一致
            result_item = SearchResultItem(
                rank=1,
                uuid=query_plan.plan_id,
                name=f"查询计划 - {task.query_text[:50]}...",
                description=f"生成了包含{len(query_plan.steps)}个步骤的查询计划",
                search_type="query_planning",
                metadata={
                    "query_plan": query_plan.model_dump(),
                    "step_count": len(query_plan.steps),
                    "intent_analysis": query_plan.intent_analysis
                }
            )
            
            return QueryResult(
                task_id=task.task_id,
                status='success',
                results=[result_item],
                execution_time=execution_time,
                total_results=1
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return QueryResult(
                task_id=task.task_id,
                status='failure',
                error_message=str(e),
                execution_time=execution_time,
                total_results=0
            )
    
    def parse_query_plan_from_result(self, result: QueryResult) -> Optional[QueryPlan]:
        """从查询结果中解析查询计划"""
        if result.status != 'success' or not result.results:
            return None
        
        metadata = result.results[0].metadata
        if 'query_plan' not in metadata:
            return None
        
        plan_data = metadata['query_plan']
        return QueryPlan(**plan_data)
