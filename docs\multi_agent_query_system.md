# 多智能体查询系统（Multi-Agent Query System）

## 1. 概述

多智能体查询系统是一个专为 CAD/CAE 领域设计的高级检索系统，能够处理用户的自然语言、多模态、模糊查询请求。系统采用多智能体架构，包含一个查询规划智能体和三个专业查询智能体，能够同时考虑模型的几何形状、语义描述、结构化数据及模型间的结构关系，提供全面、精准的查询结果。

## 2. 系统架构

### 2.1 核心组件

系统由以下核心组件构成：

1. **查询规划智能体（Query Planner Agent）**：

   - 分析用户的自然语言查询，识别查询意图和约束条件
   - 创建结构化的查询计划，指定调用的专业智能体及其参数
   - 定义查询步骤之间的依赖关系和结果融合策略

2. **几何和语义查询智能体（Geometry Semantic Agent）**：

   - 处理基于文本语义和/或形状几何特征的相似度搜索
   - 支持纯文本查询、纯形状查询和混合查询模式
   - 与 Milvus 向量数据库交互，实现高效的向量相似度搜索

3. **结构化数据查询智能体（Structured Data Agent）**：

   - 处理基于精确属性（如材料、质量、尺寸等）的结构化查询
   - 将自然语言查询转换为 SQL 语句，与 PostgreSQL 数据库交互
   - 支持复杂的属性过滤和条件组合

4. **结构关系查询智能体（Structural Relationship Agent）**：

   - 处理零件之间的结构关系查询，如装配层次和连接约束
   - 将自然语言查询转换为 Cypher 查询语句，与 Neo4j 图数据库交互
   - 支持复杂的图遍历和路径分析

5. **多智能体协调器（Multi-Agent Coordinator）**：
   - 协调执行查询计划，调用各专业智能体
   - 管理中间结果和数据流
   - 执行结果融合和排序，生成最终查询结果

### 2.2 系统架构图

```
+---------------------+
| 用户自然语言查询    |
| (可能包含多模态输入) |
+----------+----------+
           |
           v
+----------+----------+      +-----------------------+
| 查询规划智能体      | ---> | 查询计划 (JSON)        |
+----------+----------+      | - 意图分析            |
           |                 | - 查询步骤            |
           v                 | - 结果融合策略        |
+----------+----------+      +-----------------------+
| 多智能体协调器      |
+----------+----------+
     /      |      \
    /       |       \
   v        v        v
+------+ +------+ +------+
|几何和| |结构化| |结构关|
|语义  | |数据  | |系    |
|查询  | |查询  | |查询  |
|智能体| |智能体| |智能体|
+------+ +------+ +------+
   |        |        |
   v        v        v
+------+ +------+ +------+
|Milvus| |Postgr| |Neo4j |
|向量库| |eSQL  | |图库  |
+------+ +------+ +------+
     \      |      /
      \     |     /
       v    v    v
   +---+----+----+---+
   | 结果融合与排序  |
   +-----------------+
           |
           v
   +-------+--------+
   | 最终查询结果    |
   +----------------+
```

## 3. 工作流程

### 3.1 整体流程

1. **查询接收与分析**：

   - 系统接收用户的自然语言查询和可能的多模态输入（如 STEP 文件、STL 模型等）
   - 查询规划智能体分析查询意图，识别主要查询对象、形状约束、属性约束和结构关系约束

2. **查询计划生成**：

   - 查询规划智能体创建结构化的查询计划，包括：
     - 查询意图分析
     - 查询步骤定义（要调用的智能体及其参数）
     - 步骤间的依赖关系
     - 结果融合策略

3. **查询执行**：

   - 多智能体协调器按计划依次调用各专业智能体
   - 管理中间结果，并在必要时将前一步骤的结果作为 ID 过滤条件传递给后续步骤

4. **结果融合**：

   - 协调器根据融合策略（加权融合、序列融合等）合并各智能体的查询结果
   - 对结果进行排序、去重和筛选

5. **结果返回**：
   - 系统返回标准化的查询结果，包含相关度评分、元数据等信息

### 3.2 查询计划示例

以查询"给我找跟这段 STEP 差不多的泵阀零件，材料得用 304 不锈钢，而且要直接用法兰接到离心泵"为例：

```json
{
  "intent_analysis": {
    "main_object": "泵阀零件",
    "shape_constraints": {
      "similarity_type": "geometric",
      "reference": "STEP模型"
    },
    "property_constraints": {
      "material": "304不锈钢"
    },
    "structural_constraints": {
      "connection_type": "法兰",
      "connected_to": "离心泵"
    }
  },
  "steps": [
    {
      "id": "step1",
      "agent": "GeometrySemanticAgent",
      "description": "基于形状和泵阀语义进行初步筛选",
      "parameters": {
        "query_text": "泵阀零件",
        "shape_weight": 0.6,
        "top_k": 30
      }
    },
    {
      "id": "step2",
      "agent": "StructuredDataAgent",
      "description": "筛选304不锈钢材料的零件",
      "parameters": {
        "query_text": "材料为304不锈钢的零件",
        "top_k": 50
      },
      "depends_on": ["step1"]
    },
    {
      "id": "step3",
      "agent": "StructuralRelationshipAgent",
      "description": "查找通过法兰连接到离心泵的零件",
      "parameters": {
        "query_text": "通过法兰连接到离心泵的零件",
        "top_k": 20
      },
      "depends_on": ["step2"]
    }
  ],
  "result_fusion": {
    "method": "weighted",
    "weights": {
      "geometry": 0.5,
      "structured": 0.3,
      "structural": 0.2
    }
  }
}
```

## 4. 使用方法

### 4.1 基本用法

```python
import asyncio
from src.agent.multi_agent_coordinator import MultiAgentCoordinator

async def search_example():
    # 初始化协调器
    coordinator = MultiAgentCoordinator()

    try:
        # 连接所有智能体
        await coordinator.connect_all()

        # 执行查询
        query_text = "给我找跟这段 STEP 差不多的泵阀零件，材料得用 304 不锈钢，而且要直接用法兰接到离心泵。"
        result = await coordinator.process_query(
            query_text=query_text,
            top_k=10  # 返回结果数量
        )

        # 处理结果
        if result.status == 'success':
            print(f"查询成功，找到 {result.total_results} 个结果")
            print(f"执行时间: {result.execution_time:.2f} 秒")

            for item in result.results:
                print(f"- {item.name} (ID: {item.uuid})")
                print(f"  相似度: {item.similarity_score:.3f}")
                print(f"  搜索类型: {item.search_type}")
                print(f"  描述: {item.description}")
        else:
            print(f"查询失败: {result.error_message}")

    finally:
        # 断开连接
        await coordinator.disconnect_all()

# 运行示例
asyncio.run(search_example())
```

### 4.2 高级用法 - 使用上下文管理器

```python
import asyncio
from src.agent.multi_agent_coordinator import MultiAgentCoordinator

async def advanced_search_example():
    # 使用上下文管理器自动处理连接和断开
    async with MultiAgentCoordinator() as coordinator:
        # 执行多个查询
        queries = [
            "这张散热片草图的相似件，热导率要超过 200 W/mK，和 PCB 靠卡扣固定在一起。",
            "在铝合金部件里，找形状跟这份点云相近的碰撞缓冲块，要通过橡胶垫片连到车身侧梁。"
        ]

        for query in queries:
            result = await coordinator.process_query(query)

            if result.status == 'success':
                print(f"查询成功: {query}")
                print(f"找到 {result.total_results} 个结果")
            else:
                print(f"查询失败: {result.error_message}")

# 运行示例
asyncio.run(advanced_search_example())
```

### 4.3 仅使用查询规划

如果只需要获取查询计划而不执行完整查询：

```python
import asyncio
from src.agent.query_planner_agent import QueryPlannerAgent, QueryPlannerTask

async def get_query_plan():
    planner = QueryPlannerAgent()

    try:
        await planner.connect()

        # 创建查询任务
        task = QueryPlannerTask(
            task_id="plan_task",
            query_text="我上传的U型支架，帮我找壁厚不超过2毫米、能支撑5公斤以上的轻量化替代件。"
        )

        # 执行查询规划
        result = await planner.execute_task(task)

        if result.status == 'success':
            query_plan = result.results[0].metadata
            print("查询计划:", query_plan)
            return query_plan
        else:
            print(f"规划失败: {result.error_message}")
            return None

    finally:
        await planner.disconnect()

# 运行示例
asyncio.run(get_query_plan())
```

## 5. 多智能体协作示例

### 5.1 几何形状+属性约束

用户查询：

> "这张散热片草图的相似件，热导率要超过 200 W/mK，和 PCB 靠卡扣固定在一起。"

协作流程：

1. 几何和语义智能体：基于"散热片"的语义和上传草图的形状进行初步搜索
2. 结构化数据智能体：在第 1 步结果中筛选热导率超过 200 W/mK 的零件
3. 结构关系智能体：在第 2 步结果中查找与 PCB 通过卡扣连接的零件
4. 多智能体协调器：融合三个智能体的结果，生成最终排序列表

### 5.2 形状+结构关系查询

用户查询：

> "根据这个 STL 片段，搜公差在 ±0.03 毫米以内的精密定位销，而且它得是叶子零件直接装在主夹具板上。"

协作流程：

1. 几何和语义智能体：基于"精密定位销"的语义和上传 STL 的形状进行初步搜索
2. 结构化数据智能体：在第 1 步结果中筛选公差在 ±0.03 毫米以内的零件
3. 结构关系智能体：在第 2 步结果中查找直接安装在主夹具板上的叶子零件
4. 多智能体协调器：融合结果，突出符合所有条件的零件

## 6. 系统配置与扩展

### 6.1 配置选项

系统配置位于`src/config.py`文件中，主要包括：

- 数据库连接信息（Milvus、PostgreSQL、Neo4j）
- LLM 设置（API 密钥、模型参数等）
- 向量搜索参数（相似度阈值、重排序设置等）

### 6.2 扩展系统

系统设计具有良好的可扩展性：

1. **添加新的专业智能体**：

   - 创建继承自`BaseAgent`的新智能体类
   - 实现`connect()`、`disconnect()`和`execute_task()`方法
   - 在`multi_agent_coordinator.py`中注册新智能体

2. **扩展查询规划能力**：

   - 修改`query_planner_agent.py`中的提示词
   - 添加新的融合策略或权重计算方法

3. **增强结果处理**：
   - 在`_fuse_results()`方法中添加新的融合算法
   - 扩展`SearchResultItem`模型以包含更多元数据

## 7. 常见问题与排错

### 7.1 连接问题

- **数据库连接失败**：确保 Milvus、PostgreSQL 和 Neo4j 服务正在运行，并检查配置中的连接参数
- **LLM API 错误**：验证 API 密钥是否有效，网络连接是否正常

### 7.2 查询问题

- **查询计划生成失败**：可能是 LLM 响应格式不正确，检查系统提示词
- **查询结果为空**：检查各步骤的中间结果，可能是某个过滤条件太严格

### 7.3 性能优化

- 对于复杂查询，可以增加缓存机制
- 优化向量检索参数，如索引类型和搜索算法
- 调整结果融合策略和权重，提高相关性
