# Neo4j with Graph Data Science and APOC

This directory contains Docker Compose configurations for running Neo4j with Graph Data Science (GDS) and APOC plugins.

## Features

- Neo4j latest version
- Graph Data Science library included
- APOC (Awesome Procedures on Cypher) plugin included
- Data persistence with local volumes
- No authentication (suitable for development)

## Files

- `docker-compose-windows.yml` - For Windows systems
- `docker-compose-linux.yml` - For Linux systems

## Usage

### Windows

```powershell
docker-compose -f docker-compose-windows.yml up -d
```

### Linux

```bash
docker-compose -f docker-compose-linux.yml up -d
```

## Volumes

The following directories will be created for data persistence:

- `data/` - Neo4j database files
- `logs/` - Neo4j log files
- `import/` - Files for import operations
- `plugins/` - Additional plugins
- `conf/` - Configuration files

## Access

- Neo4j Browser: http://localhost:7474
- Bolt connection: bolt://localhost:7687

## Environment Variables

- `NEO4J_AUTH=none` - Disables authentication
- `NEO4J_PLUGINS=["graph-data-science","apoc"]` - Enables GDS and APOC plugins
- Security settings allow unrestricted access to GDS and APOC procedures

## Stopping

```bash
docker-compose down
```

To also remove volumes:

```bash
docker-compose down -v
```
