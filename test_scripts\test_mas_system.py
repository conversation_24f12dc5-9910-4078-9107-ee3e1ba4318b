#!/usr/bin/env python3
"""
多智能体系统测试
测试文字输入、图片输入和混合输入三种情况
"""

import asyncio
import sys
import os
import numpy as np
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    from src.agent import create_mas_system
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有依赖包并正确配置了Python路径")
    sys.exit(1)


async def test_text_input():
    """测试1: 纯文字输入"""
    print("\n" + "="*60)
    print("测试1: 纯文字输入")
    print("="*60)
    
    mas = await create_mas_system()
    
    try:
        # 测试文字查询
        text_query = "查找铝合金材料的发动机零件"
        
        print(f"输入文字: {text_query}")
        print("处理中...")
        
        result = await mas.query(text=text_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  查询ID: {result['query_id']}")
        print(f"  执行时间: {result['metadata']['execution_time']:.2f}秒")
        print(f"  输入模态: {result['metadata']['input_modality']}")
        print(f"  查询意图: {result['metadata']['query_intent']}")
        print(f"  结果数量: {result['metadata']['result_count']}")
        
        if result['success'] and result['results']:
            print(f"\n前3个结果:")
            for i, res in enumerate(result['results'][:3]):
                print(f"    结果{i+1}: {res}")
        elif not result['success']:
            print(f"  错误: {result['error']}")
    
    finally:
        await mas.stop()


async def test_image_input():
    """测试2: 纯图片输入"""
    print("\n" + "="*60)
    print("测试2: 纯图片输入")
    print("="*60)
    
    mas = await create_mas_system()
    
    try:
        # 模拟图片的shape_embedding向量
        mock_shape_embedding = np.random.rand(768).tolist()
        
        print(f"输入图片: shape_embedding向量 (维度: {len(mock_shape_embedding)})")
        print("处理中...")
        
        result = await mas.query(image=mock_shape_embedding)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  查询ID: {result['query_id']}")
        print(f"  执行时间: {result['metadata']['execution_time']:.2f}秒")
        print(f"  输入模态: {result['metadata']['input_modality']}")
        print(f"  查询意图: {result['metadata']['query_intent']}")
        print(f"  结果数量: {result['metadata']['result_count']}")
        
        if result['success'] and result['results']:
            print(f"\n前3个结果:")
            for i, res in enumerate(result['results'][:3]):
                print(f"    结果{i+1}: {res}")
        elif not result['success']:
            print(f"  错误: {result['error']}")
    
    finally:
        await mas.stop()


async def test_mixed_input():
    """测试3: 文字+图片混合输入"""
    print("\n" + "="*60)
    print("测试3: 文字+图片混合输入")
    print("="*60)
    
    mas = await create_mas_system()
    
    try:
        # 混合输入
        text_query = "查找相似形状的汽车零件"
        mock_shape_embedding = np.random.rand(768).tolist()
        
        print(f"输入文字: {text_query}")
        print(f"输入图片: shape_embedding向量 (维度: {len(mock_shape_embedding)})")
        print("处理中...")
        
        result = await mas.query(text=text_query, image=mock_shape_embedding)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  查询ID: {result['query_id']}")
        print(f"  执行时间: {result['metadata']['execution_time']:.2f}秒")
        print(f"  输入模态: {result['metadata']['input_modality']}")
        print(f"  查询意图: {result['metadata']['query_intent']}")
        print(f"  结果数量: {result['metadata']['result_count']}")
        
        if result['success'] and result['results']:
            print(f"\n前3个结果:")
            for i, res in enumerate(result['results'][:3]):
                print(f"    结果{i+1}: {res}")
        elif not result['success']:
            print(f"  错误: {result['error']}")
    
    finally:
        await mas.stop()


async def test_invalid_input():
    """测试4: 无效输入（两者都为空）"""
    print("\n" + "="*60)
    print("测试4: 无效输入测试")
    print("="*60)
    
    mas = await create_mas_system()
    
    try:
        print("输入: 文字和图片都为空")
        print("处理中...")
        
        result = await mas.query()
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  错误: {result['error']}")
    
    except ValueError as e:
        print(f"\n预期的错误: {e}")
    
    finally:
        await mas.stop()


async def main():
    """运行所有测试"""
    print("多智能体系统测试")
    print("测试文字输入、图片输入和混合输入")
    print("="*60)
    
    tests = [
        test_text_input,
        test_image_input,
        test_mixed_input,
        test_invalid_input
    ]
    
    for test in tests:
        try:
            await test()
            await asyncio.sleep(1)  # 短暂暂停
        except Exception as e:
            print(f"\n测试执行失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "="*60)
    print("所有测试完成")
    print("\n系统功能验证:")
    print("✅ 主入口函数: query(text, image)")
    print("✅ 输入验证: 文字和图片不能同时为空")
    print("✅ 文字输入: 使用LLM进行查询分解")
    print("✅ 图片输入: 直接进行shape_embedding检索")
    print("✅ 混合输入: 结合文字和图片信息")
    print("✅ 错误处理: 优雅的错误处理和回退机制")


if __name__ == "__main__":
    print("启动多智能体系统测试...")
    print("请确保Neo4j数据库正在运行并包含CAD数据")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n\n💥 测试执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 测试完成!")
