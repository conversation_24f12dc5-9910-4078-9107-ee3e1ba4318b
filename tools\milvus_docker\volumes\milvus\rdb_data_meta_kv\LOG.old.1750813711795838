2025/06/24-00:35:22.739258 46 RocksDB version: 6.29.5
2025/06/24-00:35:22.739944 46 Git sha 0
2025/06/24-00:35:22.739948 46 Compile date 2024-11-15 11:22:58
2025/06/24-00:35:22.739968 46 DB SUMMARY
2025/06/24-00:35:22.739970 46 DB Session ID:  RQ8MAS9M7DG6HMJAHPA2
2025/06/24-00:35:22.741140 46 CURRENT file:  CURRENT
2025/06/24-00:35:22.741145 46 IDENTITY file:  IDENTITY
2025/06/24-00:35:22.741657 46 MANIFEST file:  MANIFEST-000020 size: 445 Bytes
2025/06/24-00:35:22.741663 46 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 3, files: 000009.sst 000013.sst 000019.sst 
2025/06/24-00:35:22.741667 46 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000021.log size: 39977417 ; 
2025/06/24-00:35:22.741671 46                         Options.error_if_exists: 0
2025/06/24-00:35:22.741672 46                       Options.create_if_missing: 1
2025/06/24-00:35:22.741673 46                         Options.paranoid_checks: 1
2025/06/24-00:35:22.741674 46             Options.flush_verify_memtable_count: 1
2025/06/24-00:35:22.741675 46                               Options.track_and_verify_wals_in_manifest: 0
2025/06/24-00:35:22.741675 46                                     Options.env: 0x7f412647bd00
2025/06/24-00:35:22.741677 46                                      Options.fs: PosixFileSystem
2025/06/24-00:35:22.741678 46                                Options.info_log: 0x7f4027690050
2025/06/24-00:35:22.741678 46                Options.max_file_opening_threads: 16
2025/06/24-00:35:22.741679 46                              Options.statistics: (nil)
2025/06/24-00:35:22.741680 46                               Options.use_fsync: 0
2025/06/24-00:35:22.741681 46                       Options.max_log_file_size: 0
2025/06/24-00:35:22.741682 46                  Options.max_manifest_file_size: 1073741824
2025/06/24-00:35:22.741683 46                   Options.log_file_time_to_roll: 0
2025/06/24-00:35:22.741683 46                       Options.keep_log_file_num: 1000
2025/06/24-00:35:22.741684 46                    Options.recycle_log_file_num: 0
2025/06/24-00:35:22.741685 46                         Options.allow_fallocate: 1
2025/06/24-00:35:22.741686 46                        Options.allow_mmap_reads: 0
2025/06/24-00:35:22.741687 46                       Options.allow_mmap_writes: 0
2025/06/24-00:35:22.741687 46                        Options.use_direct_reads: 0
2025/06/24-00:35:22.741688 46                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/24-00:35:22.741689 46          Options.create_missing_column_families: 0
2025/06/24-00:35:22.741690 46                              Options.db_log_dir: 
2025/06/24-00:35:22.741690 46                                 Options.wal_dir: 
2025/06/24-00:35:22.741691 46                Options.table_cache_numshardbits: 6
2025/06/24-00:35:22.741692 46                         Options.WAL_ttl_seconds: 0
2025/06/24-00:35:22.741693 46                       Options.WAL_size_limit_MB: 0
2025/06/24-00:35:22.741694 46                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/24-00:35:22.741694 46             Options.manifest_preallocation_size: 4194304
2025/06/24-00:35:22.741695 46                     Options.is_fd_close_on_exec: 1
2025/06/24-00:35:22.741696 46                   Options.advise_random_on_open: 1
2025/06/24-00:35:22.741697 46                   Options.experimental_mempurge_threshold: 0.000000
2025/06/24-00:35:22.741738 46                    Options.db_write_buffer_size: 0
2025/06/24-00:35:22.741741 46                    Options.write_buffer_manager: 0x7f402a2400a0
2025/06/24-00:35:22.741742 46         Options.access_hint_on_compaction_start: 1
2025/06/24-00:35:22.741743 46  Options.new_table_reader_for_compaction_inputs: 0
2025/06/24-00:35:22.741744 46           Options.random_access_max_buffer_size: 1048576
2025/06/24-00:35:22.741745 46                      Options.use_adaptive_mutex: 0
2025/06/24-00:35:22.741746 46                            Options.rate_limiter: (nil)
2025/06/24-00:35:22.741757 46     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/24-00:35:22.741758 46                       Options.wal_recovery_mode: 2
2025/06/24-00:35:22.742223 46                  Options.enable_thread_tracking: 0
2025/06/24-00:35:22.742227 46                  Options.enable_pipelined_write: 0
2025/06/24-00:35:22.742228 46                  Options.unordered_write: 0
2025/06/24-00:35:22.742228 46         Options.allow_concurrent_memtable_write: 1
2025/06/24-00:35:22.742228 46      Options.enable_write_thread_adaptive_yield: 1
2025/06/24-00:35:22.742229 46             Options.write_thread_max_yield_usec: 100
2025/06/24-00:35:22.742229 46            Options.write_thread_slow_yield_usec: 3
2025/06/24-00:35:22.742230 46                               Options.row_cache: None
2025/06/24-00:35:22.742230 46                              Options.wal_filter: None
2025/06/24-00:35:22.742231 46             Options.avoid_flush_during_recovery: 0
2025/06/24-00:35:22.742232 46             Options.allow_ingest_behind: 0
2025/06/24-00:35:22.742232 46             Options.preserve_deletes: 0
2025/06/24-00:35:22.742233 46             Options.two_write_queues: 0
2025/06/24-00:35:22.742233 46             Options.manual_wal_flush: 0
2025/06/24-00:35:22.742233 46             Options.atomic_flush: 0
2025/06/24-00:35:22.742234 46             Options.avoid_unnecessary_blocking_io: 0
2025/06/24-00:35:22.742234 46                 Options.persist_stats_to_disk: 0
2025/06/24-00:35:22.742235 46                 Options.write_dbid_to_manifest: 0
2025/06/24-00:35:22.742235 46                 Options.log_readahead_size: 0
2025/06/24-00:35:22.742236 46                 Options.file_checksum_gen_factory: Unknown
2025/06/24-00:35:22.742236 46                 Options.best_efforts_recovery: 0
2025/06/24-00:35:22.742237 46                Options.max_bgerror_resume_count: 2147483647
2025/06/24-00:35:22.742237 46            Options.bgerror_resume_retry_interval: 1000000
2025/06/24-00:35:22.742238 46             Options.allow_data_in_errors: 0
2025/06/24-00:35:22.742238 46             Options.db_host_id: __hostname__
2025/06/24-00:35:22.742240 46             Options.max_background_jobs: 2
2025/06/24-00:35:22.742241 46             Options.max_background_compactions: -1
2025/06/24-00:35:22.742241 46             Options.max_subcompactions: 1
2025/06/24-00:35:22.742242 46             Options.avoid_flush_during_shutdown: 0
2025/06/24-00:35:22.742242 46           Options.writable_file_max_buffer_size: 1048576
2025/06/24-00:35:22.742243 46             Options.delayed_write_rate : 16777216
2025/06/24-00:35:22.742243 46             Options.max_total_wal_size: 0
2025/06/24-00:35:22.742244 46             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/24-00:35:22.742244 46                   Options.stats_dump_period_sec: 600
2025/06/24-00:35:22.742244 46                 Options.stats_persist_period_sec: 600
2025/06/24-00:35:22.742245 46                 Options.stats_history_buffer_size: 1048576
2025/06/24-00:35:22.742245 46                          Options.max_open_files: -1
2025/06/24-00:35:22.742246 46                          Options.bytes_per_sync: 0
2025/06/24-00:35:22.742246 46                      Options.wal_bytes_per_sync: 0
2025/06/24-00:35:22.742247 46                   Options.strict_bytes_per_sync: 0
2025/06/24-00:35:22.742247 46       Options.compaction_readahead_size: 0
2025/06/24-00:35:22.742248 46                  Options.max_background_flushes: 1
2025/06/24-00:35:22.742248 46 Compression algorithms supported:
2025/06/24-00:35:22.742250 46 	kZSTD supported: 1
2025/06/24-00:35:22.742250 46 	kXpressCompression supported: 0
2025/06/24-00:35:22.742251 46 	kBZip2Compression supported: 0
2025/06/24-00:35:22.742252 46 	kZSTDNotFinalCompression supported: 1
2025/06/24-00:35:22.742252 46 	kLZ4Compression supported: 0
2025/06/24-00:35:22.742253 46 	kZlibCompression supported: 0
2025/06/24-00:35:22.742253 46 	kLZ4HCCompression supported: 0
2025/06/24-00:35:22.742254 46 	kSnappyCompression supported: 0
2025/06/24-00:35:22.742257 46 Fast CRC32 supported: Not supported on x86
2025/06/24-00:35:22.794264 46 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000020
2025/06/24-00:35:22.800473 46 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/06/24-00:35:22.800483 46               Options.comparator: leveldb.BytewiseComparator
2025/06/24-00:35:22.800484 46           Options.merge_operator: None
2025/06/24-00:35:22.800485 46        Options.compaction_filter: None
2025/06/24-00:35:22.800485 46        Options.compaction_filter_factory: None
2025/06/24-00:35:22.800485 46  Options.sst_partitioner_factory: None
2025/06/24-00:35:22.800486 46         Options.memtable_factory: SkipListFactory
2025/06/24-00:35:22.800487 46            Options.table_factory: BlockBasedTable
2025/06/24-00:35:22.800508 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f402a3000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f402a240010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/24-00:35:22.800509 46        Options.write_buffer_size: 67108864
2025/06/24-00:35:22.800510 46  Options.max_write_buffer_number: 2
2025/06/24-00:35:22.800511 46        Options.compression[0]: NoCompression
2025/06/24-00:35:22.800512 46        Options.compression[1]: NoCompression
2025/06/24-00:35:22.800512 46        Options.compression[2]: ZSTD
2025/06/24-00:35:22.800513 46        Options.compression[3]: ZSTD
2025/06/24-00:35:22.800513 46        Options.compression[4]: ZSTD
2025/06/24-00:35:22.800514 46                  Options.bottommost_compression: Disabled
2025/06/24-00:35:22.800514 46       Options.prefix_extractor: nullptr
2025/06/24-00:35:22.800515 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/24-00:35:22.800515 46             Options.num_levels: 5
2025/06/24-00:35:22.800516 46        Options.min_write_buffer_number_to_merge: 1
2025/06/24-00:35:22.800516 46     Options.max_write_buffer_number_to_maintain: 0
2025/06/24-00:35:22.800517 46     Options.max_write_buffer_size_to_maintain: 0
2025/06/24-00:35:22.800517 46            Options.bottommost_compression_opts.window_bits: -14
2025/06/24-00:35:22.800518 46                  Options.bottommost_compression_opts.level: 32767
2025/06/24-00:35:22.800518 46               Options.bottommost_compression_opts.strategy: 0
2025/06/24-00:35:22.800519 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/24-00:35:22.800519 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/24-00:35:22.800520 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/24-00:35:22.800520 46                  Options.bottommost_compression_opts.enabled: false
2025/06/24-00:35:22.800521 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/24-00:35:22.800521 46            Options.compression_opts.window_bits: -14
2025/06/24-00:35:22.800522 46                  Options.compression_opts.level: 32767
2025/06/24-00:35:22.800522 46               Options.compression_opts.strategy: 0
2025/06/24-00:35:22.800523 46         Options.compression_opts.max_dict_bytes: 0
2025/06/24-00:35:22.800523 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/24-00:35:22.800688 46         Options.compression_opts.parallel_threads: 1
2025/06/24-00:35:22.800693 46                  Options.compression_opts.enabled: false
2025/06/24-00:35:22.800693 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/24-00:35:22.800694 46      Options.level0_file_num_compaction_trigger: 4
2025/06/24-00:35:22.800695 46          Options.level0_slowdown_writes_trigger: 20
2025/06/24-00:35:22.800695 46              Options.level0_stop_writes_trigger: 36
2025/06/24-00:35:22.800696 46                   Options.target_file_size_base: 67108864
2025/06/24-00:35:22.800696 46             Options.target_file_size_multiplier: 2
2025/06/24-00:35:22.800697 46                Options.max_bytes_for_level_base: 268435456
2025/06/24-00:35:22.800697 46 Options.level_compaction_dynamic_level_bytes: 0
2025/06/24-00:35:22.800698 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/24-00:35:22.800700 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/24-00:35:22.800701 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/24-00:35:22.800701 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/24-00:35:22.800702 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/24-00:35:22.800702 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/24-00:35:22.800702 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/24-00:35:22.800703 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/24-00:35:22.800703 46       Options.max_sequential_skip_in_iterations: 8
2025/06/24-00:35:22.800704 46                    Options.max_compaction_bytes: 1677721600
2025/06/24-00:35:22.800704 46                        Options.arena_block_size: 1048576
2025/06/24-00:35:22.800705 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/24-00:35:22.800705 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/24-00:35:22.800706 46       Options.rate_limit_delay_max_milliseconds: 100
2025/06/24-00:35:22.800706 46                Options.disable_auto_compactions: 0
2025/06/24-00:35:22.800709 46                        Options.compaction_style: kCompactionStyleLevel
2025/06/24-00:35:22.800710 46                          Options.compaction_pri: kMinOverlappingRatio
2025/06/24-00:35:22.800710 46 Options.compaction_options_universal.size_ratio: 1
2025/06/24-00:35:22.800710 46 Options.compaction_options_universal.min_merge_width: 2
2025/06/24-00:35:22.800711 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/24-00:35:22.800711 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/24-00:35:22.800712 46 Options.compaction_options_universal.compression_size_percent: -1
2025/06/24-00:35:22.800713 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/24-00:35:22.800713 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/24-00:35:22.800714 46 Options.compaction_options_fifo.allow_compaction: 0
2025/06/24-00:35:22.800718 46                   Options.table_properties_collectors: 
2025/06/24-00:35:22.800719 46                   Options.inplace_update_support: 0
2025/06/24-00:35:22.800719 46                 Options.inplace_update_num_locks: 10000
2025/06/24-00:35:22.800720 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/24-00:35:22.800720 46               Options.memtable_whole_key_filtering: 0
2025/06/24-00:35:22.800721 46   Options.memtable_huge_page_size: 0
2025/06/24-00:35:22.800721 46                           Options.bloom_locality: 0
2025/06/24-00:35:22.800722 46                    Options.max_successive_merges: 0
2025/06/24-00:35:22.800722 46                Options.optimize_filters_for_hits: 0
2025/06/24-00:35:22.800723 46                Options.paranoid_file_checks: 0
2025/06/24-00:35:22.800723 46                Options.force_consistency_checks: 1
2025/06/24-00:35:22.800724 46                Options.report_bg_io_stats: 0
2025/06/24-00:35:22.800724 46                               Options.ttl: 2592000
2025/06/24-00:35:22.800873 46          Options.periodic_compaction_seconds: 0
2025/06/24-00:35:22.800874 46                       Options.enable_blob_files: false
2025/06/24-00:35:22.800875 46                           Options.min_blob_size: 0
2025/06/24-00:35:22.800875 46                          Options.blob_file_size: 268435456
2025/06/24-00:35:22.800876 46                   Options.blob_compression_type: NoCompression
2025/06/24-00:35:22.800877 46          Options.enable_blob_garbage_collection: false
2025/06/24-00:35:22.800877 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/24-00:35:22.800878 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/24-00:35:22.800879 46          Options.blob_compaction_readahead_size: 0
2025/06/24-00:35:22.812576 46 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000020 succeeded,manifest_file_number is 20, next_file_number is 22, last_sequence is 1024749, log_number is 16,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 8
2025/06/24-00:35:22.812584 46 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 16
2025/06/24-00:35:22.820637 46 [db/version_set.cc:4409] Creating manifest 24
2025/06/24-00:35:23.061676 46 EVENT_LOG_v1 {"time_micros": 1750725323061660, "job": 1, "event": "recovery_started", "wal_files": [21]}
2025/06/24-00:35:23.061683 46 [db/db_impl/db_impl_open.cc:888] Recovering log #21 mode 2
2025/06/24-00:35:25.435332 46 EVENT_LOG_v1 {"time_micros": 1750725325435288, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 25, "file_size": 1484, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 500, "index_size": 67, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 717, "raw_average_key_size": 55, "raw_value_size": 114, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 13, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750725325, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "70bc45a2-e82b-4473-bc30-6ae251c53e8f", "db_session_id": "RQ8MAS9M7DG6HMJAHPA2", "orig_file_number": 25}}
2025/06/24-00:35:25.435740 46 [db/version_set.cc:4409] Creating manifest 26
2025/06/24-00:35:25.495181 46 EVENT_LOG_v1 {"time_micros": 1750725325495172, "job": 1, "event": "recovery_finished"}
2025/06/24-00:35:25.566666 46 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000021.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-00:35:25.567267 46 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f4027790000
2025/06/24-00:35:25.568525 46 DB pointer 0x7f4027620000
2025/06/24-00:35:25.569012 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-00:35:25.569023 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2.8 total, 2.8 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2.8 total, 2.8 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 1 last_copies: 0 last_secs: 4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-00:45:25.575660 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-00:45:25.576396 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 602.8 total, 600.0 interval
Cumulative writes: 8575 writes, 8579 keys, 5821 commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8575 writes, 0 syncs, 8575.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8575 writes, 8579 keys, 5821 commit groups, 1.5 writes per commit group, ingest: 0.45 MB, 0.00 MB/s
Interval WAL: 8575 writes, 0 syncs, 8575.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 602.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 2 last_copies: 0 last_secs: 0.002242 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-00:55:25.585536 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-00:55:25.586411 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1202.8 total, 600.0 interval
Cumulative writes: 17K writes, 17K keys, 11K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17K writes, 0 syncs, 17604.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9029 writes, 9035 keys, 6084 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9029 writes, 0 syncs, 9029.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1202.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 3 last_copies: 0 last_secs: 0.002132 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-01:05:25.597039 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:05:25.597448 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1802.8 total, 600.0 interval
Cumulative writes: 26K writes, 26K keys, 18K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26K writes, 0 syncs, 26595.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8993 keys, 6099 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1802.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 4 last_copies: 0 last_secs: 0.003449 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-01:15:25.615110 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:15:25.615556 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2402.8 total, 600.0 interval
Cumulative writes: 35K writes, 35K keys, 24K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 35K writes, 0 syncs, 35556.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8961 writes, 8961 keys, 6118 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8961 writes, 0 syncs, 8961.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2402.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 5 last_copies: 0 last_secs: 0.005305 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-01:25:25.628739 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:25:25.629339 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3002.8 total, 600.0 interval
Cumulative writes: 44K writes, 44K keys, 30K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 44K writes, 0 syncs, 44549.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8993 writes, 8993 keys, 6141 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8993 writes, 0 syncs, 8993.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3002.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 6 last_copies: 0 last_secs: 0.004427 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-01:35:25.638049 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:35:25.638385 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3602.8 total, 600.0 interval
Cumulative writes: 53K writes, 53K keys, 36K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 53K writes, 0 syncs, 53535.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8986 writes, 8986 keys, 6053 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8986 writes, 0 syncs, 8986.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3602.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 7 last_copies: 0 last_secs: 0.002577 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-01:45:25.653887 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:45:25.655096 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4202.8 total, 600.0 interval
Cumulative writes: 62K writes, 62K keys, 42K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 62K writes, 0 syncs, 62514.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8979 writes, 8979 keys, 5947 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8979 writes, 0 syncs, 8979.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4202.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 8 last_copies: 0 last_secs: 0.005336 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-01:55:25.670514 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:55:25.671022 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4802.9 total, 600.0 interval
Cumulative writes: 71K writes, 71K keys, 48K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 71K writes, 0 syncs, 71505.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 5771 commit groups, 1.6 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4802.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 9 last_copies: 0 last_secs: 0.004364 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-02:05:25.683824 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:05:25.684425 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5402.9 total, 600.0 interval
Cumulative writes: 80K writes, 80K keys, 53K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 80K writes, 0 syncs, 80493.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8988 writes, 8988 keys, 5842 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8988 writes, 0 syncs, 8988.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5402.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 10 last_copies: 0 last_secs: 0.005804 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-02:15:25.694197 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:15:25.695654 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6002.9 total, 600.0 interval
Cumulative writes: 89K writes, 89K keys, 59K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 89K writes, 0 syncs, 89490.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 6110 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6002.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 11 last_copies: 0 last_secs: 0.00304 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-02:25:25.705739 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:25:25.706184 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6602.9 total, 600.0 interval
Cumulative writes: 98K writes, 98K keys, 66K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 98K writes, 0 syncs, 98481.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 6143 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6602.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 12 last_copies: 0 last_secs: 0.00261 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-02:35:25.716237 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:35:25.716819 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7202.9 total, 600.0 interval
Cumulative writes: 107K writes, 107K keys, 72K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 107K writes, 0 syncs, 107481.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6158 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7202.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 13 last_copies: 0 last_secs: 0.002747 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-02:45:25.727833 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:45:25.728226 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7802.9 total, 600.0 interval
Cumulative writes: 116K writes, 116K keys, 78K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 116K writes, 0 syncs, 116478.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 6104 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7802.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 14 last_copies: 0 last_secs: 0.002767 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-02:55:25.737614 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:55:25.738929 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8402.9 total, 600.0 interval
Cumulative writes: 125K writes, 125K keys, 84K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 125K writes, 0 syncs, 125469.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 6077 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8402.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 15 last_copies: 0 last_secs: 0.002543 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-03:05:25.755734 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:05:25.756477 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9002.9 total, 600.0 interval
Cumulative writes: 134K writes, 134K keys, 90K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 134K writes, 0 syncs, 134469.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5983 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9003.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 16 last_copies: 0 last_secs: 0.005569 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-03:15:25.767071 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:15:25.769348 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9603.0 total, 600.0 interval
Cumulative writes: 143K writes, 143K keys, 96K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 143K writes, 0 syncs, 143469.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5938 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9603.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 17 last_copies: 0 last_secs: 0.003064 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-03:25:25.778838 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:25:25.779369 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10203.0 total, 600.0 interval
Cumulative writes: 152K writes, 152K keys, 102K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 152K writes, 0 syncs, 152454.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8985 writes, 8985 keys, 6148 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8985 writes, 0 syncs, 8985.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10203.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 18 last_copies: 0 last_secs: 0.002372 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-03:35:25.796030 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:35:25.798607 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10803.0 total, 600.0 interval
Cumulative writes: 161K writes, 161K keys, 108K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 161K writes, 0 syncs, 161415.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8961 writes, 8961 keys, 6086 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8961 writes, 0 syncs, 8961.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10803.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 19 last_copies: 0 last_secs: 0.005232 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-03:45:25.808930 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:45:25.811626 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11403.0 total, 600.0 interval
Cumulative writes: 170K writes, 170K keys, 114K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 170K writes, 0 syncs, 170429.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9014 writes, 9014 keys, 6010 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9014 writes, 0 syncs, 9014.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11403.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 20 last_copies: 0 last_secs: 0.002694 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(23,5.31 MB,0.557987%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-03:55:25.815201 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:55:25.815862 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12003.0 total, 600.0 interval
Cumulative writes: 179K writes, 179K keys, 121K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 179K writes, 0 syncs, 179524.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9095 writes, 9095 keys, 6430 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9095 writes, 0 syncs, 9095.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12003.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 21 last_copies: 0 last_secs: 0.000902 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(27,6.94 MB,0.728716%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-04:05:25.822452 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:05:25.822774 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12603.0 total, 600.0 interval
Cumulative writes: 188K writes, 188K keys, 127K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 188K writes, 0 syncs, 188657.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9133 writes, 9135 keys, 6200 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9133 writes, 0 syncs, 9133.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12603.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 22 last_copies: 0 last_secs: 0.001946 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(27,6.94 MB,0.728716%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-04:15:25.829811 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:15:25.832459 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13203.0 total, 600.0 interval
Cumulative writes: 197K writes, 197K keys, 133K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 197K writes, 0 syncs, 197773.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9116 writes, 9118 keys, 6270 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9116 writes, 0 syncs, 9116.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13203.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 23 last_copies: 0 last_secs: 0.001505 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(29,7.75 MB,0.81408%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-04:25:25.834013 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:25:25.834172 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13803.0 total, 600.0 interval
Cumulative writes: 206K writes, 206K keys, 139K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 206K writes, 0 syncs, 206888.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9115 writes, 9115 keys, 6180 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9115 writes, 0 syncs, 9115.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13803.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 24 last_copies: 0 last_secs: 0.0004 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(31,8.56 MB,0.899445%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-04:35:25.834888 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:35:25.835869 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 14403.0 total, 600.0 interval
Cumulative writes: 215K writes, 216K keys, 145K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 215K writes, 0 syncs, 215989.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9101 writes, 9103 keys, 6219 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9101 writes, 0 syncs, 9101.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14403.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 25 last_copies: 0 last_secs: 6.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(31,8.56 MB,0.899445%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-04:45:25.838864 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:45:25.851099 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15003.0 total, 600.0 interval
Cumulative writes: 225K writes, 225K keys, 152K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 225K writes, 0 syncs, 225041.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9052 writes, 9052 keys, 6204 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9052 writes, 0 syncs, 9052.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15003.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 26 last_copies: 0 last_secs: 0.000865 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-04:55:25.878869 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:55:25.880481 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15603.1 total, 600.0 interval
Cumulative writes: 234K writes, 234K keys, 158K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 234K writes, 0 syncs, 234043.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9002 writes, 9004 keys, 5935 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9002 writes, 0 syncs, 9002.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15603.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 27 last_copies: 0 last_secs: 0.00725 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-05:05:25.893722 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:05:25.894025 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16203.1 total, 600.0 interval
Cumulative writes: 243K writes, 243K keys, 164K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 243K writes, 0 syncs, 243056.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9013 writes, 9013 keys, 6024 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9013 writes, 0 syncs, 9013.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16203.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 28 last_copies: 0 last_secs: 0.003949 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-05:15:25.904667 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:15:25.905172 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16803.1 total, 600.0 interval
Cumulative writes: 252K writes, 252K keys, 170K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 252K writes, 0 syncs, 252056.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6140 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16803.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 29 last_copies: 0 last_secs: 0.00343 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-05:25:25.912701 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:25:25.913554 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 17403.1 total, 600.0 interval
Cumulative writes: 261K writes, 261K keys, 176K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 261K writes, 0 syncs, 261056.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6092 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17403.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 30 last_copies: 0 last_secs: 0.002621 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-05:35:25.922933 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:35:25.923346 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18003.1 total, 600.0 interval
Cumulative writes: 270K writes, 270K keys, 182K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 270K writes, 0 syncs, 270056.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5830 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18003.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 31 last_copies: 0 last_secs: 0.003013 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-05:45:25.936973 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:45:25.941274 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18603.1 total, 600.0 interval
Cumulative writes: 279K writes, 279K keys, 188K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 279K writes, 0 syncs, 279056.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5917 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18603.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 32 last_copies: 0 last_secs: 0.004023 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-05:55:25.953716 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:55:25.954503 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19203.1 total, 600.0 interval
Cumulative writes: 288K writes, 288K keys, 193K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 288K writes, 0 syncs, 288056.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5714 commit groups, 1.6 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 33 last_copies: 0 last_secs: 0.004797 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-06:05:25.963649 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:05:25.964260 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19803.2 total, 600.0 interval
Cumulative writes: 297K writes, 297K keys, 199K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 297K writes, 0 syncs, 297056.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5718 commit groups, 1.6 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 34 last_copies: 0 last_secs: 0.002849 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-06:15:25.972654 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:15:25.973147 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20403.2 total, 600.0 interval
Cumulative writes: 306K writes, 306K keys, 205K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 306K writes, 0 syncs, 306056.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5756 commit groups, 1.6 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 35 last_copies: 0 last_secs: 0.002629 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-06:25:25.981965 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:25:25.982493 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21003.2 total, 600.0 interval
Cumulative writes: 315K writes, 315K keys, 211K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 315K writes, 0 syncs, 315059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9003 writes, 9003 keys, 5820 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 9003 writes, 0 syncs, 9003.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 36 last_copies: 0 last_secs: 0.00284 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-06:35:25.992727 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:35:25.993244 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21603.2 total, 600.0 interval
Cumulative writes: 324K writes, 324K keys, 217K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 324K writes, 0 syncs, 324059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6108 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 37 last_copies: 0 last_secs: 0.003474 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-06:45:26.005738 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:45:26.006408 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22203.2 total, 600.0 interval
Cumulative writes: 333K writes, 333K keys, 223K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 333K writes, 0 syncs, 333059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5991 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 38 last_copies: 0 last_secs: 0.004495 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-06:55:26.016798 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:55:26.056646 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22803.2 total, 600.0 interval
Cumulative writes: 342K writes, 342K keys, 229K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 342K writes, 0 syncs, 342059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6006 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 39 last_copies: 0 last_secs: 0.003285 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-07:05:26.066324 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:05:26.066617 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 23403.3 total, 600.1 interval
Cumulative writes: 351K writes, 351K keys, 235K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 351K writes, 0 syncs, 351059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6170 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 23403.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 40 last_copies: 0 last_secs: 0.002939 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-07:15:26.075084 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:15:26.075344 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24003.3 total, 600.0 interval
Cumulative writes: 360K writes, 360K keys, 241K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 360K writes, 0 syncs, 360059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5971 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24003.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 41 last_copies: 0 last_secs: 0.002734 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-07:25:26.089413 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:25:26.090039 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24603.3 total, 600.0 interval
Cumulative writes: 369K writes, 369K keys, 247K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 369K writes, 0 syncs, 369059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6020 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24603.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 42 last_copies: 0 last_secs: 0.004856 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-07:35:26.100667 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:35:26.101197 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 25203.3 total, 600.0 interval
Cumulative writes: 378K writes, 378K keys, 253K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 378K writes, 0 syncs, 378059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5981 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25203.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 43 last_copies: 0 last_secs: 0.003347 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-07:45:26.109231 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:45:26.135875 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 25803.3 total, 600.0 interval
Cumulative writes: 387K writes, 387K keys, 259K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 387K writes, 0 syncs, 387059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5830 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25803.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 44 last_copies: 0 last_secs: 0.002438 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-07:55:26.146088 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:55:26.146495 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 26403.3 total, 600.0 interval
Cumulative writes: 396K writes, 396K keys, 265K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 396K writes, 0 syncs, 396059.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5842 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 26403.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 45 last_copies: 0 last_secs: 0.003212 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-08:05:26.155842 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-08:05:26.156609 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27003.4 total, 600.0 interval
Cumulative writes: 405K writes, 405K keys, 270K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 405K writes, 0 syncs, 405050.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 5742 commit groups, 1.6 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27003.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 46 last_copies: 0 last_secs: 0.002973 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/24-08:15:26.166355 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-08:15:26.167327 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27603.4 total, 600.0 interval
Cumulative writes: 414K writes, 414K keys, 276K commit groups, 1.5 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 414K writes, 0 syncs, 414050.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 5768 commit groups, 1.6 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.57 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.076       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27603.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 47 last_copies: 0 last_secs: 0.002975 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
