# CAD知识图谱多智能体系统

## 系统概述

本系统提供一个简洁的多智能体架构，用于CAD知识图谱的多模态查询。系统支持**文字**和**图片**两种输入模态，能够智能识别查询意图并返回相关结果。

## 核心特性

- **主入口函数**: `query(text, image)` - 统一的查询接口
- **多模态支持**: 文字输入、图片输入、混合输入
- **智能分解**: 使用LLM进行文字查询的智能分解
- **直接检索**: 图片输入直接进行shape_embedding相似性搜索
- **输入验证**: 文字和图片不能同时为空

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# LLM配置
export LLM_PROVIDER=openai
export LLM_API_KEY=your-api-key
export LLM_MODEL=gpt-3.5-turbo

# Neo4j配置
export NEO4J_URI=bolt://localhost:7687
export NEO4J_USER=neo4j
export NEO4J_PASSWORD=your-password
```

### 3. 基本使用

```python
import asyncio
from src.agent import create_mas_system

async def main():
    # 创建并启动系统
    mas = await create_mas_system()
    
    try:
        # 文字查询
        result1 = await mas.query(text="查找铝合金材料的发动机零件")
        
        # 图片查询
        import numpy as np
        shape_embedding = np.random.rand(768).tolist()
        result2 = await mas.query(image=shape_embedding)
        
        # 混合查询
        result3 = await mas.query(
            text="查找相似形状的汽车零件", 
            image=shape_embedding
        )
        
        print(f"文字查询结果: {len(result1['results'])} 个")
        print(f"图片查询结果: {len(result2['results'])} 个")
        print(f"混合查询结果: {len(result3['results'])} 个")
        
    finally:
        await mas.stop()

asyncio.run(main())
```

## API 文档

### 主入口函数

```python
async def query(text: Optional[str] = None, 
               image: Optional[List[float]] = None) -> Dict[str, Any]
```

**参数**:
- `text`: 文字查询内容（可选）
- `image`: 图片的shape_embedding向量（可选）

**返回值**:
```python
{
    "success": bool,           # 查询是否成功
    "query_id": str,          # 查询唯一标识
    "results": List[Dict],    # 查询结果列表
    "error": str,             # 错误信息（如果失败）
    "metadata": {
        "execution_time": float,      # 执行时间（秒）
        "result_count": int,          # 结果数量
        "query_intent": str,          # 查询意图
        "input_modality": str         # 输入模态类型
    }
}
```

**约束**:
- `text` 和 `image` 不能同时为空
- `image` 应该是768维的浮点数列表（shape_embedding向量）

## 系统架构

### 三个核心智能体

1. **编排智能体 (Orchestrator Agent)**
   - 查询分解和意图理解
   - 图片输入: 直接返回shape_embedding查询策略
   - 文字输入: 使用LLM进行智能分解

2. **多模态查询智能体 (Multi-modal Query Agent)**
   - 与Neo4j数据库交互
   - 支持元数据查询、结构查询、向量相似性查询
   - 动态生成Cypher查询语句

3. **融合智能体 (Fusion Agent)**
   - 多路径查询结果的智能融合
   - 结果排序和去重
   - 提供统一的输出格式

### 处理流程

```
用户输入 → 输入验证 → 查询分解 → 数据库查询 → 结果融合 → 返回结果
```

## 测试

运行测试脚本验证系统功能：

```bash
python test_mas_system.py
```

测试包括：
1. 纯文字输入测试
2. 纯图片输入测试  
3. 文字+图片混合输入测试
4. 无效输入测试

## 配置说明

### LLM配置

支持多种LLM提供商：

```bash
# OpenAI
export LLM_PROVIDER=openai
export LLM_API_KEY=sk-your-key

# Azure OpenAI
export LLM_PROVIDER=azure
export LLM_API_KEY=your-azure-key

# Ollama (本地)
export LLM_PROVIDER=ollama
export LLM_API_URL=http://localhost:11434
```

### 数据库配置

确保Neo4j数据库包含以下节点和属性：

- **Assembly**: `id, name, description, shape_embedding, description_embedding`
- **Part**: `id, name, description, material, shape_embedding, description_embedding`
- **Feature**: `id, name, type`

## 故障排除

### 常见问题

1. **LLM连接失败**
   - 检查API密钥和网络连接
   - 系统会自动回退到规则分解

2. **数据库连接失败**
   - 检查Neo4j服务状态
   - 验证连接配置

3. **查询结果为空**
   - 检查数据库中是否有相关数据
   - 降低相似度阈值

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展开发

### 添加新的查询类型

1. 在`QueryType`枚举中添加新类型
2. 在查询智能体中实现对应的执行器
3. 更新编排智能体的分解逻辑

### 自定义融合策略

继承`FusionAgent`类并重写融合方法。

## 许可证

本项目遵循MIT许可证。
