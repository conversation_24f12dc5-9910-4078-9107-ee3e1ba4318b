# 零件特征提取器使用指南

## 概述

`PartFeatureExtractor` 类用于提取零件的多模态特征，包括：

- 12 维结构化数值特征（经过 Z-score 标准化）
- 768 维形状向量特征
- 1024 维语义向量特征

## 配置说明

### 默认配置

`PartFeatureExtractor` 使用 `src/config.py` 中的数据库配置，并在类中定义了特征提取相关的配置：

```python
# 数据库配置来自 src/config.py
from src.config import Config
db_config = Config.POSTGRES_CONFIG

# 特征提取配置直接在类中定义
FEATURE_EXTRACTION_CONFIG = {
    'milvus_collection_name': 'cad_collection',
    'structural_feature_dim': 12,
    'shape_feature_dim': 768,
    'semantic_feature_dim': 1024,
    'batch_size': 1000,
    'max_parts_per_batch': 100,
    'continue_on_error': True,
    'max_retries': 3,
    'retry_delay': 1,
}
```

### 自定义配置

#### 方法 1：构造函数参数

```python
from src.part_feature_extractor import PartFeatureExtractor

# 自定义数据库配置
db_config = {
    'host': '*************',
    'port': 5432,
    'dbname': 'cad_database',
    'user': 'cad_user',
    'password': 'your_password'
}

# 创建特征提取器
extractor = PartFeatureExtractor(
    db_config=db_config,
    milvus_collection_name="my_collection"
)
```

#### 方法 2：获取和查看配置

```python
from src.part_feature_extractor import PartFeatureExtractor

# 获取默认配置
default_config = PartFeatureExtractor.get_default_config()
print(default_config)

# 创建实例并查看配置
extractor = PartFeatureExtractor()

# 获取特征维度信息
dimensions = extractor.get_feature_dimensions()
print(f"特征维度: {dimensions}")

# 获取批处理配置
batch_config = extractor.get_batch_config()
print(f"批处理配置: {batch_config}")
```

## 使用示例

### 基本使用

```python
from src.part_feature_extractor import PartFeatureExtractor

# 创建特征提取器（使用默认配置）
extractor = PartFeatureExtractor()

# 提取所有特征
features = extractor.extract_all_features()

# 保存特征
extractor.save_features(features, 'dataset/part_features.pkl')
extractor.save_scalers('dataset/feature_scalers.pkl')
```

### 提取指定零件的特征

```python
# 指定零件ID列表
part_ids = ["part_001", "part_002", "part_003"]

# 提取指定零件的特征
features = extractor.extract_all_features(part_ids=part_ids)
```

### 选择性提取特征

```python
# 只提取结构化特征和形状特征
features = extractor.extract_all_features(
    include_shape_features=True,
    include_semantic_features=False
)
```

## 命令行使用

### 基本命令

```bash
# 提取所有零件特征
python scripts/extract_part_features.py

# 指定输出目录
python scripts/extract_part_features.py --output-dir my_dataset

# 限制零件数量
python scripts/extract_part_features.py --max-parts 100

# 指定零件ID
python scripts/extract_part_features.py --part-ids part_001 part_002 part_003

# 仅加载和测试现有特征
python scripts/extract_part_features.py --load-only
```

### 参数说明

- `--output-dir`: 输出目录，默认为 `dataset`
- `--part-ids`: 指定零件 ID 列表
- `--max-parts`: 最大零件数量限制
- `--load-only`: 仅加载和测试现有特征

## 特征格式

### 结构化特征（12 维）

经过 Z-score 标准化的数值特征：

1. **几何特征（5 维）**：

   - length: 长度
   - width: 宽度
   - height: 高度
   - area: 表面积
   - volume: 体积

2. **物理特征（2 维）**：

   - density: 密度
   - mass: 质量

3. **孔特征（5 维）**：
   - hole_count: 孔数量
   - hole_diameter_mean: 孔直径均值
   - hole_diameter_std: 孔直径标准差
   - hole_depth_mean: 孔深度均值
   - hole_depth_std: 孔深度标准差

### 向量特征

- **形状向量（768 维）**：从 Milvus 的 shape_vector 字段提取
- **语义向量（1024 维）**：从 Milvus 的 dense_vector 字段提取

## 输出文件

### part_features.pkl

包含所有提取的特征：

```python
features = {
    'structural': np.ndarray,      # 结构化特征矩阵 (n_parts, 12)
    'shape': np.ndarray,           # 形状特征矩阵 (n_parts, 768)
    'semantic': np.ndarray,        # 语义特征矩阵 (n_parts, 1024)
    'part_ids': List[str],         # 零件ID列表
    'shape_ids': List[str],        # 有形状特征的零件ID列表
    'semantic_ids': List[str]      # 有语义特征的零件ID列表
}
```

### feature_scalers.pkl

包含标准化器：

```python
scalers = {
    'scaler': StandardScaler,      # 用于结构化特征的标准化器
    'fitted': bool                 # 是否已训练
}
```

## 错误处理

### 常见错误

1. **数据库连接失败**：

   - 检查数据库配置
   - 确认数据库服务正在运行
   - 验证用户名和密码

2. **Milvus 连接失败**：

   - 检查 Milvus 服务状态
   - 确认集合名称正确
   - 验证网络连接

3. **特征缺失**：
   - 检查数据库中是否有相关数据
   - 验证 Milvus 中是否有对应的向量数据

### 调试建议

1. 启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. 检查数据覆盖率：

```python
# 分析特征质量和覆盖率
features = extractor.extract_all_features()
print(f"总零件数: {len(features.get('part_ids', []))}")
print(f"结构化特征: {features.get('structural', np.array([])).shape}")
print(f"形状特征: {features.get('shape', np.array([])).shape}")
print(f"语义特征: {features.get('semantic', np.array([])).shape}")
```

## 注意事项

1. **Z-score 标准化**：结构化特征会自动进行标准化处理，确保各维度特征的公平性
2. **内存使用**：大批量处理时注意内存使用情况
3. **数据一致性**：确保 PostgreSQL 和 Milvus 中的数据一致性
4. **错误恢复**：部分特征提取失败不会影响其他特征的提取
