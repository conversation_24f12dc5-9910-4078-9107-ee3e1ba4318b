2025/06/25-01:08:41.531851 46 RocksDB version: 6.29.5
2025/06/25-01:08:41.532020 46 Git sha 0
2025/06/25-01:08:41.532024 46 Compile date 2024-11-15 11:22:58
2025/06/25-01:08:41.532025 46 DB SUMMARY
2025/06/25-01:08:41.532026 46 DB Session ID:  98460IFQDGWL0GKLFD7V
2025/06/25-01:08:41.602883 46 CURRENT file:  CURRENT
2025/06/25-01:08:41.602887 46 IDENTITY file:  IDENTITY
2025/06/25-01:08:41.603287 46 MANIFEST file:  MANIFEST-000044 size: 2798 Bytes
2025/06/25-01:08:41.603289 46 SST files in /var/lib/milvus/rdb_data dir, Total Num: 9, files: 000052.sst 000053.sst 000054.sst 000055.sst 000056.sst 000057.sst 000059.sst 000061.sst 000063.sst 
2025/06/25-01:08:41.603291 46 Write Ahead Log file in /var/lib/milvus/rdb_data: 000062.log size: 27617735 ; 
2025/06/25-01:08:41.603292 46                         Options.error_if_exists: 0
2025/06/25-01:08:41.603293 46                       Options.create_if_missing: 1
2025/06/25-01:08:41.603294 46                         Options.paranoid_checks: 1
2025/06/25-01:08:41.603294 46             Options.flush_verify_memtable_count: 1
2025/06/25-01:08:41.603294 46                               Options.track_and_verify_wals_in_manifest: 0
2025/06/25-01:08:41.603295 46                                     Options.env: 0x7fdd922dcd00
2025/06/25-01:08:41.603296 46                                      Options.fs: PosixFileSystem
2025/06/25-01:08:41.603296 46                                Options.info_log: 0x7fdc93290140
2025/06/25-01:08:41.603297 46                Options.max_file_opening_threads: 16
2025/06/25-01:08:41.603297 46                              Options.statistics: (nil)
2025/06/25-01:08:41.603298 46                               Options.use_fsync: 0
2025/06/25-01:08:41.603298 46                       Options.max_log_file_size: 0
2025/06/25-01:08:41.603299 46                  Options.max_manifest_file_size: 1073741824
2025/06/25-01:08:41.603299 46                   Options.log_file_time_to_roll: 0
2025/06/25-01:08:41.603300 46                       Options.keep_log_file_num: 1000
2025/06/25-01:08:41.603300 46                    Options.recycle_log_file_num: 0
2025/06/25-01:08:41.603301 46                         Options.allow_fallocate: 1
2025/06/25-01:08:41.603301 46                        Options.allow_mmap_reads: 0
2025/06/25-01:08:41.603302 46                       Options.allow_mmap_writes: 0
2025/06/25-01:08:41.603302 46                        Options.use_direct_reads: 0
2025/06/25-01:08:41.603303 46                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/25-01:08:41.603303 46          Options.create_missing_column_families: 1
2025/06/25-01:08:41.603303 46                              Options.db_log_dir: 
2025/06/25-01:08:41.603304 46                                 Options.wal_dir: 
2025/06/25-01:08:41.603304 46                Options.table_cache_numshardbits: 6
2025/06/25-01:08:41.603305 46                         Options.WAL_ttl_seconds: 0
2025/06/25-01:08:41.603305 46                       Options.WAL_size_limit_MB: 0
2025/06/25-01:08:41.603306 46                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/25-01:08:41.603306 46             Options.manifest_preallocation_size: 4194304
2025/06/25-01:08:41.603307 46                     Options.is_fd_close_on_exec: 1
2025/06/25-01:08:41.603307 46                   Options.advise_random_on_open: 1
2025/06/25-01:08:41.603308 46                   Options.experimental_mempurge_threshold: 0.000000
2025/06/25-01:08:41.603310 46                    Options.db_write_buffer_size: 0
2025/06/25-01:08:41.603311 46                    Options.write_buffer_manager: 0x7fdc95e40280
2025/06/25-01:08:41.603311 46         Options.access_hint_on_compaction_start: 1
2025/06/25-01:08:41.603312 46  Options.new_table_reader_for_compaction_inputs: 0
2025/06/25-01:08:41.603312 46           Options.random_access_max_buffer_size: 1048576
2025/06/25-01:08:41.603313 46                      Options.use_adaptive_mutex: 0
2025/06/25-01:08:41.603313 46                            Options.rate_limiter: (nil)
2025/06/25-01:08:41.603314 46     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/25-01:08:41.605666 46                       Options.wal_recovery_mode: 2
2025/06/25-01:08:41.605667 46                  Options.enable_thread_tracking: 0
2025/06/25-01:08:41.605668 46                  Options.enable_pipelined_write: 0
2025/06/25-01:08:41.605669 46                  Options.unordered_write: 0
2025/06/25-01:08:41.605669 46         Options.allow_concurrent_memtable_write: 1
2025/06/25-01:08:41.605670 46      Options.enable_write_thread_adaptive_yield: 1
2025/06/25-01:08:41.605670 46             Options.write_thread_max_yield_usec: 100
2025/06/25-01:08:41.605671 46            Options.write_thread_slow_yield_usec: 3
2025/06/25-01:08:41.605671 46                               Options.row_cache: None
2025/06/25-01:08:41.605672 46                              Options.wal_filter: None
2025/06/25-01:08:41.605672 46             Options.avoid_flush_during_recovery: 0
2025/06/25-01:08:41.605673 46             Options.allow_ingest_behind: 0
2025/06/25-01:08:41.605673 46             Options.preserve_deletes: 0
2025/06/25-01:08:41.605674 46             Options.two_write_queues: 0
2025/06/25-01:08:41.605674 46             Options.manual_wal_flush: 0
2025/06/25-01:08:41.605675 46             Options.atomic_flush: 0
2025/06/25-01:08:41.605675 46             Options.avoid_unnecessary_blocking_io: 0
2025/06/25-01:08:41.605675 46                 Options.persist_stats_to_disk: 0
2025/06/25-01:08:41.605676 46                 Options.write_dbid_to_manifest: 0
2025/06/25-01:08:41.605676 46                 Options.log_readahead_size: 0
2025/06/25-01:08:41.605677 46                 Options.file_checksum_gen_factory: Unknown
2025/06/25-01:08:41.605677 46                 Options.best_efforts_recovery: 0
2025/06/25-01:08:41.605678 46                Options.max_bgerror_resume_count: 2147483647
2025/06/25-01:08:41.605678 46            Options.bgerror_resume_retry_interval: 1000000
2025/06/25-01:08:41.605679 46             Options.allow_data_in_errors: 0
2025/06/25-01:08:41.605679 46             Options.db_host_id: __hostname__
2025/06/25-01:08:41.605680 46             Options.max_background_jobs: 2
2025/06/25-01:08:41.605680 46             Options.max_background_compactions: -1
2025/06/25-01:08:41.605681 46             Options.max_subcompactions: 1
2025/06/25-01:08:41.605681 46             Options.avoid_flush_during_shutdown: 0
2025/06/25-01:08:41.605682 46           Options.writable_file_max_buffer_size: 1048576
2025/06/25-01:08:41.605682 46             Options.delayed_write_rate : 16777216
2025/06/25-01:08:41.605683 46             Options.max_total_wal_size: 0
2025/06/25-01:08:41.605683 46             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/25-01:08:41.605684 46                   Options.stats_dump_period_sec: 600
2025/06/25-01:08:41.605684 46                 Options.stats_persist_period_sec: 600
2025/06/25-01:08:41.605685 46                 Options.stats_history_buffer_size: 1048576
2025/06/25-01:08:41.605685 46                          Options.max_open_files: -1
2025/06/25-01:08:41.605686 46                          Options.bytes_per_sync: 0
2025/06/25-01:08:41.605686 46                      Options.wal_bytes_per_sync: 0
2025/06/25-01:08:41.605687 46                   Options.strict_bytes_per_sync: 0
2025/06/25-01:08:41.605687 46       Options.compaction_readahead_size: 0
2025/06/25-01:08:41.605688 46                  Options.max_background_flushes: 1
2025/06/25-01:08:41.605688 46 Compression algorithms supported:
2025/06/25-01:08:41.605689 46 	kZSTD supported: 1
2025/06/25-01:08:41.605690 46 	kXpressCompression supported: 0
2025/06/25-01:08:41.605691 46 	kBZip2Compression supported: 0
2025/06/25-01:08:41.605691 46 	kZSTDNotFinalCompression supported: 1
2025/06/25-01:08:41.605692 46 	kLZ4Compression supported: 0
2025/06/25-01:08:41.605692 46 	kZlibCompression supported: 0
2025/06/25-01:08:41.605693 46 	kLZ4HCCompression supported: 0
2025/06/25-01:08:41.605693 46 	kSnappyCompression supported: 0
2025/06/25-01:08:41.605694 46 Fast CRC32 supported: Not supported on x86
2025/06/25-01:08:41.694836 46 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000044
2025/06/25-01:08:41.720508 46 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/06/25-01:08:41.720515 46               Options.comparator: leveldb.BytewiseComparator
2025/06/25-01:08:41.720516 46           Options.merge_operator: None
2025/06/25-01:08:41.720516 46        Options.compaction_filter: None
2025/06/25-01:08:41.720517 46        Options.compaction_filter_factory: None
2025/06/25-01:08:41.720517 46  Options.sst_partitioner_factory: None
2025/06/25-01:08:41.720518 46         Options.memtable_factory: SkipListFactory
2025/06/25-01:08:41.720519 46            Options.table_factory: BlockBasedTable
2025/06/25-01:08:41.720530 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fdc95f015c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fdc95e40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/25-01:08:41.720530 46        Options.write_buffer_size: 67108864
2025/06/25-01:08:41.720531 46  Options.max_write_buffer_number: 2
2025/06/25-01:08:41.720532 46        Options.compression[0]: NoCompression
2025/06/25-01:08:41.720532 46        Options.compression[1]: NoCompression
2025/06/25-01:08:41.720533 46        Options.compression[2]: ZSTD
2025/06/25-01:08:41.720533 46        Options.compression[3]: ZSTD
2025/06/25-01:08:41.720534 46        Options.compression[4]: ZSTD
2025/06/25-01:08:41.720534 46                  Options.bottommost_compression: Disabled
2025/06/25-01:08:41.720535 46       Options.prefix_extractor: nullptr
2025/06/25-01:08:41.720535 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/25-01:08:41.720536 46             Options.num_levels: 5
2025/06/25-01:08:41.720536 46        Options.min_write_buffer_number_to_merge: 1
2025/06/25-01:08:41.720537 46     Options.max_write_buffer_number_to_maintain: 0
2025/06/25-01:08:41.720537 46     Options.max_write_buffer_size_to_maintain: 0
2025/06/25-01:08:41.720538 46            Options.bottommost_compression_opts.window_bits: -14
2025/06/25-01:08:41.720538 46                  Options.bottommost_compression_opts.level: 32767
2025/06/25-01:08:41.720539 46               Options.bottommost_compression_opts.strategy: 0
2025/06/25-01:08:41.720539 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/25-01:08:41.720540 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/25-01:08:41.720540 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/25-01:08:41.720541 46                  Options.bottommost_compression_opts.enabled: false
2025/06/25-01:08:41.720541 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/25-01:08:41.720542 46            Options.compression_opts.window_bits: -14
2025/06/25-01:08:41.720542 46                  Options.compression_opts.level: 32767
2025/06/25-01:08:41.720543 46               Options.compression_opts.strategy: 0
2025/06/25-01:08:41.720543 46         Options.compression_opts.max_dict_bytes: 0
2025/06/25-01:08:41.720544 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/25-01:08:41.720627 46         Options.compression_opts.parallel_threads: 1
2025/06/25-01:08:41.720631 46                  Options.compression_opts.enabled: false
2025/06/25-01:08:41.720632 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/25-01:08:41.720633 46      Options.level0_file_num_compaction_trigger: 4
2025/06/25-01:08:41.720633 46          Options.level0_slowdown_writes_trigger: 20
2025/06/25-01:08:41.720634 46              Options.level0_stop_writes_trigger: 36
2025/06/25-01:08:41.720634 46                   Options.target_file_size_base: 67108864
2025/06/25-01:08:41.720635 46             Options.target_file_size_multiplier: 2
2025/06/25-01:08:41.720635 46                Options.max_bytes_for_level_base: 268435456
2025/06/25-01:08:41.720636 46 Options.level_compaction_dynamic_level_bytes: 0
2025/06/25-01:08:41.720637 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/25-01:08:41.720639 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/25-01:08:41.720640 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/25-01:08:41.720640 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/25-01:08:41.720640 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/25-01:08:41.720641 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/25-01:08:41.720641 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/25-01:08:41.720642 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/25-01:08:41.720642 46       Options.max_sequential_skip_in_iterations: 8
2025/06/25-01:08:41.720643 46                    Options.max_compaction_bytes: 1677721600
2025/06/25-01:08:41.720643 46                        Options.arena_block_size: 1048576
2025/06/25-01:08:41.720644 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/25-01:08:41.720644 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/25-01:08:41.720645 46       Options.rate_limit_delay_max_milliseconds: 100
2025/06/25-01:08:41.720645 46                Options.disable_auto_compactions: 0
2025/06/25-01:08:41.720648 46                        Options.compaction_style: kCompactionStyleLevel
2025/06/25-01:08:41.720648 46                          Options.compaction_pri: kMinOverlappingRatio
2025/06/25-01:08:41.720649 46 Options.compaction_options_universal.size_ratio: 1
2025/06/25-01:08:41.720649 46 Options.compaction_options_universal.min_merge_width: 2
2025/06/25-01:08:41.720650 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/25-01:08:41.720650 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/25-01:08:41.720651 46 Options.compaction_options_universal.compression_size_percent: -1
2025/06/25-01:08:41.720652 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/25-01:08:41.720652 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/25-01:08:41.720653 46 Options.compaction_options_fifo.allow_compaction: 0
2025/06/25-01:08:41.720657 46                   Options.table_properties_collectors: 
2025/06/25-01:08:41.720657 46                   Options.inplace_update_support: 0
2025/06/25-01:08:41.720658 46                 Options.inplace_update_num_locks: 10000
2025/06/25-01:08:41.720658 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/25-01:08:41.720659 46               Options.memtable_whole_key_filtering: 0
2025/06/25-01:08:41.720660 46   Options.memtable_huge_page_size: 0
2025/06/25-01:08:41.720660 46                           Options.bloom_locality: 0
2025/06/25-01:08:41.720661 46                    Options.max_successive_merges: 0
2025/06/25-01:08:41.720661 46                Options.optimize_filters_for_hits: 0
2025/06/25-01:08:41.720662 46                Options.paranoid_file_checks: 0
2025/06/25-01:08:41.720662 46                Options.force_consistency_checks: 1
2025/06/25-01:08:41.720662 46                Options.report_bg_io_stats: 0
2025/06/25-01:08:41.720663 46                               Options.ttl: 2592000
2025/06/25-01:08:41.720757 46          Options.periodic_compaction_seconds: 0
2025/06/25-01:08:41.720759 46                       Options.enable_blob_files: false
2025/06/25-01:08:41.720759 46                           Options.min_blob_size: 0
2025/06/25-01:08:41.720760 46                          Options.blob_file_size: 268435456
2025/06/25-01:08:41.720761 46                   Options.blob_compression_type: NoCompression
2025/06/25-01:08:41.720761 46          Options.enable_blob_garbage_collection: false
2025/06/25-01:08:41.720762 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/25-01:08:41.720763 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/25-01:08:41.720764 46          Options.blob_compaction_readahead_size: 0
2025/06/25-01:08:41.721109 46 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/06/25-01:08:41.721111 46               Options.comparator: leveldb.BytewiseComparator
2025/06/25-01:08:41.721111 46           Options.merge_operator: None
2025/06/25-01:08:41.721112 46        Options.compaction_filter: None
2025/06/25-01:08:41.721112 46        Options.compaction_filter_factory: None
2025/06/25-01:08:41.721113 46  Options.sst_partitioner_factory: None
2025/06/25-01:08:41.721113 46         Options.memtable_factory: SkipListFactory
2025/06/25-01:08:41.721114 46            Options.table_factory: BlockBasedTable
2025/06/25-01:08:41.721121 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fdc95f015c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fdc95e40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/25-01:08:41.721121 46        Options.write_buffer_size: 67108864
2025/06/25-01:08:41.721122 46  Options.max_write_buffer_number: 2
2025/06/25-01:08:41.721122 46        Options.compression[0]: NoCompression
2025/06/25-01:08:41.721123 46        Options.compression[1]: NoCompression
2025/06/25-01:08:41.721123 46        Options.compression[2]: ZSTD
2025/06/25-01:08:41.721124 46        Options.compression[3]: ZSTD
2025/06/25-01:08:41.721124 46        Options.compression[4]: ZSTD
2025/06/25-01:08:41.721125 46                  Options.bottommost_compression: Disabled
2025/06/25-01:08:41.721125 46       Options.prefix_extractor: nullptr
2025/06/25-01:08:41.721126 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/25-01:08:41.721126 46             Options.num_levels: 5
2025/06/25-01:08:41.721127 46        Options.min_write_buffer_number_to_merge: 1
2025/06/25-01:08:41.721127 46     Options.max_write_buffer_number_to_maintain: 0
2025/06/25-01:08:41.721128 46     Options.max_write_buffer_size_to_maintain: 0
2025/06/25-01:08:41.721128 46            Options.bottommost_compression_opts.window_bits: -14
2025/06/25-01:08:41.721129 46                  Options.bottommost_compression_opts.level: 32767
2025/06/25-01:08:41.721129 46               Options.bottommost_compression_opts.strategy: 0
2025/06/25-01:08:41.721129 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/25-01:08:41.721130 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/25-01:08:41.721199 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/25-01:08:41.721201 46                  Options.bottommost_compression_opts.enabled: false
2025/06/25-01:08:41.721202 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/25-01:08:41.721202 46            Options.compression_opts.window_bits: -14
2025/06/25-01:08:41.721203 46                  Options.compression_opts.level: 32767
2025/06/25-01:08:41.721203 46               Options.compression_opts.strategy: 0
2025/06/25-01:08:41.721204 46         Options.compression_opts.max_dict_bytes: 0
2025/06/25-01:08:41.721204 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/25-01:08:41.721205 46         Options.compression_opts.parallel_threads: 1
2025/06/25-01:08:41.721205 46                  Options.compression_opts.enabled: false
2025/06/25-01:08:41.721206 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/25-01:08:41.721206 46      Options.level0_file_num_compaction_trigger: 4
2025/06/25-01:08:41.721207 46          Options.level0_slowdown_writes_trigger: 20
2025/06/25-01:08:41.721207 46              Options.level0_stop_writes_trigger: 36
2025/06/25-01:08:41.721207 46                   Options.target_file_size_base: 67108864
2025/06/25-01:08:41.721208 46             Options.target_file_size_multiplier: 2
2025/06/25-01:08:41.721208 46                Options.max_bytes_for_level_base: 268435456
2025/06/25-01:08:41.721209 46 Options.level_compaction_dynamic_level_bytes: 0
2025/06/25-01:08:41.721209 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/25-01:08:41.721211 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/25-01:08:41.721211 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/25-01:08:41.721212 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/25-01:08:41.721212 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/25-01:08:41.721213 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/25-01:08:41.721213 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/25-01:08:41.721214 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/25-01:08:41.721214 46       Options.max_sequential_skip_in_iterations: 8
2025/06/25-01:08:41.721215 46                    Options.max_compaction_bytes: 1677721600
2025/06/25-01:08:41.721215 46                        Options.arena_block_size: 1048576
2025/06/25-01:08:41.721216 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/25-01:08:41.721216 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/25-01:08:41.721217 46       Options.rate_limit_delay_max_milliseconds: 100
2025/06/25-01:08:41.721217 46                Options.disable_auto_compactions: 0
2025/06/25-01:08:41.721218 46                        Options.compaction_style: kCompactionStyleLevel
2025/06/25-01:08:41.721219 46                          Options.compaction_pri: kMinOverlappingRatio
2025/06/25-01:08:41.721219 46 Options.compaction_options_universal.size_ratio: 1
2025/06/25-01:08:41.721220 46 Options.compaction_options_universal.min_merge_width: 2
2025/06/25-01:08:41.721220 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/25-01:08:41.721221 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/25-01:08:41.721221 46 Options.compaction_options_universal.compression_size_percent: -1
2025/06/25-01:08:41.721222 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/25-01:08:41.721222 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/25-01:08:41.721223 46 Options.compaction_options_fifo.allow_compaction: 0
2025/06/25-01:08:41.721225 46                   Options.table_properties_collectors: 
2025/06/25-01:08:41.721226 46                   Options.inplace_update_support: 0
2025/06/25-01:08:41.721226 46                 Options.inplace_update_num_locks: 10000
2025/06/25-01:08:41.721227 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/25-01:08:41.721303 46               Options.memtable_whole_key_filtering: 0
2025/06/25-01:08:41.721306 46   Options.memtable_huge_page_size: 0
2025/06/25-01:08:41.721307 46                           Options.bloom_locality: 0
2025/06/25-01:08:41.721308 46                    Options.max_successive_merges: 0
2025/06/25-01:08:41.721308 46                Options.optimize_filters_for_hits: 0
2025/06/25-01:08:41.721309 46                Options.paranoid_file_checks: 0
2025/06/25-01:08:41.721309 46                Options.force_consistency_checks: 1
2025/06/25-01:08:41.721310 46                Options.report_bg_io_stats: 0
2025/06/25-01:08:41.721310 46                               Options.ttl: 2592000
2025/06/25-01:08:41.721311 46          Options.periodic_compaction_seconds: 0
2025/06/25-01:08:41.721311 46                       Options.enable_blob_files: false
2025/06/25-01:08:41.721312 46                           Options.min_blob_size: 0
2025/06/25-01:08:41.721312 46                          Options.blob_file_size: 268435456
2025/06/25-01:08:41.721313 46                   Options.blob_compression_type: NoCompression
2025/06/25-01:08:41.721313 46          Options.enable_blob_garbage_collection: false
2025/06/25-01:08:41.721314 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/25-01:08:41.721315 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/25-01:08:41.721316 46          Options.blob_compaction_readahead_size: 0
2025/06/25-01:08:41.931722 46 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000044 succeeded,manifest_file_number is 44, next_file_number is 65, last_sequence is 1908385, log_number is 62,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 62
2025/06/25-01:08:41.931729 46 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 62
2025/06/25-01:08:41.931730 46 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 39
2025/06/25-01:08:41.957989 46 [db/version_set.cc:4409] Creating manifest 66
2025/06/25-01:08:42.093414 46 EVENT_LOG_v1 {"time_micros": 1750813722093408, "job": 1, "event": "recovery_started", "wal_files": [62]}
2025/06/25-01:08:42.093418 46 [db/db_impl/db_impl_open.cc:888] Recovering log #62 mode 2
2025/06/25-01:08:51.490771 46 EVENT_LOG_v1 {"time_micros": 1750813731490751, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 67, "file_size": 19748589, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 19741306, "index_size": 6354, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 10014828, "raw_average_key_size": 49, "raw_value_size": 16715466, "raw_average_value_size": 82, "num_data_blocks": 117, "num_entries": 203005, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750813730, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 67}}
2025/06/25-01:08:51.491240 46 [db/version_set.cc:4409] Creating manifest 68
2025/06/25-01:08:51.918470 46 EVENT_LOG_v1 {"time_micros": 1750813731918464, "job": 1, "event": "recovery_finished"}
2025/06/25-01:08:52.048684 46 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000062.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:08:52.048841 46 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fdc93390700
2025/06/25-01:08:52.048920 48 [db/compaction/compaction_job.cc:2331] [default] [JOB 3] Compacting 4@0 + 4@1 files to L1, score 1.00
2025/06/25-01:08:52.048925 48 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 5 Base level 0, inputs: [67(18MB) 63(59MB) 61(59MB) 59(59MB)], [52(64MB) 53(64MB) 54(64MB) 55(57MB)]
2025/06/25-01:08:52.048941 48 EVENT_LOG_v1 {"time_micros": 1750813732048933, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [67, 63, 61, 59], "files_L1": [52, 53, 54, 55], "score": 1, "input_data_size": 468570972}
2025/06/25-01:08:52.049809 46 DB pointer 0x7fdc93221c00
2025/06/25-01:08:55.050079 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:08:55.050092 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13.3 total, 13.3 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/4   196.85 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/4   250.02 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum     10/8   558.03 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13.3 total, 13.3 interval
Flush(GB): cumulative 0.018, interval 0.018
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 1.41 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.5 seconds
Interval compaction: 0.02 GB write, 1.41 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.5 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 1 last_copies: 2 last_secs: 2.9e-05 secs_since: 13
Block cache entry stats(count,size,portion): Misc(6,2.77 KB,0.000283704%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13.3 total, 13.3 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 1 last_copies: 2 last_secs: 2.9e-05 secs_since: 13
Block cache entry stats(count,size,portion): Misc(6,2.77 KB,0.000283704%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-01:08:58.283464 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #72: 5239 keys, 67240936 bytes
2025/06/25-01:08:58.283504 48 EVENT_LOG_v1 {"time_micros": 1750813738283484, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 72, "file_size": 67240936, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67235076, "index_size": 4910, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 256145, "raw_average_key_size": 48, "raw_value_size": 67154022, "raw_average_value_size": 12818, "num_data_blocks": 91, "num_entries": 5239, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813733, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 72}}
2025/06/25-01:09:03.125156 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #73: 216339 keys, 67855988 bytes
2025/06/25-01:09:03.125184 48 EVENT_LOG_v1 {"time_micros": 1750813743125173, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 73, "file_size": 67855988, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67844997, "index_size": 10038, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 10600611, "raw_average_key_size": 49, "raw_value_size": 64623529, "raw_average_value_size": 298, "num_data_blocks": 186, "num_entries": 216339, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813738, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 73}}
2025/06/25-01:09:13.552320 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #74: 4773 keys, 67739410 bytes
2025/06/25-01:09:13.552433 48 EVENT_LOG_v1 {"time_micros": 1750813753552421, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 74, "file_size": 67739410, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67733499, "index_size": 4961, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 233877, "raw_average_key_size": 49, "raw_value_size": 67658837, "raw_average_value_size": 14175, "num_data_blocks": 92, "num_entries": 4773, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813743, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 74}}
2025/06/25-01:09:18.193320 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #75: 4853 keys, 67839859 bytes
2025/06/25-01:09:18.193349 48 EVENT_LOG_v1 {"time_micros": 1750813758193337, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 75, "file_size": 67839859, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67833954, "index_size": 4955, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 237797, "raw_average_key_size": 49, "raw_value_size": 67758461, "raw_average_value_size": 13962, "num_data_blocks": 92, "num_entries": 4853, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813753, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 75}}
2025/06/25-01:09:22.399316 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #76: 4752 keys, 67241301 bytes
2025/06/25-01:09:22.399348 48 EVENT_LOG_v1 {"time_micros": 1750813762399333, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 76, "file_size": 67241301, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67235446, "index_size": 4905, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 232848, "raw_average_key_size": 49, "raw_value_size": 67161270, "raw_average_value_size": 14133, "num_data_blocks": 91, "num_entries": 4752, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813758, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 76}}
2025/06/25-01:09:26.211992 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #77: 200023 keys, 67140439 bytes
2025/06/25-01:09:26.212024 48 EVENT_LOG_v1 {"time_micros": 1750813766212011, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 77, "file_size": 67140439, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67112759, "index_size": 26726, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 9932304, "raw_average_key_size": 49, "raw_value_size": 64115298, "raw_average_value_size": 320, "num_data_blocks": 488, "num_entries": 200023, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813762, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 77}}
2025/06/25-01:09:30.379225 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #78: 1626485 keys, 63512496 bytes
2025/06/25-01:09:30.379254 48 EVENT_LOG_v1 {"time_micros": 1750813770379242, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 78, "file_size": 63512496, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 63462364, "index_size": 49178, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 80401760, "raw_average_key_size": 49, "raw_value_size": 39210343, "raw_average_value_size": 24, "num_data_blocks": 905, "num_entries": 1626485, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813766, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 78}}
2025/06/25-01:09:30.408418 48 [db/compaction/compaction_job.cc:1998] [default] [JOB 3] Compacted 4@0 + 4@1 files to L1 => 468570429 bytes
2025/06/25-01:09:30.433949 48 (Original Log Time 2025/06/25-01:09:30.433885) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 7 2 0 0] max score 1.75, MB/sec: 12.2 rd, 12.2 wr, level 1, files in(4, 4) out(7 +0 blob) MB in(196.8, 250.0 +0.0 blob) out(446.9 +0.0 blob), read-write-amplify(4.5) write-amplify(2.3) OK, records in: 2062464, records dropped: 0 output_compression: NoCompression
2025/06/25-01:09:30.433953 48 (Original Log Time 2025/06/25-01:09:30.433909) EVENT_LOG_v1 {"time_micros": 1750813770433897, "job": 3, "event": "compaction_finished", "compaction_time_micros": 38330857, "compaction_time_cpu_micros": 1037988, "output_level": 1, "num_output_files": 7, "total_output_size": 468570429, "num_input_records": 2062464, "num_output_records": 2062464, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 7, 2, 0, 0]}
2025/06/25-01:09:30.436382 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000067.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:30.436393 48 EVENT_LOG_v1 {"time_micros": 1750813770436391, "job": 3, "event": "table_file_deletion", "file_number": 67}
2025/06/25-01:09:30.440888 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000063.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:30.440898 48 EVENT_LOG_v1 {"time_micros": 1750813770440896, "job": 3, "event": "table_file_deletion", "file_number": 63}
2025/06/25-01:09:30.444929 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000061.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:30.444939 48 EVENT_LOG_v1 {"time_micros": 1750813770444937, "job": 3, "event": "table_file_deletion", "file_number": 61}
2025/06/25-01:09:30.449322 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000059.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:30.449335 48 EVENT_LOG_v1 {"time_micros": 1750813770449332, "job": 3, "event": "table_file_deletion", "file_number": 59}
2025/06/25-01:09:30.453497 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000055.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:30.453511 48 EVENT_LOG_v1 {"time_micros": 1750813770453509, "job": 3, "event": "table_file_deletion", "file_number": 55}
2025/06/25-01:09:30.457914 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000054.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:30.457925 48 EVENT_LOG_v1 {"time_micros": 1750813770457923, "job": 3, "event": "table_file_deletion", "file_number": 54}
2025/06/25-01:09:30.461041 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000053.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:30.461051 48 EVENT_LOG_v1 {"time_micros": 1750813770461049, "job": 3, "event": "table_file_deletion", "file_number": 53}
2025/06/25-01:09:30.464179 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000052.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:30.464189 48 EVENT_LOG_v1 {"time_micros": 1750813770464187, "job": 3, "event": "table_file_deletion", "file_number": 52}
2025/06/25-01:09:30.464329 48 [db/compaction/compaction_job.cc:2331] [default] [JOB 4] Compacting 1@1 files to L2, score 1.75
2025/06/25-01:09:30.464333 48 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 7 Base level 1, inputs: [73(64MB)]
2025/06/25-01:09:30.464339 48 EVENT_LOG_v1 {"time_micros": 1750813770464335, "job": 4, "event": "compaction_started", "compaction_reason": "LevelMaxLevelSize", "files_L1": [73], "score": 1.74556, "input_data_size": 67855988}
2025/06/25-01:09:31.684748 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 4] Generated table #79: 216339 keys, 54101592 bytes
2025/06/25-01:09:31.684788 48 EVENT_LOG_v1 {"time_micros": 1750813771684765, "cf_name": "default", "job": 4, "event": "table_file_creation", "file_number": 79, "file_size": 54101592, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 54098118, "index_size": 9935, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 10600611, "raw_average_key_size": 49, "raw_value_size": 64623529, "raw_average_value_size": 298, "num_data_blocks": 186, "num_entries": 216339, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "ZSTD", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813770, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 79}}
2025/06/25-01:09:31.769371 48 [db/compaction/compaction_job.cc:1998] [default] [JOB 4] Compacted 1@1 files to L2 => 54101592 bytes
2025/06/25-01:09:31.802120 48 (Original Log Time 2025/06/25-01:09:31.802075) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 6 3 0 0] max score 1.49, MB/sec: 55.6 rd, 44.3 wr, level 2, files in(1, 0) out(1 +0 blob) MB in(64.7, 0.0 +0.0 blob) out(51.6 +0.0 blob), read-write-amplify(1.8) write-amplify(0.8) OK, records in: 216339, records dropped: 0 output_compression: ZSTD
2025/06/25-01:09:31.802124 48 (Original Log Time 2025/06/25-01:09:31.802095) EVENT_LOG_v1 {"time_micros": 1750813771802088, "job": 4, "event": "compaction_finished", "compaction_time_micros": 1220873, "compaction_time_cpu_micros": 212944, "output_level": 2, "num_output_files": 1, "total_output_size": 54101592, "num_input_records": 216339, "num_output_records": 216339, "num_subcompactions": 1, "output_compression": "ZSTD", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 6, 3, 0, 0]}
2025/06/25-01:09:31.810070 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000073.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:31.810088 48 EVENT_LOG_v1 {"time_micros": 1750813771810086, "job": 4, "event": "table_file_deletion", "file_number": 73}
2025/06/25-01:09:31.810354 48 [db/compaction/compaction_job.cc:2331] [default] [JOB 5] Compacting 1@1 files to L2, score 1.49
2025/06/25-01:09:31.810358 48 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 8 Base level 1, inputs: [74(64MB)]
2025/06/25-01:09:31.810366 48 EVENT_LOG_v1 {"time_micros": 1750813771810362, "job": 5, "event": "compaction_started", "compaction_reason": "LevelMaxLevelSize", "files_L1": [74], "score": 1.49278, "input_data_size": 67739410}
2025/06/25-01:09:33.025451 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 5] Generated table #80: 4773 keys, 58260488 bytes
2025/06/25-01:09:33.025478 48 EVENT_LOG_v1 {"time_micros": 1750813773025467, "cf_name": "default", "job": 5, "event": "table_file_creation", "file_number": 80, "file_size": 58260488, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 58258246, "index_size": 4960, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 233877, "raw_average_key_size": 49, "raw_value_size": 67658837, "raw_average_value_size": 14175, "num_data_blocks": 92, "num_entries": 4773, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "ZSTD", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813771, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 80}}
2025/06/25-01:09:33.036721 48 [db/compaction/compaction_job.cc:1998] [default] [JOB 5] Compacted 1@1 files to L2 => 58260488 bytes
2025/06/25-01:09:33.069699 48 (Original Log Time 2025/06/25-01:09:33.069654) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 5 4 0 0] max score 1.24, MB/sec: 55.7 rd, 47.9 wr, level 2, files in(1, 0) out(1 +0 blob) MB in(64.6, 0.0 +0.0 blob) out(55.6 +0.0 blob), read-write-amplify(1.9) write-amplify(0.9) OK, records in: 4773, records dropped: 0 output_compression: ZSTD
2025/06/25-01:09:33.069702 48 (Original Log Time 2025/06/25-01:09:33.069674) EVENT_LOG_v1 {"time_micros": 1750813773069668, "job": 5, "event": "compaction_finished", "compaction_time_micros": 1215500, "compaction_time_cpu_micros": 176737, "output_level": 2, "num_output_files": 1, "total_output_size": 58260488, "num_input_records": 4773, "num_output_records": 4773, "num_subcompactions": 1, "output_compression": "ZSTD", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 5, 4, 0, 0]}
2025/06/25-01:09:33.077217 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000074.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:33.077236 48 EVENT_LOG_v1 {"time_micros": 1750813773077233, "job": 5, "event": "table_file_deletion", "file_number": 74}
2025/06/25-01:09:33.077442 48 [db/compaction/compaction_job.cc:2331] [default] [JOB 6] Compacting 1@1 files to L2, score 1.24
2025/06/25-01:09:33.077447 48 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 9 Base level 1, inputs: [75(64MB)]
2025/06/25-01:09:33.077456 48 EVENT_LOG_v1 {"time_micros": 1750813773077452, "job": 6, "event": "compaction_started", "compaction_reason": "LevelMaxLevelSize", "files_L1": [75], "score": 1.24043, "input_data_size": 67839859}
2025/06/25-01:09:34.426224 48 [db/compaction/compaction_job.cc:1937] [default] [JOB 6] Generated table #81: 4853 keys, 58541842 bytes
2025/06/25-01:09:34.426254 48 EVENT_LOG_v1 {"time_micros": 1750813774426240, "cf_name": "default", "job": 6, "event": "table_file_creation", "file_number": 81, "file_size": 58541842, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 58539567, "index_size": 4955, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 237797, "raw_average_key_size": 49, "raw_value_size": 67758461, "raw_average_value_size": 13962, "num_data_blocks": 92, "num_entries": 4853, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "ZSTD", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750813773, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "98460IFQDGWL0GKLFD7V", "orig_file_number": 81}}
2025/06/25-01:09:34.504772 48 [db/compaction/compaction_job.cc:1998] [default] [JOB 6] Compacted 1@1 files to L2 => 58541842 bytes
2025/06/25-01:09:34.514340 48 (Original Log Time 2025/06/25-01:09:34.514300) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 4 5 0 0] max score 0.99, MB/sec: 50.3 rd, 43.4 wr, level 2, files in(1, 0) out(1 +0 blob) MB in(64.7, 0.0 +0.0 blob) out(55.8 +0.0 blob), read-write-amplify(1.9) write-amplify(0.9) OK, records in: 4853, records dropped: 0 output_compression: ZSTD
2025/06/25-01:09:34.514343 48 (Original Log Time 2025/06/25-01:09:34.514317) EVENT_LOG_v1 {"time_micros": 1750813774514311, "job": 6, "event": "compaction_finished", "compaction_time_micros": 1349182, "compaction_time_cpu_micros": 176942, "output_level": 2, "num_output_files": 1, "total_output_size": 58541842, "num_input_records": 4853, "num_output_records": 4853, "num_subcompactions": 1, "output_compression": "ZSTD", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 4, 5, 0, 0]}
2025/06/25-01:09:34.519769 48 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000075.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:09:34.519791 48 EVENT_LOG_v1 {"time_micros": 1750813774519788, "job": 6, "event": "table_file_deletion", "file_number": 75}
2025/06/25-01:18:55.051185 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:18:55.051364 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 613.3 total, 600.0 interval
Cumulative writes: 8862 writes, 8862 keys, 4592 commit groups, 1.9 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8862 writes, 0 syncs, 8862.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8862 writes, 8862 keys, 4592 commit groups, 1.9 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8862 writes, 0 syncs, 8862.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0 639474351.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 613.3 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 1.03 MB/s write, 0.63 GB read, 1.04 MB/s read, 42.6 seconds
Interval compaction: 0.60 GB write, 1.02 MB/s write, 0.63 GB read, 1.07 MB/s read, 42.1 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 2 last_copies: 2 last_secs: 0.002832 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 613.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 2 last_copies: 2 last_secs: 0.002832 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-01:28:55.053224 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:28:55.058485 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1213.3 total, 600.0 interval
Cumulative writes: 17K writes, 17K keys, 9188 commit groups, 1.9 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17K writes, 0 syncs, 17862.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4596 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1213.3 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.52 MB/s write, 0.63 GB read, 0.53 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 3 last_copies: 2 last_secs: 0.00293 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1213.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 3 last_copies: 2 last_secs: 0.00293 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-01:38:55.065436 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:38:55.065825 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1813.3 total, 600.0 interval
Cumulative writes: 26K writes, 26K keys, 13K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26K writes, 0 syncs, 26862.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4481 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1813.3 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.35 MB/s write, 0.63 GB read, 0.35 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 4 last_copies: 2 last_secs: 0.002604 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1813.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 4 last_copies: 2 last_secs: 0.002604 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-01:48:55.067681 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:48:55.068215 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2413.3 total, 600.0 interval
Cumulative writes: 35K writes, 35K keys, 18K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 35K writes, 0 syncs, 35862.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4642 commit groups, 1.9 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2413.3 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.26 MB/s write, 0.63 GB read, 0.27 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 5 last_copies: 2 last_secs: 0.005229 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2413.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 5 last_copies: 2 last_secs: 0.005229 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-01:58:55.070267 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:58:55.070598 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3013.3 total, 600.0 interval
Cumulative writes: 44K writes, 44K keys, 23K commit groups, 1.9 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 44K writes, 0 syncs, 44863.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9001 writes, 9001 keys, 4783 commit groups, 1.9 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9001 writes, 0 syncs, 9001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3013.3 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.21 MB/s write, 0.63 GB read, 0.21 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 6 last_copies: 2 last_secs: 0.004011 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3013.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 6 last_copies: 2 last_secs: 0.004011 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-02:08:55.073122 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:08:55.074321 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3613.4 total, 600.0 interval
Cumulative writes: 53K writes, 53K keys, 27K commit groups, 1.9 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 53K writes, 0 syncs, 53863.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4760 commit groups, 1.9 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3613.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.17 MB/s write, 0.63 GB read, 0.18 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 7 last_copies: 2 last_secs: 0.005172 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3613.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 7 last_copies: 2 last_secs: 0.005172 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-02:18:55.076899 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:18:55.083598 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4213.4 total, 600.0 interval
Cumulative writes: 62K writes, 62K keys, 32K commit groups, 1.9 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 62K writes, 0 syncs, 62860.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 4727 commit groups, 1.9 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4213.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.15 MB/s write, 0.63 GB read, 0.15 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 8 last_copies: 2 last_secs: 0.004824 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4213.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 8 last_copies: 2 last_secs: 0.004824 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-02:28:55.088557 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:28:55.105777 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4813.4 total, 600.0 interval
Cumulative writes: 71K writes, 71K keys, 37K commit groups, 1.9 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 71K writes, 0 syncs, 71861.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9001 writes, 9001 keys, 4530 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9001 writes, 0 syncs, 9001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4813.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.13 MB/s write, 0.63 GB read, 0.13 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 9 last_copies: 2 last_secs: 0.004489 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4813.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 9 last_copies: 2 last_secs: 0.004489 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-02:38:55.108840 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:38:55.109308 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5413.4 total, 600.0 interval
Cumulative writes: 80K writes, 80K keys, 41K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 80K writes, 0 syncs, 80861.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4314 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5413.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.12 MB/s write, 0.63 GB read, 0.12 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 10 last_copies: 2 last_secs: 0.003745 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5413.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 10 last_copies: 2 last_secs: 0.003745 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-02:48:55.113184 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:48:55.113630 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6013.4 total, 600.0 interval
Cumulative writes: 89K writes, 89K keys, 45K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 89K writes, 0 syncs, 89861.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4157 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6013.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.10 MB/s write, 0.63 GB read, 0.11 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 11 last_copies: 2 last_secs: 0.004199 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6013.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 11 last_copies: 2 last_secs: 0.004199 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-02:58:55.117213 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:58:55.132202 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6613.4 total, 600.0 interval
Cumulative writes: 98K writes, 98K keys, 49K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 98K writes, 0 syncs, 98861.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4231 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6613.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.10 MB/s write, 0.63 GB read, 0.10 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 12 last_copies: 2 last_secs: 0.003697 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6613.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 12 last_copies: 2 last_secs: 0.003697 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-03:08:55.137239 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:08:55.137905 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7213.4 total, 600.0 interval
Cumulative writes: 107K writes, 107K keys, 54K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 107K writes, 0 syncs, 107864.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9003 writes, 9003 keys, 4284 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9003 writes, 0 syncs, 9003.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7213.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.09 MB/s write, 0.63 GB read, 0.09 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 13 last_copies: 2 last_secs: 0.0021 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7213.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 13 last_copies: 2 last_secs: 0.0021 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-03:18:55.141504 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:18:55.141981 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7813.4 total, 600.0 interval
Cumulative writes: 116K writes, 116K keys, 58K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 116K writes, 0 syncs, 116864.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4326 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7813.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.08 MB/s write, 0.63 GB read, 0.08 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 14 last_copies: 2 last_secs: 0.003165 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7813.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 14 last_copies: 2 last_secs: 0.003165 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-03:28:55.144483 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:28:55.145084 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8413.4 total, 600.0 interval
Cumulative writes: 125K writes, 125K keys, 62K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 125K writes, 0 syncs, 125864.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4191 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8413.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.07 MB/s write, 0.63 GB read, 0.08 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 15 last_copies: 2 last_secs: 0.003444 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8413.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 15 last_copies: 2 last_secs: 0.003444 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-03:38:55.149323 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:38:55.149893 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9013.4 total, 600.0 interval
Cumulative writes: 134K writes, 134K keys, 66K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 134K writes, 0 syncs, 134864.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4123 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9013.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.07 MB/s write, 0.63 GB read, 0.07 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 16 last_copies: 2 last_secs: 0.002645 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9013.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 16 last_copies: 2 last_secs: 0.002645 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-03:48:55.152666 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:48:55.158086 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9613.4 total, 600.0 interval
Cumulative writes: 143K writes, 143K keys, 71K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 143K writes, 0 syncs, 143864.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4493 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9613.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.07 MB/s write, 0.63 GB read, 0.07 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 17 last_copies: 2 last_secs: 0.00288 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9613.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 17 last_copies: 2 last_secs: 0.00288 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-03:58:55.161403 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:58:55.162003 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10213.4 total, 600.0 interval
Cumulative writes: 152K writes, 152K keys, 75K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 152K writes, 0 syncs, 152864.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4465 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10213.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.06 MB/s write, 0.63 GB read, 0.06 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 18 last_copies: 2 last_secs: 0.003927 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10213.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 18 last_copies: 2 last_secs: 0.003927 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-04:08:55.164091 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:08:55.164590 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10813.4 total, 600.0 interval
Cumulative writes: 161K writes, 161K keys, 79K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 161K writes, 0 syncs, 161802.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8938 writes, 8938 keys, 4197 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8938 writes, 0 syncs, 8938.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10813.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.06 MB/s write, 0.63 GB read, 0.06 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 19 last_copies: 2 last_secs: 0.002696 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10813.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 19 last_copies: 2 last_secs: 0.002696 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-04:18:55.167172 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:18:55.167556 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11413.4 total, 600.0 interval
Cumulative writes: 170K writes, 170K keys, 83K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 170K writes, 0 syncs, 170691.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8889 writes, 8889 keys, 4101 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8889 writes, 0 syncs, 8889.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11413.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.06 MB/s write, 0.63 GB read, 0.06 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 20 last_copies: 2 last_secs: 0.003209 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11413.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 20 last_copies: 2 last_secs: 0.003209 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-04:28:55.169953 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:28:55.170567 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12013.4 total, 600.0 interval
Cumulative writes: 179K writes, 179K keys, 88K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 179K writes, 0 syncs, 179559.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8868 writes, 8868 keys, 4175 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8868 writes, 0 syncs, 8868.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12013.4 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.05 MB/s write, 0.63 GB read, 0.05 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 21 last_copies: 2 last_secs: 0.003783 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12013.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 21 last_copies: 2 last_secs: 0.003783 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-04:38:55.173187 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:38:55.173520 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12613.5 total, 600.0 interval
Cumulative writes: 188K writes, 188K keys, 92K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 188K writes, 0 syncs, 188484.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8925 writes, 8925 keys, 4215 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8925 writes, 0 syncs, 8925.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12613.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.05 MB/s write, 0.63 GB read, 0.05 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 22 last_copies: 2 last_secs: 0.003418 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12613.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 22 last_copies: 2 last_secs: 0.003418 secs_since: 13
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-04:48:55.175843 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:48:55.176203 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13213.5 total, 600.0 interval
Cumulative writes: 197K writes, 197K keys, 96K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 197K writes, 0 syncs, 197301.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8817 writes, 8817 keys, 4172 commit groups, 2.1 writes per commit group, ingest: 0.56 MB, 0.00 MB/s
Interval WAL: 8817 writes, 0 syncs, 8817.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13213.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.05 MB/s write, 0.63 GB read, 0.05 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 23 last_copies: 2 last_secs: 0.005114 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13213.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 23 last_copies: 2 last_secs: 0.005114 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-04:58:55.178138 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:58:55.178528 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13813.5 total, 600.0 interval
Cumulative writes: 206K writes, 206K keys, 100K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 206K writes, 0 syncs, 206301.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4373 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13813.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.05 MB/s write, 0.63 GB read, 0.05 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 24 last_copies: 2 last_secs: 0.004591 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13813.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 24 last_copies: 2 last_secs: 0.004591 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-05:08:55.181196 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:08:55.181749 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 14413.5 total, 600.0 interval
Cumulative writes: 215K writes, 215K keys, 105K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 215K writes, 0 syncs, 215298.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 4240 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14413.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.04 MB/s write, 0.63 GB read, 0.04 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 25 last_copies: 2 last_secs: 0.005672 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14413.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 25 last_copies: 2 last_secs: 0.005672 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-05:18:55.201102 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:18:55.201695 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15013.5 total, 600.0 interval
Cumulative writes: 224K writes, 224K keys, 109K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 224K writes, 0 syncs, 224292.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8994 writes, 8994 keys, 4270 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8994 writes, 0 syncs, 8994.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15013.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.04 MB/s write, 0.63 GB read, 0.04 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 26 last_copies: 2 last_secs: 0.003762 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15013.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 26 last_copies: 2 last_secs: 0.003762 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-05:28:55.204723 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:28:55.208901 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15613.5 total, 600.0 interval
Cumulative writes: 233K writes, 233K keys, 113K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 233K writes, 0 syncs, 233274.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8982 writes, 8982 keys, 4479 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8982 writes, 0 syncs, 8982.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15613.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.04 MB/s write, 0.63 GB read, 0.04 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 27 last_copies: 2 last_secs: 0.002719 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15613.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 27 last_copies: 2 last_secs: 0.002719 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-05:38:55.211548 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:38:55.212166 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16213.5 total, 600.0 interval
Cumulative writes: 242K writes, 242K keys, 118K commit groups, 2.0 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 242K writes, 0 syncs, 242274.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4528 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16213.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.04 MB/s write, 0.63 GB read, 0.04 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 28 last_copies: 2 last_secs: 0.002693 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16213.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 28 last_copies: 2 last_secs: 0.002693 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-05:48:55.216687 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:48:55.217857 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16813.5 total, 600.0 interval
Cumulative writes: 251K writes, 251K keys, 122K commit groups, 2.0 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 251K writes, 0 syncs, 251277.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9003 writes, 9003 keys, 4409 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9003 writes, 0 syncs, 9003.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16813.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.04 MB/s write, 0.63 GB read, 0.04 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 29 last_copies: 2 last_secs: 0.003164 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16813.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 29 last_copies: 2 last_secs: 0.003164 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-05:58:55.220854 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:58:55.226658 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 17413.5 total, 600.0 interval
Cumulative writes: 260K writes, 260K keys, 127K commit groups, 2.0 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 260K writes, 0 syncs, 260277.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4315 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17413.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.04 MB/s write, 0.63 GB read, 0.04 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 30 last_copies: 2 last_secs: 0.003807 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17413.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 30 last_copies: 2 last_secs: 0.003807 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-06:08:55.231133 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:08:55.231500 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18013.5 total, 600.0 interval
Cumulative writes: 269K writes, 269K keys, 131K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 269K writes, 0 syncs, 269274.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 4114 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18013.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.04 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 31 last_copies: 2 last_secs: 0.003538 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18013.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 31 last_copies: 2 last_secs: 0.003538 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-06:18:55.234037 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:18:55.234603 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18613.5 total, 600.0 interval
Cumulative writes: 278K writes, 278K keys, 135K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 278K writes, 0 syncs, 278202.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8928 writes, 8928 keys, 4047 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8928 writes, 0 syncs, 8928.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18613.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 32 last_copies: 2 last_secs: 0.005884 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18613.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 32 last_copies: 2 last_secs: 0.005884 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-06:28:55.237416 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:28:55.239488 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19213.5 total, 600.0 interval
Cumulative writes: 287K writes, 287K keys, 139K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 287K writes, 0 syncs, 287169.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8967 writes, 8967 keys, 4213 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8967 writes, 0 syncs, 8967.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19213.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 33 last_copies: 2 last_secs: 0.00544 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19213.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 33 last_copies: 2 last_secs: 0.00544 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-06:38:55.242503 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:38:55.243830 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19813.5 total, 600.0 interval
Cumulative writes: 296K writes, 296K keys, 143K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 296K writes, 0 syncs, 296163.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8994 writes, 8994 keys, 4121 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8994 writes, 0 syncs, 8994.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19813.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 34 last_copies: 2 last_secs: 0.003514 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19813.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 34 last_copies: 2 last_secs: 0.003514 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-06:48:55.264123 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:48:55.264869 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20413.5 total, 600.0 interval
Cumulative writes: 305K writes, 305K keys, 147K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 305K writes, 0 syncs, 305154.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 4086 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20413.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 35 last_copies: 2 last_secs: 0.003934 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20413.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 35 last_copies: 2 last_secs: 0.003934 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-06:58:55.268403 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:58:55.269574 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21013.5 total, 600.0 interval
Cumulative writes: 314K writes, 314K keys, 151K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 314K writes, 0 syncs, 314122.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8968 writes, 8968 keys, 4199 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8968 writes, 0 syncs, 8968.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21013.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 36 last_copies: 2 last_secs: 0.003018 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21013.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 36 last_copies: 2 last_secs: 0.003018 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-07:08:55.271418 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:08:55.271949 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21613.5 total, 600.0 interval
Cumulative writes: 323K writes, 323K keys, 156K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 323K writes, 0 syncs, 323099.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8977 writes, 8977 keys, 4118 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8977 writes, 0 syncs, 8977.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21613.5 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 37 last_copies: 2 last_secs: 0.002993 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21613.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 37 last_copies: 2 last_secs: 0.002993 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-07:18:55.274542 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:18:55.275028 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22213.6 total, 600.0 interval
Cumulative writes: 332K writes, 332K keys, 160K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 332K writes, 0 syncs, 332021.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8922 writes, 8922 keys, 4151 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8922 writes, 0 syncs, 8922.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22213.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 38 last_copies: 2 last_secs: 0.00331 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22213.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 38 last_copies: 2 last_secs: 0.00331 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-07:28:55.278217 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:28:55.278703 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22813.6 total, 600.0 interval
Cumulative writes: 340K writes, 340K keys, 164K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 340K writes, 0 syncs, 340984.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8963 writes, 8963 keys, 4023 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8963 writes, 0 syncs, 8963.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22813.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 39 last_copies: 2 last_secs: 0.003445 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22813.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 39 last_copies: 2 last_secs: 0.003445 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-07:38:55.281189 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:38:55.286252 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 23413.6 total, 600.0 interval
Cumulative writes: 349K writes, 349K keys, 168K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 349K writes, 0 syncs, 349982.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8998 writes, 8998 keys, 4389 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8998 writes, 0 syncs, 8998.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 23413.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 40 last_copies: 2 last_secs: 0.00317 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 23413.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 40 last_copies: 2 last_secs: 0.00317 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-07:48:55.293843 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:48:55.294026 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24013.6 total, 600.0 interval
Cumulative writes: 358K writes, 358K keys, 172K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 358K writes, 0 syncs, 358982.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4131 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24013.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 41 last_copies: 2 last_secs: 0.003359 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24013.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 41 last_copies: 2 last_secs: 0.003359 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-07:58:55.295955 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:58:55.296445 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24613.6 total, 600.0 interval
Cumulative writes: 367K writes, 367K keys, 176K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 367K writes, 0 syncs, 367976.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8994 writes, 8994 keys, 4081 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8994 writes, 0 syncs, 8994.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24613.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.03 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 42 last_copies: 2 last_secs: 0.003237 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24613.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 42 last_copies: 2 last_secs: 0.003237 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-08:08:55.298597 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:08:55.300240 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 25213.6 total, 600.0 interval
Cumulative writes: 376K writes, 376K keys, 180K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 376K writes, 0 syncs, 376938.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8962 writes, 8962 keys, 4026 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8962 writes, 0 syncs, 8962.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25213.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.03 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 43 last_copies: 2 last_secs: 0.003908 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25213.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 43 last_copies: 2 last_secs: 0.003908 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-08:18:55.303976 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:18:55.304578 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 25813.6 total, 600.0 interval
Cumulative writes: 385K writes, 385K keys, 185K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 385K writes, 0 syncs, 385902.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8964 writes, 8964 keys, 4152 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8964 writes, 0 syncs, 8964.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25813.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 44 last_copies: 2 last_secs: 0.006465 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25813.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 44 last_copies: 2 last_secs: 0.006465 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-08:28:55.306948 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:28:55.307157 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 26413.6 total, 600.0 interval
Cumulative writes: 394K writes, 394K keys, 189K commit groups, 2.1 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 394K writes, 0 syncs, 394887.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8985 writes, 8985 keys, 4179 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8985 writes, 0 syncs, 8985.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 26413.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 45 last_copies: 2 last_secs: 0.002831 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 26413.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 45 last_copies: 2 last_secs: 0.002831 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-08:38:55.309237 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:38:55.309653 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27013.6 total, 600.0 interval
Cumulative writes: 403K writes, 403K keys, 193K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 403K writes, 0 syncs, 403852.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8965 writes, 8965 keys, 4319 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8965 writes, 0 syncs, 8965.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27013.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 46 last_copies: 2 last_secs: 0.001443 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27013.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 46 last_copies: 2 last_secs: 0.001443 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-08:48:55.312245 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:48:55.312780 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27613.6 total, 600.0 interval
Cumulative writes: 412K writes, 412K keys, 197K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 412K writes, 0 syncs, 412853.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9001 writes, 9001 keys, 4168 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9001 writes, 0 syncs, 9001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27613.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 47 last_copies: 2 last_secs: 0.003022 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27613.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 47 last_copies: 2 last_secs: 0.003022 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-08:58:55.314952 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:58:55.315342 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 28213.6 total, 600.0 interval
Cumulative writes: 421K writes, 421K keys, 201K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 421K writes, 0 syncs, 421853.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4113 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 28213.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 48 last_copies: 2 last_secs: 0.002777 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 28213.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 48 last_copies: 2 last_secs: 0.002777 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-09:08:55.317839 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:08:55.318366 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 28813.6 total, 600.0 interval
Cumulative writes: 430K writes, 430K keys, 205K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 430K writes, 0 syncs, 430839.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8986 writes, 8986 keys, 4115 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8986 writes, 0 syncs, 8986.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 28813.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 49 last_copies: 2 last_secs: 0.002386 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 28813.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 49 last_copies: 2 last_secs: 0.002386 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-09:18:55.320227 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:18:55.320601 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 29413.6 total, 600.0 interval
Cumulative writes: 439K writes, 439K keys, 210K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 439K writes, 0 syncs, 439839.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4523 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 29413.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 50 last_copies: 2 last_secs: 0.005489 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 29413.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 50 last_copies: 2 last_secs: 0.005489 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-09:28:55.324036 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:28:55.324523 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 30013.6 total, 600.0 interval
Cumulative writes: 448K writes, 448K keys, 215K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 448K writes, 0 syncs, 448839.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4778 commit groups, 1.9 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 30013.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 51 last_copies: 2 last_secs: 0.003157 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 30013.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 51 last_copies: 2 last_secs: 0.003157 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-09:38:55.326309 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:38:55.326617 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 30613.6 total, 600.0 interval
Cumulative writes: 457K writes, 457K keys, 219K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 457K writes, 0 syncs, 457839.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4525 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 30613.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 52 last_copies: 2 last_secs: 0.002446 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 30613.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 52 last_copies: 2 last_secs: 0.002446 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-09:48:55.329090 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:48:55.329467 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 31213.6 total, 600.0 interval
Cumulative writes: 466K writes, 466K keys, 224K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 466K writes, 0 syncs, 466839.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4456 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 31213.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 53 last_copies: 2 last_secs: 0.003152 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 31213.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 53 last_copies: 2 last_secs: 0.003152 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-09:58:55.332271 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:58:55.333613 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 31813.6 total, 600.0 interval
Cumulative writes: 475K writes, 475K keys, 228K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 475K writes, 0 syncs, 475839.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4475 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 31813.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 54 last_copies: 2 last_secs: 0.003131 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 31813.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 54 last_copies: 2 last_secs: 0.003131 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-10:08:55.339752 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:08:55.340283 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 32413.6 total, 600.0 interval
Cumulative writes: 484K writes, 484K keys, 233K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 484K writes, 0 syncs, 484839.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4504 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 32413.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 55 last_copies: 2 last_secs: 0.003623 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 32413.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 55 last_copies: 2 last_secs: 0.003623 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-10:18:55.342997 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:18:55.343731 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 33013.6 total, 600.0 interval
Cumulative writes: 493K writes, 493K keys, 237K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 493K writes, 0 syncs, 493693.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8855 writes, 8855 keys, 4135 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8854 writes, 0 syncs, 8854.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 33013.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 56 last_copies: 2 last_secs: 0.002982 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 33013.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 56 last_copies: 2 last_secs: 0.002982 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-10:28:55.346964 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:28:55.347432 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 33613.6 total, 600.0 interval
Cumulative writes: 502K writes, 502K keys, 241K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 502K writes, 0 syncs, 502420.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8726 writes, 8726 keys, 4125 commit groups, 2.1 writes per commit group, ingest: 0.56 MB, 0.00 MB/s
Interval WAL: 8727 writes, 0 syncs, 8727.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 33613.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 57 last_copies: 2 last_secs: 0.003209 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 33613.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 57 last_copies: 2 last_secs: 0.003209 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-10:38:55.349592 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:38:55.351198 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 34213.6 total, 600.0 interval
Cumulative writes: 511K writes, 511K keys, 245K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 511K writes, 0 syncs, 511294.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8874 writes, 8874 keys, 4150 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8874 writes, 0 syncs, 8874.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 34213.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 58 last_copies: 2 last_secs: 0.002357 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 34213.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 58 last_copies: 2 last_secs: 0.002357 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-10:48:55.353288 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:48:55.353870 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 34813.6 total, 600.0 interval
Cumulative writes: 520K writes, 520K keys, 249K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 520K writes, 0 syncs, 520294.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4173 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 34813.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 59 last_copies: 2 last_secs: 0.003025 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 34813.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 59 last_copies: 2 last_secs: 0.003025 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-10:58:55.357370 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:58:55.357880 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 35413.6 total, 600.0 interval
Cumulative writes: 529K writes, 529K keys, 254K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 529K writes, 0 syncs, 529294.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4358 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 35413.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 60 last_copies: 2 last_secs: 0.002954 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 35413.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 60 last_copies: 2 last_secs: 0.002954 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-11:08:55.360505 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:08:55.362499 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 36013.6 total, 600.0 interval
Cumulative writes: 538K writes, 538K keys, 258K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 538K writes, 0 syncs, 538282.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8988 writes, 8988 keys, 4416 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8988 writes, 0 syncs, 8988.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 36013.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 61 last_copies: 2 last_secs: 0.003021 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 36013.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 61 last_copies: 2 last_secs: 0.003021 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-11:18:55.366203 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:18:55.367164 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 36613.6 total, 600.0 interval
Cumulative writes: 547K writes, 547K keys, 262K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 547K writes, 0 syncs, 547157.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8875 writes, 8875 keys, 4059 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8875 writes, 0 syncs, 8875.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 36613.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 62 last_copies: 2 last_secs: 0.002911 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 36613.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 62 last_copies: 2 last_secs: 0.002911 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-11:28:55.370491 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:28:55.372495 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 37213.6 total, 600.0 interval
Cumulative writes: 556K writes, 556K keys, 266K commit groups, 2.1 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 556K writes, 0 syncs, 556113.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8956 writes, 8956 keys, 4024 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8956 writes, 0 syncs, 8956.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 37213.6 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 63 last_copies: 2 last_secs: 0.003759 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 37213.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 63 last_copies: 2 last_secs: 0.003759 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-11:38:55.375531 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:38:55.376158 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 37813.7 total, 600.0 interval
Cumulative writes: 565K writes, 565K keys, 270K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 565K writes, 0 syncs, 565089.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8976 writes, 8976 keys, 4068 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8976 writes, 0 syncs, 8976.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 37813.7 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 64 last_copies: 2 last_secs: 0.003465 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 37813.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 64 last_copies: 2 last_secs: 0.003465 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-11:48:55.379414 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:48:55.380238 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 38413.7 total, 600.0 interval
Cumulative writes: 573K writes, 573K keys, 274K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 573K writes, 0 syncs, 573708.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8619 writes, 8619 keys, 4053 commit groups, 2.1 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 8619 writes, 0 syncs, 8619.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 38413.7 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 65 last_copies: 2 last_secs: 0.006049 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 38413.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 65 last_copies: 2 last_secs: 0.006049 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-11:58:55.382390 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:58:55.382701 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 39013.7 total, 600.0 interval
Cumulative writes: 582K writes, 582K keys, 278K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 582K writes, 0 syncs, 582579.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8871 writes, 8871 keys, 4223 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8871 writes, 0 syncs, 8871.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 39013.7 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 66 last_copies: 2 last_secs: 0.004609 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 39013.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 66 last_copies: 2 last_secs: 0.004609 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-12:08:55.384833 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:08:55.385317 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 39613.7 total, 600.0 interval
Cumulative writes: 591K writes, 591K keys, 283K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 591K writes, 0 syncs, 591531.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8952 writes, 8952 keys, 4168 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8952 writes, 0 syncs, 8952.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 39613.7 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 67 last_copies: 2 last_secs: 0.003296 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 39613.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 67 last_copies: 2 last_secs: 0.003296 secs_since: 12
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-12:18:58.795385 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:18:58.795929 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 40217.1 total, 603.4 interval
Cumulative writes: 600K writes, 600K keys, 287K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 600K writes, 0 syncs, 600537.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9006 writes, 9006 keys, 4289 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9006 writes, 0 syncs, 9006.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 40217.1 total, 603.4 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 68 last_copies: 2 last_secs: 0.004835 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 40217.1 total, 603.4 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 68 last_copies: 2 last_secs: 0.004835 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-12:28:58.797695 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:28:58.799552 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 40817.1 total, 600.0 interval
Cumulative writes: 609K writes, 609K keys, 291K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 609K writes, 0 syncs, 609495.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8958 writes, 8958 keys, 4141 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8958 writes, 0 syncs, 8958.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 40817.1 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 69 last_copies: 2 last_secs: 0.00477 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 40817.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 69 last_copies: 2 last_secs: 0.00477 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-12:38:58.801537 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:38:58.802161 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 41417.1 total, 600.0 interval
Cumulative writes: 618K writes, 618K keys, 295K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 618K writes, 0 syncs, 618480.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8985 writes, 8985 keys, 4044 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8985 writes, 0 syncs, 8985.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 41417.1 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.02 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 70 last_copies: 2 last_secs: 0.00477 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 41417.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 70 last_copies: 2 last_secs: 0.00477 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-12:48:58.805838 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:48:58.807719 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 42017.1 total, 600.0 interval
Cumulative writes: 627K writes, 627K keys, 299K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 627K writes, 0 syncs, 627396.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8916 writes, 8916 keys, 4177 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8916 writes, 0 syncs, 8916.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 42017.1 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.01 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 71 last_copies: 2 last_secs: 0.003617 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 42017.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 71 last_copies: 2 last_secs: 0.003617 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/25-12:58:58.810792 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:58:58.814519 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 42617.1 total, 600.0 interval
Cumulative writes: 636K writes, 636K keys, 303K commit groups, 2.1 writes per commit group, ingest: 0.04 GB, 0.00 MB/s
Cumulative WAL: 636K writes, 0 syncs, 636312.00 writes per sync, written: 0.04 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8916 writes, 8916 keys, 4195 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8916 writes, 0 syncs, 8916.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   2.3     11.7     11.7     38.33              1.04         1   38.331   2062K      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.2     0.2      0.0       0.2      0.2       0.0   0.8     51.3     43.1      3.79              0.57         3    1.262    225K      0       0.0       0.0
 Sum      9/0   527.00 MB   0.0      0.6     0.4      0.2       0.6      0.4       0.0  33.4     15.0     14.8     42.62              1.60         5    8.524   2288K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.6     0.4      0.2       0.6      0.4       0.0   0.0     15.2     14.5     42.12              1.60         4   10.529   2288K      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     37.5      0.50              0.00         1    0.502       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 42617.1 total, 600.0 interval
Flush(GB): cumulative 0.018, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.01 MB/s write, 0.63 GB read, 0.02 MB/s read, 42.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 72 last_copies: 2 last_secs: 0.003451 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 42617.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 72 last_copies: 2 last_secs: 0.003451 secs_since: 15
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
