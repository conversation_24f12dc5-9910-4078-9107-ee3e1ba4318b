#!/usr/bin/env python3
"""
装配体词袋(Bag-of-Parts)特征提取脚本

基于零件聚类结果为每个装配体创建词袋表示：
- 将零件簇视为"词汇"
- 将装配体视为由这些"词汇"组成的"文档"
- 统计每个装配体中包含的零件簇数量
- 支持TF-IDF加权和归一化
"""

import argparse
import pickle
from pathlib import Path
import numpy as np
from sklearn.feature_extraction.text import TfidfTransformer
from sklearn.preprocessing import normalize
from collections import defaultdict, Counter
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_part_cluster_labels(cluster_file):
    """加载零件聚类结果"""
    logger.info(f"正在加载零件聚类结果: {cluster_file}")
    
    with open(cluster_file, 'rb') as f:
        cluster_data = pickle.load(f)
    
    labels = cluster_data['labels']
    part_names = cluster_data['part_names']
    assembly_names = cluster_data['assembly_names']
    
    logger.info(f"加载了 {len(labels)} 个零件的聚类结果")
    logger.info(f"聚类数量: {len(np.unique(labels[labels >= 0]))}")
    logger.info(f"噪声点数量: {np.sum(labels == -1)}")
    
    return labels, part_names, assembly_names

def create_assembly_part_mapping(labels, part_names, assembly_names, ignore_noise=True):
    """创建装配体到零件簇的映射"""
    logger.info("创建装配体到零件簇的映射...")
    
    # 获取有效的聚类标签
    valid_clusters = set(np.unique(labels))
    if ignore_noise and -1 in valid_clusters:
        valid_clusters.remove(-1)
        logger.info(f"忽略噪声点，有效聚类数量: {len(valid_clusters)}")
    else:
        logger.info(f"包含噪声点，总聚类数量: {len(valid_clusters)}")
    
    # 创建装配体词袋映射
    assembly_bow = defaultdict(Counter)
    
    for part_label, part_name, assembly_name in zip(labels, part_names, assembly_names):
        # 如果忽略噪声且当前零件是噪声点，跳过
        if ignore_noise and part_label == -1:
            continue
        
        # 统计装配体中每个簇的零件数量
        assembly_bow[assembly_name][part_label] += 1
    
    logger.info(f"处理了 {len(assembly_bow)} 个装配体")
    
    return assembly_bow, sorted(valid_clusters)

def create_bow_vectors(assembly_bow, valid_clusters):
    """创建词袋向量矩阵"""
    logger.info("创建词袋向量...")
    
    num_clusters = len(valid_clusters)
    cluster_to_idx = {cluster: idx for idx, cluster in enumerate(valid_clusters)}
    
    assembly_names = list(assembly_bow.keys())
    num_assemblies = len(assembly_names)
    
    # 创建词袋矩阵 (assemblies x clusters)
    bow_matrix = np.zeros((num_assemblies, num_clusters), dtype=np.int32)
    
    for assembly_idx, assembly_name in enumerate(assembly_names):
        for cluster_id, count in assembly_bow[assembly_name].items():
            if cluster_id in cluster_to_idx:
                cluster_idx = cluster_to_idx[cluster_id]
                bow_matrix[assembly_idx, cluster_idx] = count
    
    logger.info(f"创建了形状为 {bow_matrix.shape} 的词袋矩阵")
    
    return bow_matrix, assembly_names, cluster_to_idx

def compute_tfidf_features(bow_matrix, use_tfidf=True, normalize_l2=True):
    """计算TF-IDF特征"""
    if not use_tfidf and not normalize_l2:
        logger.info("使用原始词袋计数")
        return bow_matrix
    
    features = bow_matrix.astype(np.float32)
    
    if use_tfidf:
        logger.info("应用TF-IDF变换...")
        tfidf = TfidfTransformer(norm=None, use_idf=True, smooth_idf=True)
        features = tfidf.fit_transform(features).toarray()
        logger.info("TF-IDF变换完成")
    
    if normalize_l2:
        logger.info("应用L2归一化...")
        features = normalize(features, norm='l2')
        logger.info("L2归一化完成")
    
    return features

def analyze_bow_statistics(bow_matrix, assembly_names, cluster_to_idx):
    """分析词袋统计信息"""
    logger.info("分析词袋统计信息...")
    
    num_assemblies, num_clusters = bow_matrix.shape
    
    # 装配体统计
    parts_per_assembly = np.sum(bow_matrix, axis=1)
    clusters_per_assembly = np.sum(bow_matrix > 0, axis=1)
    
    # 簇统计
    assemblies_per_cluster = np.sum(bow_matrix > 0, axis=0)
    total_parts_per_cluster = np.sum(bow_matrix, axis=0)
    
    logger.info(f"装配体统计:")
    logger.info(f"  平均零件数: {np.mean(parts_per_assembly):.2f}")
    logger.info(f"  零件数范围: {np.min(parts_per_assembly)} - {np.max(parts_per_assembly)}")
    logger.info(f"  平均簇数: {np.mean(clusters_per_assembly):.2f}")
    logger.info(f"  簇数范围: {np.min(clusters_per_assembly)} - {np.max(clusters_per_assembly)}")
    
    logger.info(f"零件簇统计:")
    logger.info(f"  平均出现在装配体数: {np.mean(assemblies_per_cluster):.2f}")
    logger.info(f"  出现装配体数范围: {np.min(assemblies_per_cluster)} - {np.max(assemblies_per_cluster)}")
    logger.info(f"  平均总零件数: {np.mean(total_parts_per_cluster):.2f}")
    logger.info(f"  总零件数范围: {np.min(total_parts_per_cluster)} - {np.max(total_parts_per_cluster)}")
    
    # 找出最常见和最稀有的簇
    idx_to_cluster = {idx: cluster for cluster, idx in cluster_to_idx.items()}
    
    # 最常见的簇（出现在最多装配体中）
    most_common_clusters = np.argsort(assemblies_per_cluster)[-10:][::-1]
    logger.info("最常见的零件簇（出现在最多装配体中）:")
    for idx in most_common_clusters:
        cluster_id = idx_to_cluster[idx]
        logger.info(f"  簇 {cluster_id}: 出现在 {assemblies_per_cluster[idx]} 个装配体中，"
                   f"总共 {total_parts_per_cluster[idx]} 个零件")
    
    # 最稀有的簇
    least_common_clusters = np.argsort(assemblies_per_cluster)[:10]
    logger.info("最稀有的零件簇:")
    for idx in least_common_clusters:
        if assemblies_per_cluster[idx] > 0:  # 排除空簇
            cluster_id = idx_to_cluster[idx]
            logger.info(f"  簇 {cluster_id}: 出现在 {assemblies_per_cluster[idx]} 个装配体中，"
                       f"总共 {total_parts_per_cluster[idx]} 个零件")

def save_bow_features(features, assembly_names, cluster_to_idx, output_file, 
                     feature_type, params):
    """保存词袋特征"""
    logger.info(f"保存词袋特征到: {output_file}")
    
    bow_data = {
        'features': features,
        'assembly_names': assembly_names,
        'cluster_to_idx': cluster_to_idx,
        'feature_type': feature_type,
        'params': params,
        'num_assemblies': len(assembly_names),
        'num_clusters': len(cluster_to_idx),
        'feature_dim': features.shape[1]
    }
    
    with open(output_file, 'wb') as f:
        pickle.dump(bow_data, f)
    
    logger.info(f"已保存 {len(assembly_names)} 个装配体的 {features.shape[1]} 维词袋特征")

def main():
    parser = argparse.ArgumentParser(description='提取装配体词袋特征')
    parser.add_argument('--cluster-file', type=str, 
                       default='dataset/hdbscan_labels_fused_features.pkl',
                       help='零件聚类结果文件')
    parser.add_argument('--output-file', type=str,
                       default='dataset/assembly_bow_features.pkl',
                       help='输出词袋特征文件')
    parser.add_argument('--use-tfidf', default=True,
                       help='使用TF-IDF加权')
    parser.add_argument('--normalize-l2', default=True,
                       help='使用L2归一化')
    parser.add_argument('--ignore-noise', default=True,
                       help='忽略噪声点(-1标签)')
    parser.add_argument('--show-stats', action='store_true', default=True,
                       help='显示详细统计信息')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not Path(args.cluster_file).exists():
        logger.error(f"聚类文件不存在: {args.cluster_file}")
        return
    
    try:
        # 加载零件聚类结果
        labels, part_names, assembly_names = load_part_cluster_labels(args.cluster_file)
        
        # 创建装配体词袋映射
        assembly_bow, valid_clusters = create_assembly_part_mapping(
            labels, part_names, assembly_names, args.ignore_noise)
        
        # 创建词袋向量
        bow_matrix, assembly_name_list, cluster_to_idx = create_bow_vectors(
            assembly_bow, valid_clusters)
        
        # 显示统计信息
        if args.show_stats:
            analyze_bow_statistics(bow_matrix, assembly_name_list, cluster_to_idx)
        
        # 计算最终特征
        features = compute_tfidf_features(
            bow_matrix, args.use_tfidf, args.normalize_l2)
        
        # 确定特征类型
        feature_type = "raw_bow"
        if args.use_tfidf and args.normalize_l2:
            feature_type = "tfidf_l2_bow"
        elif args.use_tfidf:
            feature_type = "tfidf_bow"
        elif args.normalize_l2:
            feature_type = "l2_bow"
        
        # 保存参数
        params = {
            'use_tfidf': args.use_tfidf,
            'normalize_l2': args.normalize_l2,
            'ignore_noise': args.ignore_noise,
            'source_cluster_file': args.cluster_file,
            'num_source_parts': len(labels),
            'num_valid_clusters': len(valid_clusters)
        }
        
        # 保存特征
        save_bow_features(features, assembly_name_list, cluster_to_idx, 
                         args.output_file, feature_type, params)
        
        logger.info("装配体词袋特征提取完成!")
        
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        raise

if __name__ == "__main__":
    main()
