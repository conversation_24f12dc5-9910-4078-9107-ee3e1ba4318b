#!/usr/bin/env python3
"""
测试统一的智能体接口
验证三个查询智能体的execute_task函数是否正常工作
"""

import asyncio
import uuid
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agent.structured_data_agent import StructuredDataAgent
from src.agent.structural_relationship_agent import StructuralRelationshipAgent
from src.agent.geometry_semantic_agent import GeometrySemanticAgent
from src.agent.data_models import (
    UnifiedStructuredDataTask,
    UnifiedStructuralQueryTask,
    UnifiedGeometrySemanticTask
)


async def test_structured_data_agent():
    """测试结构化数据查询智能体"""
    print("\n=== 测试结构化数据查询智能体 ===")
    
    agent = StructuredDataAgent()
    
    try:
        # 创建测试任务
        task = UnifiedStructuredDataTask(
            task_id=str(uuid.uuid4()),
            query_text="查找质量大于100克的零件",
            top_k=10
        )
        
        print(f"执行任务: {task.query_text}")
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        print(f"结果数量: {result.total_results}")
        
        if result.status == 'success' and result.results:
            print("前3个结果:")
            for i, item in enumerate(result.results[:3]):
                print(f"  {i+1}. ID: {item.id}, 名称: {item.name}")
        
        return result.status == 'success'
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_structural_relationship_agent():
    """测试结构关系查询智能体"""
    print("\n=== 测试结构关系查询智能体 ===")
    
    agent = StructuralRelationshipAgent()
    
    try:
        # 创建测试任务
        task = UnifiedStructuralQueryTask(
            task_id=str(uuid.uuid4()),
            query_text="查找所有装配体及其包含的零件",
            top_k=10
        )
        
        print(f"执行任务: {task.query_text}")
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        print(f"结果数量: {result.total_results}")
        
        if result.status == 'success' and result.results:
            print("前3个结果:")
            for i, item in enumerate(result.results[:3]):
                print(f"  {i+1}. ID: {item.id}, 名称: {item.name}")
        
        return result.status == 'success'
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_geometry_semantic_agent():
    """测试几何和语义查询智能体"""
    print("\n=== 测试几何和语义查询智能体 ===")
    
    agent = GeometrySemanticAgent()
    
    try:
        # 测试纯文本查询
        task = UnifiedGeometrySemanticTask(
            task_id=str(uuid.uuid4()),
            query_text="圆形零件",
            top_k=5
        )
        
        print(f"执行任务: {task.query_text}")
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        print(f"结果数量: {result.total_results}")
        
        if result.status == 'success' and result.results:
            print("前3个结果:")
            for i, item in enumerate(result.results[:3]):
                print(f"  {i+1}. ID: {item.id}, 相似度: {item.similarity_score:.3f}")
        
        return result.status == 'success'
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_with_id_filter():
    """测试带ID过滤的查询"""
    print("\n=== 测试带ID过滤的查询 ===")
    
    agent = StructuredDataAgent()
    
    try:
        # 创建带ID过滤的测试任务
        task = UnifiedStructuredDataTask(
            task_id=str(uuid.uuid4()),
            query_text="查找所有零件",
            id_list=["bc30eac0-0535-11ec-a9cd-0ae0e5d97f29", "bc3111d8-0535-11ec-b48a-0ae0e5d97f29", "bc31fc3e-0535-11ec-a5dd-0ae0e5d97f29"],
            top_k=3
        )
        
        print(f"执行任务: {task.query_text}")
        print(f"ID过滤列表: {task.id_list}")
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        print(f"结果数量: {result.total_results}")
        
        return result.status == 'success'
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def main():
    """主测试函数"""
    print("开始测试统一的智能体接口...")
    
    test_results = []
    
    # 测试结构化数据智能体
    try:
        result = await test_structured_data_agent()
        test_results.append(("结构化数据智能体", result))
    except Exception as e:
        print(f"结构化数据智能体测试异常: {e}")
        test_results.append(("结构化数据智能体", False))
    
    # 测试结构关系智能体
    try:
        result = await test_structural_relationship_agent()
        test_results.append(("结构关系智能体", result))
    except Exception as e:
        print(f"结构关系智能体测试异常: {e}")
        test_results.append(("结构关系智能体", False))
    
    # 测试几何和语义智能体
    try:
        result = await test_geometry_semantic_agent()
        test_results.append(("几何和语义智能体", result))
    except Exception as e:
        print(f"几何和语义智能体测试异常: {e}")
        test_results.append(("几何和语义智能体", False))
    
    # 测试ID过滤功能
    try:
        result = await test_with_id_filter()
        test_results.append(("ID过滤功能", result))
    except Exception as e:
        print(f"ID过滤功能测试异常: {e}")
        test_results.append(("ID过滤功能", False))
    
    # 输出测试结果总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(test_results)} 个测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试都通过了！统一接口工作正常。")
    else:
        print("⚠️  部分测试失败，请检查配置和数据库连接。")


if __name__ == "__main__":
    asyncio.run(main())
