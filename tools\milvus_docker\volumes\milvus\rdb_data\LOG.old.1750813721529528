2025/06/24-00:35:25.573668 46 RocksDB version: 6.29.5
2025/06/24-00:35:25.573961 46 Git sha 0
2025/06/24-00:35:25.573964 46 Compile date 2024-11-15 11:22:58
2025/06/24-00:35:25.573965 46 DB SUMMARY
2025/06/24-00:35:25.573965 46 DB Session ID:  RQ8MAS9M7DG6HMJAHPA3
2025/06/24-00:35:25.575500 46 CURRENT file:  CURRENT
2025/06/24-00:35:25.575509 46 IDENTITY file:  IDENTITY
2025/06/24-00:35:25.575958 46 MANIFEST file:  MANIFEST-000025 size: 1669 Bytes
2025/06/24-00:35:25.575966 46 SST files in /var/lib/milvus/rdb_data dir, Total Num: 5, files: 000033.sst 000034.sst 000035.sst 000037.sst 000039.sst 
2025/06/24-00:35:25.575967 46 Write Ahead Log file in /var/lib/milvus/rdb_data: 000038.log size: 43780743 ; 
2025/06/24-00:35:25.575969 46                         Options.error_if_exists: 0
2025/06/24-00:35:25.575969 46                       Options.create_if_missing: 1
2025/06/24-00:35:25.575970 46                         Options.paranoid_checks: 1
2025/06/24-00:35:25.575970 46             Options.flush_verify_memtable_count: 1
2025/06/24-00:35:25.575971 46                               Options.track_and_verify_wals_in_manifest: 0
2025/06/24-00:35:25.575971 46                                     Options.env: 0x7f412647bd00
2025/06/24-00:35:25.575972 46                                      Options.fs: PosixFileSystem
2025/06/24-00:35:25.575973 46                                Options.info_log: 0x7f4027690140
2025/06/24-00:35:25.575973 46                Options.max_file_opening_threads: 16
2025/06/24-00:35:25.575974 46                              Options.statistics: (nil)
2025/06/24-00:35:25.575974 46                               Options.use_fsync: 0
2025/06/24-00:35:25.575975 46                       Options.max_log_file_size: 0
2025/06/24-00:35:25.575975 46                  Options.max_manifest_file_size: 1073741824
2025/06/24-00:35:25.575976 46                   Options.log_file_time_to_roll: 0
2025/06/24-00:35:25.575976 46                       Options.keep_log_file_num: 1000
2025/06/24-00:35:25.575977 46                    Options.recycle_log_file_num: 0
2025/06/24-00:35:25.575977 46                         Options.allow_fallocate: 1
2025/06/24-00:35:25.575978 46                        Options.allow_mmap_reads: 0
2025/06/24-00:35:25.575978 46                       Options.allow_mmap_writes: 0
2025/06/24-00:35:25.575979 46                        Options.use_direct_reads: 0
2025/06/24-00:35:25.575979 46                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/24-00:35:25.575979 46          Options.create_missing_column_families: 1
2025/06/24-00:35:25.575980 46                              Options.db_log_dir: 
2025/06/24-00:35:25.575980 46                                 Options.wal_dir: 
2025/06/24-00:35:25.575981 46                Options.table_cache_numshardbits: 6
2025/06/24-00:35:25.575981 46                         Options.WAL_ttl_seconds: 0
2025/06/24-00:35:25.575982 46                       Options.WAL_size_limit_MB: 0
2025/06/24-00:35:25.575982 46                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/24-00:35:25.575983 46             Options.manifest_preallocation_size: 4194304
2025/06/24-00:35:25.575983 46                     Options.is_fd_close_on_exec: 1
2025/06/24-00:35:25.575984 46                   Options.advise_random_on_open: 1
2025/06/24-00:35:25.575984 46                   Options.experimental_mempurge_threshold: 0.000000
2025/06/24-00:35:25.575987 46                    Options.db_write_buffer_size: 0
2025/06/24-00:35:25.575988 46                    Options.write_buffer_manager: 0x7f402a240280
2025/06/24-00:35:25.575988 46         Options.access_hint_on_compaction_start: 1
2025/06/24-00:35:25.575989 46  Options.new_table_reader_for_compaction_inputs: 0
2025/06/24-00:35:25.575989 46           Options.random_access_max_buffer_size: 1048576
2025/06/24-00:35:25.575990 46                      Options.use_adaptive_mutex: 0
2025/06/24-00:35:25.575990 46                            Options.rate_limiter: (nil)
2025/06/24-00:35:25.575991 46     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/24-00:35:25.575992 46                       Options.wal_recovery_mode: 2
2025/06/24-00:35:25.576481 46                  Options.enable_thread_tracking: 0
2025/06/24-00:35:25.576485 46                  Options.enable_pipelined_write: 0
2025/06/24-00:35:25.576486 46                  Options.unordered_write: 0
2025/06/24-00:35:25.576486 46         Options.allow_concurrent_memtable_write: 1
2025/06/24-00:35:25.576487 46      Options.enable_write_thread_adaptive_yield: 1
2025/06/24-00:35:25.576487 46             Options.write_thread_max_yield_usec: 100
2025/06/24-00:35:25.576488 46            Options.write_thread_slow_yield_usec: 3
2025/06/24-00:35:25.576488 46                               Options.row_cache: None
2025/06/24-00:35:25.576489 46                              Options.wal_filter: None
2025/06/24-00:35:25.576489 46             Options.avoid_flush_during_recovery: 0
2025/06/24-00:35:25.576490 46             Options.allow_ingest_behind: 0
2025/06/24-00:35:25.576490 46             Options.preserve_deletes: 0
2025/06/24-00:35:25.576491 46             Options.two_write_queues: 0
2025/06/24-00:35:25.576491 46             Options.manual_wal_flush: 0
2025/06/24-00:35:25.576492 46             Options.atomic_flush: 0
2025/06/24-00:35:25.576492 46             Options.avoid_unnecessary_blocking_io: 0
2025/06/24-00:35:25.576493 46                 Options.persist_stats_to_disk: 0
2025/06/24-00:35:25.576493 46                 Options.write_dbid_to_manifest: 0
2025/06/24-00:35:25.576494 46                 Options.log_readahead_size: 0
2025/06/24-00:35:25.576494 46                 Options.file_checksum_gen_factory: Unknown
2025/06/24-00:35:25.576495 46                 Options.best_efforts_recovery: 0
2025/06/24-00:35:25.576495 46                Options.max_bgerror_resume_count: 2147483647
2025/06/24-00:35:25.576495 46            Options.bgerror_resume_retry_interval: 1000000
2025/06/24-00:35:25.576496 46             Options.allow_data_in_errors: 0
2025/06/24-00:35:25.576496 46             Options.db_host_id: __hostname__
2025/06/24-00:35:25.576497 46             Options.max_background_jobs: 2
2025/06/24-00:35:25.576498 46             Options.max_background_compactions: -1
2025/06/24-00:35:25.576498 46             Options.max_subcompactions: 1
2025/06/24-00:35:25.576499 46             Options.avoid_flush_during_shutdown: 0
2025/06/24-00:35:25.576499 46           Options.writable_file_max_buffer_size: 1048576
2025/06/24-00:35:25.576500 46             Options.delayed_write_rate : 16777216
2025/06/24-00:35:25.576500 46             Options.max_total_wal_size: 0
2025/06/24-00:35:25.576501 46             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/24-00:35:25.576501 46                   Options.stats_dump_period_sec: 600
2025/06/24-00:35:25.576502 46                 Options.stats_persist_period_sec: 600
2025/06/24-00:35:25.576502 46                 Options.stats_history_buffer_size: 1048576
2025/06/24-00:35:25.576502 46                          Options.max_open_files: -1
2025/06/24-00:35:25.576503 46                          Options.bytes_per_sync: 0
2025/06/24-00:35:25.576503 46                      Options.wal_bytes_per_sync: 0
2025/06/24-00:35:25.576504 46                   Options.strict_bytes_per_sync: 0
2025/06/24-00:35:25.576504 46       Options.compaction_readahead_size: 0
2025/06/24-00:35:25.576505 46                  Options.max_background_flushes: 1
2025/06/24-00:35:25.576505 46 Compression algorithms supported:
2025/06/24-00:35:25.576507 46 	kZSTD supported: 1
2025/06/24-00:35:25.576508 46 	kXpressCompression supported: 0
2025/06/24-00:35:25.576508 46 	kBZip2Compression supported: 0
2025/06/24-00:35:25.576509 46 	kZSTDNotFinalCompression supported: 1
2025/06/24-00:35:25.576510 46 	kLZ4Compression supported: 0
2025/06/24-00:35:25.576510 46 	kZlibCompression supported: 0
2025/06/24-00:35:25.576511 46 	kLZ4HCCompression supported: 0
2025/06/24-00:35:25.576511 46 	kSnappyCompression supported: 0
2025/06/24-00:35:25.576514 46 Fast CRC32 supported: Not supported on x86
2025/06/24-00:35:25.597670 46 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000025
2025/06/24-00:35:25.619600 46 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/06/24-00:35:25.619611 46               Options.comparator: leveldb.BytewiseComparator
2025/06/24-00:35:25.619613 46           Options.merge_operator: None
2025/06/24-00:35:25.619614 46        Options.compaction_filter: None
2025/06/24-00:35:25.619615 46        Options.compaction_filter_factory: None
2025/06/24-00:35:25.619616 46  Options.sst_partitioner_factory: None
2025/06/24-00:35:25.619617 46         Options.memtable_factory: SkipListFactory
2025/06/24-00:35:25.619618 46            Options.table_factory: BlockBasedTable
2025/06/24-00:35:25.619637 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f402a3014e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f402a240010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/24-00:35:25.619638 46        Options.write_buffer_size: 67108864
2025/06/24-00:35:25.619639 46  Options.max_write_buffer_number: 2
2025/06/24-00:35:25.619641 46        Options.compression[0]: NoCompression
2025/06/24-00:35:25.619642 46        Options.compression[1]: NoCompression
2025/06/24-00:35:25.619643 46        Options.compression[2]: ZSTD
2025/06/24-00:35:25.619644 46        Options.compression[3]: ZSTD
2025/06/24-00:35:25.619644 46        Options.compression[4]: ZSTD
2025/06/24-00:35:25.619645 46                  Options.bottommost_compression: Disabled
2025/06/24-00:35:25.619646 46       Options.prefix_extractor: nullptr
2025/06/24-00:35:25.619647 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/24-00:35:25.619648 46             Options.num_levels: 5
2025/06/24-00:35:25.619649 46        Options.min_write_buffer_number_to_merge: 1
2025/06/24-00:35:25.619649 46     Options.max_write_buffer_number_to_maintain: 0
2025/06/24-00:35:25.619650 46     Options.max_write_buffer_size_to_maintain: 0
2025/06/24-00:35:25.619651 46            Options.bottommost_compression_opts.window_bits: -14
2025/06/24-00:35:25.619652 46                  Options.bottommost_compression_opts.level: 32767
2025/06/24-00:35:25.619653 46               Options.bottommost_compression_opts.strategy: 0
2025/06/24-00:35:25.619654 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/24-00:35:25.619654 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/24-00:35:25.619655 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/24-00:35:25.619656 46                  Options.bottommost_compression_opts.enabled: false
2025/06/24-00:35:25.619657 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/24-00:35:25.619658 46            Options.compression_opts.window_bits: -14
2025/06/24-00:35:25.619659 46                  Options.compression_opts.level: 32767
2025/06/24-00:35:25.619660 46               Options.compression_opts.strategy: 0
2025/06/24-00:35:25.619660 46         Options.compression_opts.max_dict_bytes: 0
2025/06/24-00:35:25.619661 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/24-00:35:25.619844 46         Options.compression_opts.parallel_threads: 1
2025/06/24-00:35:25.619848 46                  Options.compression_opts.enabled: false
2025/06/24-00:35:25.619849 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/24-00:35:25.619850 46      Options.level0_file_num_compaction_trigger: 4
2025/06/24-00:35:25.619850 46          Options.level0_slowdown_writes_trigger: 20
2025/06/24-00:35:25.619851 46              Options.level0_stop_writes_trigger: 36
2025/06/24-00:35:25.619851 46                   Options.target_file_size_base: 67108864
2025/06/24-00:35:25.619852 46             Options.target_file_size_multiplier: 2
2025/06/24-00:35:25.619852 46                Options.max_bytes_for_level_base: 268435456
2025/06/24-00:35:25.619853 46 Options.level_compaction_dynamic_level_bytes: 0
2025/06/24-00:35:25.619853 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/24-00:35:25.619856 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/24-00:35:25.619857 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/24-00:35:25.619857 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/24-00:35:25.619858 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/24-00:35:25.619858 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/24-00:35:25.619859 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/24-00:35:25.619859 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/24-00:35:25.619859 46       Options.max_sequential_skip_in_iterations: 8
2025/06/24-00:35:25.619860 46                    Options.max_compaction_bytes: 1677721600
2025/06/24-00:35:25.619860 46                        Options.arena_block_size: 1048576
2025/06/24-00:35:25.619861 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/24-00:35:25.619861 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/24-00:35:25.619862 46       Options.rate_limit_delay_max_milliseconds: 100
2025/06/24-00:35:25.619863 46                Options.disable_auto_compactions: 0
2025/06/24-00:35:25.619865 46                        Options.compaction_style: kCompactionStyleLevel
2025/06/24-00:35:25.619866 46                          Options.compaction_pri: kMinOverlappingRatio
2025/06/24-00:35:25.619866 46 Options.compaction_options_universal.size_ratio: 1
2025/06/24-00:35:25.619867 46 Options.compaction_options_universal.min_merge_width: 2
2025/06/24-00:35:25.619867 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/24-00:35:25.619868 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/24-00:35:25.619868 46 Options.compaction_options_universal.compression_size_percent: -1
2025/06/24-00:35:25.619869 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/24-00:35:25.619869 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/24-00:35:25.619870 46 Options.compaction_options_fifo.allow_compaction: 0
2025/06/24-00:35:25.619874 46                   Options.table_properties_collectors: 
2025/06/24-00:35:25.619874 46                   Options.inplace_update_support: 0
2025/06/24-00:35:25.619875 46                 Options.inplace_update_num_locks: 10000
2025/06/24-00:35:25.619875 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/24-00:35:25.619876 46               Options.memtable_whole_key_filtering: 0
2025/06/24-00:35:25.619876 46   Options.memtable_huge_page_size: 0
2025/06/24-00:35:25.619877 46                           Options.bloom_locality: 0
2025/06/24-00:35:25.619877 46                    Options.max_successive_merges: 0
2025/06/24-00:35:25.619878 46                Options.optimize_filters_for_hits: 0
2025/06/24-00:35:25.619878 46                Options.paranoid_file_checks: 0
2025/06/24-00:35:25.619879 46                Options.force_consistency_checks: 1
2025/06/24-00:35:25.619879 46                Options.report_bg_io_stats: 0
2025/06/24-00:35:25.619879 46                               Options.ttl: 2592000
2025/06/24-00:35:25.620038 46          Options.periodic_compaction_seconds: 0
2025/06/24-00:35:25.620041 46                       Options.enable_blob_files: false
2025/06/24-00:35:25.620042 46                           Options.min_blob_size: 0
2025/06/24-00:35:25.620043 46                          Options.blob_file_size: 268435456
2025/06/24-00:35:25.620044 46                   Options.blob_compression_type: NoCompression
2025/06/24-00:35:25.620044 46          Options.enable_blob_garbage_collection: false
2025/06/24-00:35:25.620045 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/24-00:35:25.620046 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/24-00:35:25.620047 46          Options.blob_compaction_readahead_size: 0
2025/06/24-00:35:25.620729 46 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/06/24-00:35:25.620733 46               Options.comparator: leveldb.BytewiseComparator
2025/06/24-00:35:25.620734 46           Options.merge_operator: None
2025/06/24-00:35:25.620734 46        Options.compaction_filter: None
2025/06/24-00:35:25.620735 46        Options.compaction_filter_factory: None
2025/06/24-00:35:25.620735 46  Options.sst_partitioner_factory: None
2025/06/24-00:35:25.620736 46         Options.memtable_factory: SkipListFactory
2025/06/24-00:35:25.620736 46            Options.table_factory: BlockBasedTable
2025/06/24-00:35:25.620745 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f402a3014e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f402a240010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/24-00:35:25.620746 46        Options.write_buffer_size: 67108864
2025/06/24-00:35:25.620747 46  Options.max_write_buffer_number: 2
2025/06/24-00:35:25.620747 46        Options.compression[0]: NoCompression
2025/06/24-00:35:25.620748 46        Options.compression[1]: NoCompression
2025/06/24-00:35:25.620748 46        Options.compression[2]: ZSTD
2025/06/24-00:35:25.620749 46        Options.compression[3]: ZSTD
2025/06/24-00:35:25.620749 46        Options.compression[4]: ZSTD
2025/06/24-00:35:25.620750 46                  Options.bottommost_compression: Disabled
2025/06/24-00:35:25.620750 46       Options.prefix_extractor: nullptr
2025/06/24-00:35:25.620751 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/24-00:35:25.620751 46             Options.num_levels: 5
2025/06/24-00:35:25.620752 46        Options.min_write_buffer_number_to_merge: 1
2025/06/24-00:35:25.620752 46     Options.max_write_buffer_number_to_maintain: 0
2025/06/24-00:35:25.620753 46     Options.max_write_buffer_size_to_maintain: 0
2025/06/24-00:35:25.620753 46            Options.bottommost_compression_opts.window_bits: -14
2025/06/24-00:35:25.620754 46                  Options.bottommost_compression_opts.level: 32767
2025/06/24-00:35:25.620754 46               Options.bottommost_compression_opts.strategy: 0
2025/06/24-00:35:25.620755 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/24-00:35:25.620755 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/24-00:35:25.620879 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/24-00:35:25.620880 46                  Options.bottommost_compression_opts.enabled: false
2025/06/24-00:35:25.620880 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/24-00:35:25.620881 46            Options.compression_opts.window_bits: -14
2025/06/24-00:35:25.620882 46                  Options.compression_opts.level: 32767
2025/06/24-00:35:25.620882 46               Options.compression_opts.strategy: 0
2025/06/24-00:35:25.620883 46         Options.compression_opts.max_dict_bytes: 0
2025/06/24-00:35:25.620883 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/24-00:35:25.620883 46         Options.compression_opts.parallel_threads: 1
2025/06/24-00:35:25.620884 46                  Options.compression_opts.enabled: false
2025/06/24-00:35:25.620884 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/24-00:35:25.620885 46      Options.level0_file_num_compaction_trigger: 4
2025/06/24-00:35:25.620885 46          Options.level0_slowdown_writes_trigger: 20
2025/06/24-00:35:25.620886 46              Options.level0_stop_writes_trigger: 36
2025/06/24-00:35:25.620886 46                   Options.target_file_size_base: 67108864
2025/06/24-00:35:25.620887 46             Options.target_file_size_multiplier: 2
2025/06/24-00:35:25.620887 46                Options.max_bytes_for_level_base: 268435456
2025/06/24-00:35:25.620888 46 Options.level_compaction_dynamic_level_bytes: 0
2025/06/24-00:35:25.620888 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/24-00:35:25.620889 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/24-00:35:25.620890 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/24-00:35:25.620891 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/24-00:35:25.620891 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/24-00:35:25.620891 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/24-00:35:25.620892 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/24-00:35:25.620892 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/24-00:35:25.620893 46       Options.max_sequential_skip_in_iterations: 8
2025/06/24-00:35:25.620893 46                    Options.max_compaction_bytes: 1677721600
2025/06/24-00:35:25.620894 46                        Options.arena_block_size: 1048576
2025/06/24-00:35:25.620894 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/24-00:35:25.620895 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/24-00:35:25.620895 46       Options.rate_limit_delay_max_milliseconds: 100
2025/06/24-00:35:25.620896 46                Options.disable_auto_compactions: 0
2025/06/24-00:35:25.620897 46                        Options.compaction_style: kCompactionStyleLevel
2025/06/24-00:35:25.620898 46                          Options.compaction_pri: kMinOverlappingRatio
2025/06/24-00:35:25.620898 46 Options.compaction_options_universal.size_ratio: 1
2025/06/24-00:35:25.620899 46 Options.compaction_options_universal.min_merge_width: 2
2025/06/24-00:35:25.620899 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/24-00:35:25.620900 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/24-00:35:25.620900 46 Options.compaction_options_universal.compression_size_percent: -1
2025/06/24-00:35:25.620901 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/24-00:35:25.620901 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/24-00:35:25.620902 46 Options.compaction_options_fifo.allow_compaction: 0
2025/06/24-00:35:25.620905 46                   Options.table_properties_collectors: 
2025/06/24-00:35:25.620905 46                   Options.inplace_update_support: 0
2025/06/24-00:35:25.620906 46                 Options.inplace_update_num_locks: 10000
2025/06/24-00:35:25.620906 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/24-00:35:25.621046 46               Options.memtable_whole_key_filtering: 0
2025/06/24-00:35:25.621049 46   Options.memtable_huge_page_size: 0
2025/06/24-00:35:25.621050 46                           Options.bloom_locality: 0
2025/06/24-00:35:25.621051 46                    Options.max_successive_merges: 0
2025/06/24-00:35:25.621051 46                Options.optimize_filters_for_hits: 0
2025/06/24-00:35:25.621051 46                Options.paranoid_file_checks: 0
2025/06/24-00:35:25.621052 46                Options.force_consistency_checks: 1
2025/06/24-00:35:25.621052 46                Options.report_bg_io_stats: 0
2025/06/24-00:35:25.621053 46                               Options.ttl: 2592000
2025/06/24-00:35:25.621053 46          Options.periodic_compaction_seconds: 0
2025/06/24-00:35:25.621054 46                       Options.enable_blob_files: false
2025/06/24-00:35:25.621055 46                           Options.min_blob_size: 0
2025/06/24-00:35:25.621055 46                          Options.blob_file_size: 268435456
2025/06/24-00:35:25.621056 46                   Options.blob_compression_type: NoCompression
2025/06/24-00:35:25.621056 46          Options.enable_blob_garbage_collection: false
2025/06/24-00:35:25.621057 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/24-00:35:25.621059 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/24-00:35:25.621059 46          Options.blob_compaction_readahead_size: 0
2025/06/24-00:35:25.705653 46 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000025 succeeded,manifest_file_number is 25, next_file_number is 41, last_sequence is 1228499, log_number is 38,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 38
2025/06/24-00:35:25.705662 46 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 38
2025/06/24-00:35:25.705663 46 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 21
2025/06/24-00:35:25.729155 46 [db/version_set.cc:4409] Creating manifest 42
2025/06/24-00:35:25.795573 46 EVENT_LOG_v1 {"time_micros": 1750725325795566, "job": 1, "event": "recovery_started", "wal_files": [38]}
2025/06/24-00:35:25.795579 46 [db/db_impl/db_impl_open.cc:888] Recovering log #38 mode 2
2025/06/24-00:35:28.871618 46 EVENT_LOG_v1 {"time_micros": 1750725328871597, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 43, "file_size": 25769794, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 25755830, "index_size": 13034, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 22763603, "raw_average_key_size": 49, "raw_value_size": 18879487, "raw_average_value_size": 40, "num_data_blocks": 240, "num_entries": 461426, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750725328, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 43}}
2025/06/24-00:35:28.871932 46 [db/version_set.cc:4409] Creating manifest 44
2025/06/24-00:35:28.987516 46 EVENT_LOG_v1 {"time_micros": 1750725328987510, "job": 1, "event": "recovery_finished"}
2025/06/24-00:35:29.076879 46 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000038.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-00:35:29.077005 46 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f4027790700
2025/06/24-00:35:29.077607 46 DB pointer 0x7f4027621c00
2025/06/24-00:35:32.078022 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-00:35:32.078035 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6.5 total, 6.5 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6.5 total, 6.5 interval
Flush(GB): cumulative 0.024, interval 0.024
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 3.81 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.02 GB write, 3.81 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 1 last_copies: 2 last_secs: 4e-05 secs_since: 6
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6.5 total, 6.5 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 1 last_copies: 2 last_secs: 4e-05 secs_since: 6
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-00:45:32.079298 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-00:45:32.081290 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 606.5 total, 600.0 interval
Cumulative writes: 8671 writes, 8671 keys, 4350 commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8669 writes, 0 syncs, 8669.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8671 writes, 8671 keys, 4350 commit groups, 2.0 writes per commit group, ingest: 0.56 MB, 0.00 MB/s
Interval WAL: 8669 writes, 0 syncs, 8669.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 606.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.04 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 2 last_copies: 2 last_secs: 0.002242 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 606.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 2 last_copies: 2 last_secs: 0.002242 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-00:55:32.083024 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-00:55:32.083582 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1206.5 total, 600.0 interval
Cumulative writes: 17K writes, 17K keys, 8881 commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17K writes, 0 syncs, 17699.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9028 writes, 9028 keys, 4531 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9030 writes, 0 syncs, 9030.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1206.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 3 last_copies: 2 last_secs: 0.002132 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1206.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 3 last_copies: 2 last_secs: 0.002132 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-01:05:32.085519 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:05:32.086245 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1806.5 total, 600.0 interval
Cumulative writes: 26K writes, 26K keys, 13K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26K writes, 0 syncs, 26688.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8989 writes, 8989 keys, 4513 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8989 writes, 0 syncs, 8989.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1806.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 4 last_copies: 2 last_secs: 0.003449 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1806.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 4 last_copies: 2 last_secs: 0.003449 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-01:15:32.088195 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:15:32.088423 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2406.5 total, 600.0 interval
Cumulative writes: 35K writes, 35K keys, 17K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 35K writes, 0 syncs, 35649.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8961 writes, 8961 keys, 4576 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8961 writes, 0 syncs, 8961.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2406.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 5 last_copies: 2 last_secs: 0.005305 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2406.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 5 last_copies: 2 last_secs: 0.005305 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-01:25:32.090058 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:25:32.090474 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3006.5 total, 600.0 interval
Cumulative writes: 44K writes, 44K keys, 22K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 44K writes, 0 syncs, 44639.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8990 writes, 8990 keys, 4586 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8990 writes, 0 syncs, 8990.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3006.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 6 last_copies: 2 last_secs: 0.004427 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3006.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 6 last_copies: 2 last_secs: 0.004427 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-01:35:32.092062 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:35:32.093870 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3606.5 total, 600.0 interval
Cumulative writes: 53K writes, 53K keys, 27K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 53K writes, 0 syncs, 53625.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8986 writes, 8986 keys, 4540 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8986 writes, 0 syncs, 8986.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3606.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 7 last_copies: 2 last_secs: 0.002577 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3606.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 7 last_copies: 2 last_secs: 0.002577 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-01:45:32.096454 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:45:32.097331 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4206.5 total, 600.0 interval
Cumulative writes: 62K writes, 62K keys, 31K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 62K writes, 0 syncs, 62604.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8979 writes, 8979 keys, 4490 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8979 writes, 0 syncs, 8979.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4206.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 8 last_copies: 2 last_secs: 0.005336 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4206.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 8 last_copies: 2 last_secs: 0.005336 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-01:55:32.099981 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-01:55:32.100366 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4806.5 total, 600.0 interval
Cumulative writes: 71K writes, 71K keys, 35K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 71K writes, 0 syncs, 71595.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 4196 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4806.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 9 last_copies: 2 last_secs: 0.004364 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4806.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 9 last_copies: 2 last_secs: 0.004364 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-02:05:32.102035 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:05:32.103105 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5406.5 total, 600.0 interval
Cumulative writes: 80K writes, 80K keys, 40K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 80K writes, 0 syncs, 80583.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8988 writes, 8988 keys, 4260 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 8988 writes, 0 syncs, 8988.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5406.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 10 last_copies: 2 last_secs: 0.005804 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5406.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 10 last_copies: 2 last_secs: 0.005804 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-02:15:32.105733 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:15:32.106436 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6006.5 total, 600.0 interval
Cumulative writes: 89K writes, 89K keys, 44K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 89K writes, 0 syncs, 89580.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 4511 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6006.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 11 last_copies: 2 last_secs: 0.00304 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6006.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 11 last_copies: 2 last_secs: 0.00304 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-02:25:32.108058 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:25:32.109588 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6606.5 total, 600.0 interval
Cumulative writes: 98K writes, 98K keys, 49K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 98K writes, 0 syncs, 98571.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 4585 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6606.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 12 last_copies: 2 last_secs: 0.00261 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6606.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 12 last_copies: 2 last_secs: 0.00261 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-02:35:32.113882 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:35:32.114387 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7206.5 total, 600.0 interval
Cumulative writes: 107K writes, 107K keys, 53K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 107K writes, 0 syncs, 107571.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4591 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7206.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 13 last_copies: 2 last_secs: 0.002747 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7206.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 13 last_copies: 2 last_secs: 0.002747 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-02:45:32.116644 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:45:32.117446 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7806.5 total, 600.0 interval
Cumulative writes: 116K writes, 116K keys, 58K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 116K writes, 0 syncs, 116568.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 4585 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7806.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 14 last_copies: 2 last_secs: 0.002767 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7806.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 14 last_copies: 2 last_secs: 0.002767 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-02:55:32.120738 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-02:55:32.121347 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8406.5 total, 600.0 interval
Cumulative writes: 125K writes, 125K keys, 62K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 125K writes, 0 syncs, 125559.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 4528 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8406.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 15 last_copies: 2 last_secs: 0.002543 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8406.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 15 last_copies: 2 last_secs: 0.002543 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-03:05:32.123095 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:05:32.123601 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9006.5 total, 600.0 interval
Cumulative writes: 134K writes, 134K keys, 67K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 134K writes, 0 syncs, 134559.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4482 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9006.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 16 last_copies: 2 last_secs: 0.005569 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9006.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 16 last_copies: 2 last_secs: 0.005569 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-03:15:32.126282 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:15:32.126971 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9606.5 total, 600.0 interval
Cumulative writes: 143K writes, 143K keys, 71K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 143K writes, 0 syncs, 143559.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4413 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9606.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 17 last_copies: 2 last_secs: 0.003064 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9606.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 17 last_copies: 2 last_secs: 0.003064 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-03:25:32.130154 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:25:32.130576 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10206.5 total, 600.0 interval
Cumulative writes: 152K writes, 152K keys, 76K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 152K writes, 0 syncs, 152544.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8985 writes, 8985 keys, 4586 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8985 writes, 0 syncs, 8985.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10206.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 18 last_copies: 2 last_secs: 0.002372 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10206.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 18 last_copies: 2 last_secs: 0.002372 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-03:35:32.132164 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:35:32.133992 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10806.5 total, 600.0 interval
Cumulative writes: 161K writes, 161K keys, 80K commit groups, 2.0 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 161K writes, 0 syncs, 161505.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8961 writes, 8961 keys, 4494 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8961 writes, 0 syncs, 8961.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10806.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 19 last_copies: 2 last_secs: 0.005232 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10806.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 19 last_copies: 2 last_secs: 0.005232 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(18,5.00 MB,0.525095%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-03:45:32.135705 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:45:32.136040 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11406.5 total, 600.0 interval
Cumulative writes: 170K writes, 170K keys, 85K commit groups, 2.0 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 170K writes, 0 syncs, 170521.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9016 writes, 9016 keys, 4430 commit groups, 2.0 writes per commit group, ingest: 7.67 MB, 0.01 MB/s
Interval WAL: 9016 writes, 0 syncs, 9016.00 writes per sync, written: 0.01 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   143.35 MB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
  L1      3/0   184.33 MB   0.7      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      6/0   327.69 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11406.5 total, 600.0 interval
Flush(GB): cumulative 0.024, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.6 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 20 last_copies: 2 last_secs: 0.002694 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(23,5.31 MB,0.557987%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11406.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 20 last_copies: 2 last_secs: 0.002694 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(23,5.31 MB,0.557987%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-03:54:58.997001 567 [db/db_impl/db_impl_write.cc:1868] [default] New memtable created with log file: #48. Immutable memtables: 0.
2025/06/24-03:54:59.005736 49 [db/db_impl/db_impl_compaction_flush.cc:109] [JOB 3] Syncing log #45
2025/06/24-03:54:59.071976 49 (Original Log Time 2025/06/24-03:54:59.004375) [db/db_impl/db_impl_compaction_flush.cc:2779] Calling FlushMemTableToOutputFile with column family [default], flush slots available 1, compaction slots available 1, flush slots scheduled 1, compaction slots scheduled 0
2025/06/24-03:54:59.072066 49 [db/flush_job.cc:816] [default] [JOB 3] Flushing memtable with next log file: 48
2025/06/24-03:54:59.072879 49 EVENT_LOG_v1 {"time_micros": 1750737299072697, "job": 3, "event": "flush_started", "num_memtables": 1, "num_entries": 179109, "num_deletes": 0, "total_data_size": 60498886, "memory_usage": 66256032, "flush_reason": "Write Buffer Full"}
2025/06/24-03:54:59.072883 49 [db/flush_job.cc:845] [default] [JOB 3] Level-0 flush table #49: started
2025/06/24-03:55:00.829700 49 EVENT_LOG_v1 {"time_micros": 1750737300829535, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 49, "file_size": 53985555, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 53976457, "index_size": 8141, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 8835731, "raw_average_key_size": 49, "raw_value_size": 51304682, "raw_average_value_size": 286, "num_data_blocks": 151, "num_entries": 179109, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750725354, "oldest_key_time": 1750725354, "file_creation_time": 1750737299, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 49}}
2025/06/24-03:55:00.829791 49 [db/flush_job.cc:930] [default] [JOB 3] Level-0 flush table #49: 53985555 bytes OK
2025/06/24-03:55:00.838887 49 [db/flush_job.cc:983] [default] [JOB 3] Flush lasted 1766940 microseconds, and 141686 cpu microseconds.
2025/06/24-03:55:00.896921 49 (Original Log Time 2025/06/24-03:55:00.839078) [db/memtable_list.cc:469] [default] Level-0 commit table #49 started
2025/06/24-03:55:00.896926 49 (Original Log Time 2025/06/24-03:55:00.894751) [db/memtable_list.cc:675] [default] Level-0 commit table #49: memtable #1 done
2025/06/24-03:55:00.896927 49 (Original Log Time 2025/06/24-03:55:00.894789) EVENT_LOG_v1 {"time_micros": 1750737300894779, "job": 3, "event": "flush_finished", "output_compression": "NoCompression", "lsm_state": [4, 3, 0, 0, 0], "immutable_memtables": 0}
2025/06/24-03:55:00.896928 49 (Original Log Time 2025/06/24-03:55:00.894856) [db/db_impl/db_impl_compaction_flush.cc:288] [default] Level summary: files[4 3 0 0 0] max score 1.00
2025/06/24-03:55:00.897455 49 [db/db_impl/db_impl_files.cc:430] [JOB 3] Try to delete WAL files size 60323707, prev total WAL file size 60325763, number of live WAL files 2.
2025/06/24-03:55:00.898099 47 [db/compaction/compaction_job.cc:2331] [default] [JOB 4] Compacting 4@0 + 3@1 files to L1, score 1.00
2025/06/24-03:55:00.898107 47 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 7 Base level 0, inputs: [49(51MB) 43(24MB) 39(59MB) 37(59MB)], [33(64MB) 34(64MB) 35(55MB)]
2025/06/24-03:55:00.898546 47 EVENT_LOG_v1 {"time_micros": 1750737300898222, "job": 4, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [49, 43, 39, 37], "files_L1": [33, 34, 35], "score": 1, "input_data_size": 397588712}
2025/06/24-03:55:00.972952 49 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000045.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:02.674114 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 4] Generated table #50: 43899 keys, 67583607 bytes
2025/06/24-03:55:02.674149 47 EVENT_LOG_v1 {"time_micros": 1750737302674136, "cf_name": "default", "job": 4, "event": "table_file_creation", "file_number": 50, "file_size": 67583607, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67576743, "index_size": 5912, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2149435, "raw_average_key_size": 48, "raw_value_size": 66919474, "raw_average_value_size": 1524, "num_data_blocks": 110, "num_entries": 43899, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750737300, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 50}}
2025/06/24-03:55:03.946000 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 4] Generated table #51: 5027 keys, 67842408 bytes
2025/06/24-03:55:03.946032 47 EVENT_LOG_v1 {"time_micros": 1750737303946020, "cf_name": "default", "job": 4, "event": "table_file_creation", "file_number": 51, "file_size": 67842408, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67836497, "index_size": 4961, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 246323, "raw_average_key_size": 49, "raw_value_size": 67758841, "raw_average_value_size": 13478, "num_data_blocks": 92, "num_entries": 5027, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750737302, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 51}}
2025/06/24-03:55:05.553598 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 4] Generated table #52: 4956 keys, 67195891 bytes
2025/06/24-03:55:05.553631 47 EVENT_LOG_v1 {"time_micros": 1750737305553618, "cf_name": "default", "job": 4, "event": "table_file_creation", "file_number": 52, "file_size": 67195891, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67190031, "index_size": 4910, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 242844, "raw_average_key_size": 49, "raw_value_size": 67113430, "raw_average_value_size": 13541, "num_data_blocks": 91, "num_entries": 4956, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750737303, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 52}}
2025/06/24-03:55:08.024877 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 4] Generated table #53: 216339 keys, 67855988 bytes
2025/06/24-03:55:08.024928 47 EVENT_LOG_v1 {"time_micros": 1750737308024915, "cf_name": "default", "job": 4, "event": "table_file_creation", "file_number": 53, "file_size": 67855988, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67844997, "index_size": 10038, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 10600611, "raw_average_key_size": 49, "raw_value_size": 64623529, "raw_average_value_size": 298, "num_data_blocks": 186, "num_entries": 216339, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750737305, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 53}}
2025/06/24-03:55:10.579096 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 4] Generated table #54: 84332 keys, 67173419 bytes
2025/06/24-03:55:10.579130 47 EVENT_LOG_v1 {"time_micros": 1750737310579117, "cf_name": "default", "job": 4, "event": "table_file_creation", "file_number": 54, "file_size": 67173419, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 67148533, "index_size": 23932, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 4214245, "raw_average_key_size": 49, "raw_value_size": 65875620, "raw_average_value_size": 781, "num_data_blocks": 437, "num_entries": 84332, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750737308, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 54}}
2025/06/24-03:55:12.935346 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 4] Generated table #55: 1514481 keys, 59936638 bytes
2025/06/24-03:55:12.935390 47 EVENT_LOG_v1 {"time_micros": 1750737312935374, "cf_name": "default", "job": 4, "event": "table_file_creation", "file_number": 55, "file_size": 59936638, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 59889491, "index_size": 46193, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 74882162, "raw_average_key_size": 49, "raw_value_size": 37306275, "raw_average_value_size": 24, "num_data_blocks": 850, "num_entries": 1514481, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750737310, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 55}}
2025/06/24-03:55:12.954179 47 [db/compaction/compaction_job.cc:1998] [default] [JOB 4] Compacted 4@0 + 3@1 files to L1 => 397587951 bytes
2025/06/24-03:55:12.967674 47 (Original Log Time 2025/06/24-03:55:12.967528) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 6 0 0 0] max score 1.48, MB/sec: 33.0 rd, 33.0 wr, level 1, files in(4, 3) out(6 +0 blob) MB in(194.8, 184.3 +0.0 blob) out(379.2 +0.0 blob), read-write-amplify(3.9) write-amplify(1.9) OK, records in: 1869034, records dropped: 0 output_compression: NoCompression
2025/06/24-03:55:12.967678 47 (Original Log Time 2025/06/24-03:55:12.967588) EVENT_LOG_v1 {"time_micros": 1750737312967564, "job": 4, "event": "compaction_finished", "compaction_time_micros": 12037175, "compaction_time_cpu_micros": 862328, "output_level": 1, "num_output_files": 6, "total_output_size": 397587951, "num_input_records": 1869034, "num_output_records": 1869034, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 6, 0, 0, 0]}
2025/06/24-03:55:12.972018 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000049.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:12.972039 47 EVENT_LOG_v1 {"time_micros": 1750737312972037, "job": 4, "event": "table_file_deletion", "file_number": 49}
2025/06/24-03:55:12.992577 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000043.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:12.992603 47 EVENT_LOG_v1 {"time_micros": 1750737312992599, "job": 4, "event": "table_file_deletion", "file_number": 43}
2025/06/24-03:55:13.005557 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000039.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:13.005585 47 EVENT_LOG_v1 {"time_micros": 1750737313005582, "job": 4, "event": "table_file_deletion", "file_number": 39}
2025/06/24-03:55:13.031177 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000037.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:13.031225 47 EVENT_LOG_v1 {"time_micros": 1750737313031210, "job": 4, "event": "table_file_deletion", "file_number": 37}
2025/06/24-03:55:13.058697 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000035.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:13.058740 47 EVENT_LOG_v1 {"time_micros": 1750737313058723, "job": 4, "event": "table_file_deletion", "file_number": 35}
2025/06/24-03:55:13.069552 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000034.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:13.069581 47 EVENT_LOG_v1 {"time_micros": 1750737313069569, "job": 4, "event": "table_file_deletion", "file_number": 34}
2025/06/24-03:55:13.079673 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000033.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:13.079712 47 EVENT_LOG_v1 {"time_micros": 1750737313079696, "job": 4, "event": "table_file_deletion", "file_number": 33}
2025/06/24-03:55:13.080852 47 [db/compaction/compaction_job.cc:2331] [default] [JOB 5] Compacting 1@1 files to L2, score 1.48
2025/06/24-03:55:13.080856 47 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 8 Base level 1, inputs: [50(64MB)]
2025/06/24-03:55:13.080871 47 EVENT_LOG_v1 {"time_micros": 1750737313080858, "job": 5, "event": "compaction_started", "compaction_reason": "LevelMaxLevelSize", "files_L1": [50], "score": 1.48113, "input_data_size": 67583607}
2025/06/24-03:55:14.112367 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 5] Generated table #56: 43899 keys, 58195710 bytes
2025/06/24-03:55:14.112400 47 EVENT_LOG_v1 {"time_micros": 1750737314112387, "cf_name": "default", "job": 5, "event": "table_file_creation", "file_number": 56, "file_size": 58195710, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 58193197, "index_size": 5892, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2149435, "raw_average_key_size": 48, "raw_value_size": 66919474, "raw_average_value_size": 1524, "num_data_blocks": 110, "num_entries": 43899, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "ZSTD", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750737313, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 56}}
2025/06/24-03:55:14.160584 47 [db/compaction/compaction_job.cc:1998] [default] [JOB 5] Compacted 1@1 files to L2 => 58195710 bytes
2025/06/24-03:55:14.168903 47 (Original Log Time 2025/06/24-03:55:14.168853) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 5 1 0 0] max score 1.23, MB/sec: 65.3 rd, 56.2 wr, level 2, files in(1, 0) out(1 +0 blob) MB in(64.5, 0.0 +0.0 blob) out(55.5 +0.0 blob), read-write-amplify(1.9) write-amplify(0.9) OK, records in: 43899, records dropped: 0 output_compression: ZSTD
2025/06/24-03:55:14.168906 47 (Original Log Time 2025/06/24-03:55:14.168875) EVENT_LOG_v1 {"time_micros": 1750737314168869, "job": 5, "event": "compaction_finished", "compaction_time_micros": 1034794, "compaction_time_cpu_micros": 186630, "output_level": 2, "num_output_files": 1, "total_output_size": 58195710, "num_input_records": 43899, "num_output_records": 43899, "num_subcompactions": 1, "output_compression": "ZSTD", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 5, 1, 0, 0]}
2025/06/24-03:55:14.173894 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000050.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:14.173909 47 EVENT_LOG_v1 {"time_micros": 1750737314173907, "job": 5, "event": "table_file_deletion", "file_number": 50}
2025/06/24-03:55:14.174046 47 [db/compaction/compaction_job.cc:2331] [default] [JOB 6] Compacting 1@1 files to L2, score 1.23
2025/06/24-03:55:14.174050 47 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 9 Base level 1, inputs: [51(64MB)]
2025/06/24-03:55:14.174058 47 EVENT_LOG_v1 {"time_micros": 1750737314174054, "job": 6, "event": "compaction_started", "compaction_reason": "LevelMaxLevelSize", "files_L1": [51], "score": 1.22936, "input_data_size": 67842408}
2025/06/24-03:55:15.270004 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 6] Generated table #57: 5027 keys, 58369768 bytes
2025/06/24-03:55:15.270039 47 EVENT_LOG_v1 {"time_micros": 1750737315270026, "cf_name": "default", "job": 6, "event": "table_file_creation", "file_number": 57, "file_size": 58369768, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 58367492, "index_size": 4960, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 246323, "raw_average_key_size": 49, "raw_value_size": 67758841, "raw_average_value_size": 13478, "num_data_blocks": 92, "num_entries": 5027, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "ZSTD", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230345, "oldest_key_time": 0, "file_creation_time": 1750737314, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 57}}
2025/06/24-03:55:15.283162 47 [db/compaction/compaction_job.cc:1998] [default] [JOB 6] Compacted 1@1 files to L2 => 58369768 bytes
2025/06/24-03:55:15.292584 47 (Original Log Time 2025/06/24-03:55:15.292530) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 4 2 0 0] max score 0.98, MB/sec: 61.9 rd, 53.2 wr, level 2, files in(1, 0) out(1 +0 blob) MB in(64.7, 0.0 +0.0 blob) out(55.7 +0.0 blob), read-write-amplify(1.9) write-amplify(0.9) OK, records in: 5027, records dropped: 0 output_compression: ZSTD
2025/06/24-03:55:15.292587 47 (Original Log Time 2025/06/24-03:55:15.292551) EVENT_LOG_v1 {"time_micros": 1750737315292544, "job": 6, "event": "compaction_finished", "compaction_time_micros": 1096479, "compaction_time_cpu_micros": 182148, "output_level": 2, "num_output_files": 1, "total_output_size": 58369768, "num_input_records": 5027, "num_output_records": 5027, "num_subcompactions": 1, "output_compression": "ZSTD", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 4, 2, 0, 0]}
2025/06/24-03:55:15.301993 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000051.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-03:55:15.302015 47 EVENT_LOG_v1 {"time_micros": 1750737315302013, "job": 6, "event": "table_file_deletion", "file_number": 51}
2025/06/24-03:55:32.137930 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-03:55:32.138075 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12006.5 total, 600.0 interval
Cumulative writes: 179K writes, 179K keys, 90K commit groups, 2.0 writes per commit group, ingest: 0.06 GB, 0.00 MB/s
Cumulative WAL: 179K writes, 0 syncs, 179613.00 writes per sync, written: 0.06 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9092 writes, 9092 keys, 4916 commit groups, 1.8 writes per commit group, ingest: 41.64 MB, 0.07 MB/s
Interval WAL: 9092 writes, 0 syncs, 9092.00 writes per sync, written: 0.04 GB, 0.07 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0     32.2      2.36              0.14         2    1.181       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      6/0   361.18 MB   0.0      0.5     0.3      0.2       0.6      0.4       0.0   7.4     30.7     34.3     16.53              1.37         5    3.306   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0  10.5     31.9     34.0     15.94              1.37         4    3.984   1917K      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   0.0      0.0     29.1      1.77              0.14         1    1.767       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12006.5 total, 600.0 interval
Flush(GB): cumulative 0.074, interval 0.050
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.55 GB write, 0.05 MB/s write, 0.50 GB read, 0.04 MB/s read, 16.5 seconds
Interval compaction: 0.53 GB write, 0.90 MB/s write, 0.50 GB read, 0.85 MB/s read, 15.9 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 21 last_copies: 2 last_secs: 0.000902 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(27,6.94 MB,0.728716%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12006.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 21 last_copies: 2 last_secs: 0.000902 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(27,6.94 MB,0.728716%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-04:05:32.142181 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:05:32.142600 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12606.5 total, 600.0 interval
Cumulative writes: 188K writes, 188K keys, 94K commit groups, 2.0 writes per commit group, ingest: 0.10 GB, 0.01 MB/s
Cumulative WAL: 188K writes, 0 syncs, 188744.00 writes per sync, written: 0.10 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9131 writes, 9131 keys, 4783 commit groups, 1.9 writes per commit group, ingest: 41.69 MB, 0.07 MB/s
Interval WAL: 9131 writes, 0 syncs, 9131.00 writes per sync, written: 0.04 GB, 0.07 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0     32.2      2.36              0.14         2    1.181       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      6/0   361.18 MB   0.0      0.5     0.3      0.2       0.6      0.4       0.0   7.4     30.7     34.3     16.53              1.37         5    3.306   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   0.0      0.0     29.1      1.77              0.14         1    1.767       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12606.5 total, 600.0 interval
Flush(GB): cumulative 0.074, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.55 GB write, 0.04 MB/s write, 0.50 GB read, 0.04 MB/s read, 16.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 22 last_copies: 2 last_secs: 0.001946 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(27,6.94 MB,0.728716%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12606.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 22 last_copies: 2 last_secs: 0.001946 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(27,6.94 MB,0.728716%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-04:09:17.159241 2870 [db/db_impl/db_impl_write.cc:1868] [default] New memtable created with log file: #58. Immutable memtables: 0.
2025/06/24-04:09:17.164386 49 [db/db_impl/db_impl_compaction_flush.cc:109] [JOB 7] Syncing log #48
2025/06/24-04:09:17.388927 49 (Original Log Time 2025/06/24-04:09:17.163185) [db/db_impl/db_impl_compaction_flush.cc:2779] Calling FlushMemTableToOutputFile with column family [default], flush slots available 1, compaction slots available 1, flush slots scheduled 1, compaction slots scheduled 0
2025/06/24-04:09:17.389046 49 [db/flush_job.cc:816] [default] [JOB 7] Flushing memtable with next log file: 58
2025/06/24-04:09:17.389616 49 EVENT_LOG_v1 {"time_micros": 1750738157389609, "job": 7, "event": "flush_started", "num_memtables": 1, "num_entries": 13054, "num_deletes": 0, "total_data_size": 62601519, "memory_usage": 66267592, "flush_reason": "Write Buffer Full"}
2025/06/24-04:09:17.389630 49 [db/flush_job.cc:845] [default] [JOB 7] Level-0 flush table #59: started
2025/06/24-04:09:18.731946 49 EVENT_LOG_v1 {"time_micros": 1750738158731759, "cf_name": "default", "job": 7, "event": "table_file_creation", "file_number": 59, "file_size": 62134904, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 62129222, "index_size": 4728, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 643776, "raw_average_key_size": 49, "raw_value_size": 61931386, "raw_average_value_size": 4744, "num_data_blocks": 88, "num_entries": 13054, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750737298, "oldest_key_time": 1750737298, "file_creation_time": 1750738157, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 59}}
2025/06/24-04:09:18.732148 49 [db/flush_job.cc:930] [default] [JOB 7] Level-0 flush table #59: 62134904 bytes OK
2025/06/24-04:09:18.745505 49 [db/flush_job.cc:983] [default] [JOB 7] Flush lasted 1356580 microseconds, and 122798 cpu microseconds.
2025/06/24-04:09:18.779584 49 (Original Log Time 2025/06/24-04:09:18.745644) [db/memtable_list.cc:469] [default] Level-0 commit table #59 started
2025/06/24-04:09:18.779589 49 (Original Log Time 2025/06/24-04:09:18.778795) [db/memtable_list.cc:675] [default] Level-0 commit table #59: memtable #1 done
2025/06/24-04:09:18.779590 49 (Original Log Time 2025/06/24-04:09:18.778822) EVENT_LOG_v1 {"time_micros": 1750738158778818, "job": 7, "event": "flush_finished", "output_compression": "NoCompression", "lsm_state": [1, 4, 2, 0, 0], "immutable_memtables": 0}
2025/06/24-04:09:18.779591 49 (Original Log Time 2025/06/24-04:09:18.778866) [db/db_impl/db_impl_compaction_flush.cc:288] [default] Level summary: files[1 4 2 0 0] max score 0.98
2025/06/24-04:09:18.780098 49 [db/db_impl/db_impl_files.cc:430] [JOB 7] Try to delete WAL files size 62592737, prev total WAL file size 62594537, number of live WAL files 2.
2025/06/24-04:09:18.782058 49 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000048.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-04:15:32.144990 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:15:32.145370 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13206.5 total, 600.0 interval
Cumulative writes: 197K writes, 197K keys, 99K commit groups, 2.0 writes per commit group, ingest: 0.14 GB, 0.01 MB/s
Cumulative WAL: 197K writes, 0 syncs, 197859.00 writes per sync, written: 0.14 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9115 writes, 9115 keys, 4845 commit groups, 1.9 writes per commit group, ingest: 41.03 MB, 0.07 MB/s
Interval WAL: 9115 writes, 0 syncs, 9115.00 writes per sync, written: 0.04 GB, 0.07 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   59.26 MB   0.2      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0     36.4      3.72              0.26         3    1.240       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      7/0   420.44 MB   0.0      0.5     0.3      0.2       0.6      0.4       0.0   4.6     28.4     35.0     17.89              1.50         6    2.981   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0     43.7      1.36              0.12         1    1.357       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   0.0      0.0     35.5      3.12              0.26         2    1.562       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13206.5 total, 600.0 interval
Flush(GB): cumulative 0.132, interval 0.058
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.61 GB write, 0.05 MB/s write, 0.50 GB read, 0.04 MB/s read, 17.9 seconds
Interval compaction: 0.06 GB write, 0.10 MB/s write, 0.00 GB read, 0.00 MB/s read, 1.4 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 23 last_copies: 2 last_secs: 0.001505 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(29,7.75 MB,0.81408%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13206.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 23 last_copies: 2 last_secs: 0.001505 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(29,7.75 MB,0.81408%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-04:23:44.340352 489 [db/db_impl/db_impl_write.cc:1868] [default] New memtable created with log file: #60. Immutable memtables: 0.
2025/06/24-04:23:44.344280 49 [db/db_impl/db_impl_compaction_flush.cc:109] [JOB 8] Syncing log #58
2025/06/24-04:23:44.430897 49 (Original Log Time 2025/06/24-04:23:44.343514) [db/db_impl/db_impl_compaction_flush.cc:2779] Calling FlushMemTableToOutputFile with column family [default], flush slots available 1, compaction slots available 1, flush slots scheduled 1, compaction slots scheduled 0
2025/06/24-04:23:44.430909 49 [db/flush_job.cc:816] [default] [JOB 8] Flushing memtable with next log file: 60
2025/06/24-04:23:44.431594 49 EVENT_LOG_v1 {"time_micros": 1750739024431577, "job": 8, "event": "flush_started", "num_memtables": 1, "num_entries": 13174, "num_deletes": 0, "total_data_size": 62670820, "memory_usage": 66368448, "flush_reason": "Write Buffer Full"}
2025/06/24-04:23:44.431611 49 [db/flush_job.cc:845] [default] [JOB 8] Level-0 flush table #61: started
2025/06/24-04:23:46.229704 49 EVENT_LOG_v1 {"time_micros": 1750739026229570, "cf_name": "default", "job": 8, "event": "table_file_creation", "file_number": 61, "file_size": 62199590, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 62193910, "index_size": 4726, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 649696, "raw_average_key_size": 49, "raw_value_size": 61994527, "raw_average_value_size": 4705, "num_data_blocks": 88, "num_entries": 13174, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750738157, "oldest_key_time": 1750738157, "file_creation_time": 1750739024, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 61}}
2025/06/24-04:23:46.229906 49 [db/flush_job.cc:930] [default] [JOB 8] Level-0 flush table #61: 62199590 bytes OK
2025/06/24-04:23:46.243322 49 [db/flush_job.cc:983] [default] [JOB 8] Flush lasted 1812427 microseconds, and 91748 cpu microseconds.
2025/06/24-04:23:46.277790 49 (Original Log Time 2025/06/24-04:23:46.243493) [db/memtable_list.cc:469] [default] Level-0 commit table #61 started
2025/06/24-04:23:46.277794 49 (Original Log Time 2025/06/24-04:23:46.276855) [db/memtable_list.cc:675] [default] Level-0 commit table #61: memtable #1 done
2025/06/24-04:23:46.277795 49 (Original Log Time 2025/06/24-04:23:46.277053) EVENT_LOG_v1 {"time_micros": 1750739026276875, "job": 8, "event": "flush_finished", "output_compression": "NoCompression", "lsm_state": [2, 4, 2, 0, 0], "immutable_memtables": 0}
2025/06/24-04:23:46.277796 49 (Original Log Time 2025/06/24-04:23:46.277096) [db/db_impl/db_impl_compaction_flush.cc:288] [default] Level summary: files[2 4 2 0 0] max score 0.98
2025/06/24-04:23:46.278373 49 [db/db_impl/db_impl_files.cc:430] [JOB 8] Try to delete WAL files size 62661726, prev total WAL file size 62663746, number of live WAL files 2.
2025/06/24-04:23:46.282370 49 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000058.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-04:25:32.146447 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:25:32.146643 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13806.5 total, 600.0 interval
Cumulative writes: 206K writes, 206K keys, 104K commit groups, 2.0 writes per commit group, ingest: 0.18 GB, 0.01 MB/s
Cumulative WAL: 206K writes, 0 syncs, 206977.00 writes per sync, written: 0.18 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9118 writes, 9118 keys, 4744 commit groups, 1.9 writes per commit group, ingest: 41.78 MB, 0.07 MB/s
Interval WAL: 9118 writes, 0 syncs, 9118.00 writes per sync, written: 0.04 GB, 0.07 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   118.57 MB   0.5      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     35.2      5.53              0.36         4    1.383       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      8/0   479.76 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   3.5     25.8     34.8     19.70              1.59         7    2.814   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0     32.7      1.81              0.09         1    1.812       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     34.5      4.94              0.36         3    1.645       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13806.5 total, 600.0 interval
Flush(GB): cumulative 0.190, interval 0.058
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.67 GB write, 0.05 MB/s write, 0.50 GB read, 0.04 MB/s read, 19.7 seconds
Interval compaction: 0.06 GB write, 0.10 MB/s write, 0.00 GB read, 0.00 MB/s read, 1.8 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 24 last_copies: 2 last_secs: 0.0004 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(31,8.56 MB,0.899445%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13806.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 24 last_copies: 2 last_secs: 0.0004 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(31,8.56 MB,0.899445%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-04:35:32.147441 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:35:32.229224 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 14406.5 total, 600.0 interval
Cumulative writes: 216K writes, 216K keys, 109K commit groups, 2.0 writes per commit group, ingest: 0.22 GB, 0.02 MB/s
Cumulative WAL: 216K writes, 0 syncs, 216077.00 writes per sync, written: 0.22 GB, 0.02 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9100 writes, 9100 keys, 4728 commit groups, 1.9 writes per commit group, ingest: 41.12 MB, 0.07 MB/s
Interval WAL: 9100 writes, 0 syncs, 9100.00 writes per sync, written: 0.04 GB, 0.07 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   118.57 MB   0.5      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     35.2      5.53              0.36         4    1.383       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      8/0   479.76 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   3.5     25.8     34.8     19.70              1.59         7    2.814   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     34.5      4.94              0.36         3    1.645       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14406.5 total, 600.0 interval
Flush(GB): cumulative 0.190, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.67 GB write, 0.05 MB/s write, 0.50 GB read, 0.04 MB/s read, 19.7 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 25 last_copies: 2 last_secs: 6.9e-05 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(31,8.56 MB,0.899445%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14406.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 25 last_copies: 2 last_secs: 6.9e-05 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(31,8.56 MB,0.899445%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-04:38:10.319250 769 [db/db_impl/db_impl_write.cc:1868] [default] New memtable created with log file: #62. Immutable memtables: 0.
2025/06/24-04:38:10.319478 49 [db/db_impl/db_impl_compaction_flush.cc:109] [JOB 9] Syncing log #60
2025/06/24-04:38:10.376616 49 (Original Log Time 2025/06/24-04:38:10.319462) [db/db_impl/db_impl_compaction_flush.cc:2779] Calling FlushMemTableToOutputFile with column family [default], flush slots available 1, compaction slots available 1, flush slots scheduled 1, compaction slots scheduled 0
2025/06/24-04:38:10.376625 49 [db/flush_job.cc:816] [default] [JOB 9] Flushing memtable with next log file: 62
2025/06/24-04:38:10.376644 49 EVENT_LOG_v1 {"time_micros": 1750739890376640, "job": 9, "event": "flush_started", "num_memtables": 1, "num_entries": 13123, "num_deletes": 0, "total_data_size": 62795202, "memory_usage": 66364504, "flush_reason": "Write Buffer Full"}
2025/06/24-04:38:10.376647 49 [db/flush_job.cc:845] [default] [JOB 9] Level-0 flush table #63: started
2025/06/24-04:38:11.212926 49 EVENT_LOG_v1 {"time_micros": 1750739891212904, "cf_name": "default", "job": 9, "event": "table_file_creation", "file_number": 63, "file_size": 62325953, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 62320270, "index_size": 4729, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 647180, "raw_average_key_size": 49, "raw_value_size": 62121527, "raw_average_value_size": 4733, "num_data_blocks": 88, "num_entries": 13123, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750739024, "oldest_key_time": 1750739024, "file_creation_time": 1750739890, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "RQ8MAS9M7DG6HMJAHPA3", "orig_file_number": 63}}
2025/06/24-04:38:11.213058 49 [db/flush_job.cc:930] [default] [JOB 9] Level-0 flush table #63: 62325953 bytes OK
2025/06/24-04:38:11.223353 49 [db/flush_job.cc:983] [default] [JOB 9] Flush lasted 846741 microseconds, and 64789 cpu microseconds.
2025/06/24-04:38:11.255208 49 (Original Log Time 2025/06/24-04:38:11.223361) [db/memtable_list.cc:469] [default] Level-0 commit table #63 started
2025/06/24-04:38:11.255214 49 (Original Log Time 2025/06/24-04:38:11.254722) [db/memtable_list.cc:675] [default] Level-0 commit table #63: memtable #1 done
2025/06/24-04:38:11.255215 49 (Original Log Time 2025/06/24-04:38:11.254754) EVENT_LOG_v1 {"time_micros": 1750739891254749, "job": 9, "event": "flush_finished", "output_compression": "NoCompression", "lsm_state": [3, 4, 2, 0, 0], "immutable_memtables": 0}
2025/06/24-04:38:11.255215 49 (Original Log Time 2025/06/24-04:38:11.254778) [db/db_impl/db_impl_compaction_flush.cc:288] [default] Level summary: files[3 4 2 0 0] max score 0.98
2025/06/24-04:38:11.255223 49 [db/db_impl/db_impl_files.cc:430] [JOB 9] Try to delete WAL files size 62785289, prev total WAL file size 62786293, number of live WAL files 2.
2025/06/24-04:38:11.295820 49 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000060.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/24-04:45:32.231178 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:45:32.232167 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15006.6 total, 600.1 interval
Cumulative writes: 225K writes, 225K keys, 113K commit groups, 2.0 writes per commit group, ingest: 0.24 GB, 0.02 MB/s
Cumulative WAL: 225K writes, 0 syncs, 225128.00 writes per sync, written: 0.24 GB, 0.02 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9051 writes, 9051 keys, 4703 commit groups, 1.9 writes per commit group, ingest: 24.61 MB, 0.04 MB/s
Interval WAL: 9051 writes, 0 syncs, 9051.00 writes per sync, written: 0.02 GB, 0.04 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.1      0.1       0.0   1.0      0.0     70.2      0.85              0.06         1    0.847       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15006.6 total, 600.1 interval
Flush(GB): cumulative 0.248, interval 0.058
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.05 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.06 GB write, 0.10 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.8 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 26 last_copies: 2 last_secs: 0.000865 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15006.6 total, 600.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 26 last_copies: 2 last_secs: 0.000865 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-04:55:32.235016 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-04:55:32.236000 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15606.6 total, 600.0 interval
Cumulative writes: 234K writes, 234K keys, 118K commit groups, 2.0 writes per commit group, ingest: 0.24 GB, 0.02 MB/s
Cumulative WAL: 234K writes, 0 syncs, 234129.00 writes per sync, written: 0.24 GB, 0.02 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9001 writes, 9001 keys, 4480 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9001 writes, 0 syncs, 9001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15606.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.05 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 27 last_copies: 2 last_secs: 0.00725 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15606.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 27 last_copies: 2 last_secs: 0.00725 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-05:05:32.237445 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:05:32.237849 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16206.6 total, 600.0 interval
Cumulative writes: 243K writes, 243K keys, 123K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.02 MB/s
Cumulative WAL: 243K writes, 0 syncs, 243139.00 writes per sync, written: 0.25 GB, 0.02 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9010 writes, 9010 keys, 4578 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9010 writes, 0 syncs, 9010.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16206.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.05 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 28 last_copies: 2 last_secs: 0.003949 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16206.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 28 last_copies: 2 last_secs: 0.003949 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-05:15:32.238781 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:15:32.239153 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16806.6 total, 600.0 interval
Cumulative writes: 252K writes, 252K keys, 127K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 252K writes, 0 syncs, 252139.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4705 commit groups, 1.9 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16806.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.04 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 29 last_copies: 2 last_secs: 0.00343 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16806.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 29 last_copies: 2 last_secs: 0.00343 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-05:25:32.241686 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:25:32.242469 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 17406.6 total, 600.0 interval
Cumulative writes: 261K writes, 261K keys, 132K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 261K writes, 0 syncs, 261139.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4621 commit groups, 1.9 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17406.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.04 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 30 last_copies: 2 last_secs: 0.002621 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17406.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 30 last_copies: 2 last_secs: 0.002621 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-05:35:32.244962 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:35:32.245189 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18006.6 total, 600.0 interval
Cumulative writes: 270K writes, 270K keys, 136K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 270K writes, 0 syncs, 270139.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4299 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18006.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.04 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 31 last_copies: 2 last_secs: 0.003013 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18006.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 31 last_copies: 2 last_secs: 0.003013 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-05:45:32.246769 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:45:32.247022 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18606.6 total, 600.0 interval
Cumulative writes: 279K writes, 279K keys, 141K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 279K writes, 0 syncs, 279142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9003 writes, 9003 keys, 4438 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9003 writes, 0 syncs, 9003.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18606.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.04 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 32 last_copies: 2 last_secs: 0.004023 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18606.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 32 last_copies: 2 last_secs: 0.004023 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-05:55:32.248967 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-05:55:32.249348 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19206.6 total, 600.0 interval
Cumulative writes: 288K writes, 288K keys, 145K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 288K writes, 0 syncs, 288142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4153 commit groups, 2.2 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19206.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.04 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 33 last_copies: 2 last_secs: 0.004797 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19206.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 33 last_copies: 2 last_secs: 0.004797 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-06:05:32.250418 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:05:32.262079 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19806.6 total, 600.0 interval
Cumulative writes: 297K writes, 297K keys, 149K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 297K writes, 0 syncs, 297142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4233 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19806.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.04 MB/s write, 0.50 GB read, 0.03 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 34 last_copies: 2 last_secs: 0.002849 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19806.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 34 last_copies: 2 last_secs: 0.002849 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-06:15:32.264432 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:15:32.264776 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20406.6 total, 600.0 interval
Cumulative writes: 306K writes, 306K keys, 153K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 306K writes, 0 syncs, 306142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4231 commit groups, 2.1 writes per commit group, ingest: 0.57 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20406.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.04 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 35 last_copies: 2 last_secs: 0.002629 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20406.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 35 last_copies: 2 last_secs: 0.002629 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-06:25:32.266771 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:25:32.267158 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21006.6 total, 600.0 interval
Cumulative writes: 315K writes, 315K keys, 158K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 315K writes, 0 syncs, 315142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4376 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21006.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.04 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 36 last_copies: 2 last_secs: 0.00284 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21006.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 36 last_copies: 2 last_secs: 0.00284 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-06:35:32.269736 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:35:32.270402 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21606.6 total, 600.0 interval
Cumulative writes: 324K writes, 324K keys, 162K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 324K writes, 0 syncs, 324142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4547 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21606.6 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 37 last_copies: 2 last_secs: 0.003474 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21606.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 37 last_copies: 2 last_secs: 0.003474 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-06:45:32.271851 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:45:32.272132 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22206.7 total, 600.0 interval
Cumulative writes: 333K writes, 333K keys, 167K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 333K writes, 0 syncs, 333142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4417 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22206.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 38 last_copies: 2 last_secs: 0.004495 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22206.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 38 last_copies: 2 last_secs: 0.004495 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-06:55:32.273873 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-06:55:32.274389 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22806.7 total, 600.0 interval
Cumulative writes: 342K writes, 342K keys, 171K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 342K writes, 0 syncs, 342142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4510 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22806.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 39 last_copies: 2 last_secs: 0.003285 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22806.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 39 last_copies: 2 last_secs: 0.003285 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-07:05:32.275970 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:05:32.276416 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 23406.7 total, 600.0 interval
Cumulative writes: 351K writes, 351K keys, 176K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 351K writes, 0 syncs, 351142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4605 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 23406.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 40 last_copies: 2 last_secs: 0.002939 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 23406.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 40 last_copies: 2 last_secs: 0.002939 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-07:15:32.277516 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:15:32.277841 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24006.7 total, 600.0 interval
Cumulative writes: 360K writes, 360K keys, 180K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 360K writes, 0 syncs, 360142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4364 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24006.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 41 last_copies: 2 last_secs: 0.002734 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24006.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 41 last_copies: 2 last_secs: 0.002734 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-07:25:32.279086 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:25:32.279380 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24606.7 total, 600.0 interval
Cumulative writes: 369K writes, 369K keys, 185K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 369K writes, 0 syncs, 369142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4488 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24606.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 42 last_copies: 2 last_secs: 0.004856 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24606.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 42 last_copies: 2 last_secs: 0.004856 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-07:35:32.280912 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:35:32.281339 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 25206.7 total, 600.0 interval
Cumulative writes: 378K writes, 378K keys, 189K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 378K writes, 0 syncs, 378142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4453 commit groups, 2.0 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25206.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 43 last_copies: 2 last_secs: 0.003347 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25206.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 43 last_copies: 2 last_secs: 0.003347 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-07:45:32.282935 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:45:32.292284 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 25806.7 total, 600.0 interval
Cumulative writes: 387K writes, 387K keys, 193K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 387K writes, 0 syncs, 387142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4271 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25806.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 44 last_copies: 2 last_secs: 0.002438 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25806.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 44 last_copies: 2 last_secs: 0.002438 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-07:55:32.293954 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-07:55:32.294485 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 26406.7 total, 600.0 interval
Cumulative writes: 396K writes, 396K keys, 198K commit groups, 2.0 writes per commit group, ingest: 0.25 GB, 0.01 MB/s
Cumulative WAL: 396K writes, 0 syncs, 396142.00 writes per sync, written: 0.25 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4348 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 26406.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 45 last_copies: 2 last_secs: 0.003212 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 26406.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 45 last_copies: 2 last_secs: 0.003212 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-08:05:32.296453 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-08:05:32.296768 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27006.7 total, 600.0 interval
Cumulative writes: 405K writes, 405K keys, 202K commit groups, 2.0 writes per commit group, ingest: 0.26 GB, 0.01 MB/s
Cumulative WAL: 405K writes, 0 syncs, 405133.00 writes per sync, written: 0.26 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 4310 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27006.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 46 last_copies: 2 last_secs: 0.002973 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27006.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 46 last_copies: 2 last_secs: 0.002973 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/24-08:15:32.298293 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/24-08:15:32.298617 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27606.7 total, 600.0 interval
Cumulative writes: 414K writes, 414K keys, 206K commit groups, 2.0 writes per commit group, ingest: 0.26 GB, 0.01 MB/s
Cumulative WAL: 414K writes, 0 syncs, 414133.00 writes per sync, written: 0.26 GB, 0.01 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 4312 commit groups, 2.1 writes per commit group, ingest: 0.58 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0   178.01 MB   0.8      0.0     0.0      0.0       0.2      0.2       0.0   1.0      0.0     39.8      6.38              0.42         5    1.276       0      0       0.0       0.0
  L1      4/0   250.02 MB   1.0      0.4     0.2      0.2       0.4      0.2       0.0   1.9     31.5     31.5     12.04              0.86         1   12.037   1869K      0       0.0       0.0
  L2      2/0   111.17 MB   0.0      0.1     0.1      0.0       0.1      0.1       0.0   0.9     60.6     52.2      2.13              0.37         2    1.066     48K      0       0.0       0.0
 Sum      9/0   539.20 MB   0.0      0.5     0.3      0.2       0.7      0.5       0.0   2.9     24.7     36.2     20.55              1.65         8    2.568   1917K      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.5     0.3      0.2       0.5      0.3       0.0   0.0     35.9     34.6     14.17              1.23         3    4.723   1917K      0       0.0       0.0
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.2      0.2       0.0   0.0      0.0     39.7      5.78              0.42         4    1.446       0      0       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     41.2      0.60              0.00         1    0.596       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27606.7 total, 600.0 interval
Flush(GB): cumulative 0.248, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.73 GB write, 0.03 MB/s write, 0.50 GB read, 0.02 MB/s read, 20.5 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 47 last_copies: 2 last_secs: 0.002975 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27606.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f402a240010#8 capacity: 951.98 MB collections: 47 last_copies: 2 last_secs: 0.002975 secs_since: 6
Block cache entry stats(count,size,portion): DataBlock(33,9.38 MB,0.984809%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
