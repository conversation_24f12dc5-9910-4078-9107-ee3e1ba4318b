#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
装配体特征提取器 - 提取装配体的结构化信息、形状向量和语义向量

功能：
1. 从PostgreSQL数据库中提取装配体的结构化信息
2. 从Milvus向量数据库中提取形状向量和语义向量
3. 对数值特征进行Z-score标准化处理
4. 组合多模态特征用于相似度计算

结构化信息包括：
- 几何特征：长、宽、高、表面积、体积（5维）
- 物理特征：密度、质量（2维）
- 组成特征：零件数量（1维）
- 连接特征：关节数量、接触面数量（2维）
- 统计特征：零件体积标准差、零件质量标准差（2维）
总计：12维数值向量

鉴于数值向量中各维度物理量纲与数值范围的巨大差异，直接应用可能导致数值较大维度的特征不成比例地主导相似度计算。为消除此偏差，需要对数据集中所有资产的数值向量均进行了Z-score标准化处理。该处理使得每个特征维度都近似服从均值为0、标准差为1的标准正态分布，从而实现了跨维度信息的公平度量。

向量信息：
- 形状向量：从Milvus中的cad_collection集合的shape_vector字段提取（768维）
- 语义向量：从Milvus中的cad_collection集合的dense_vector字段提取（1024维）
"""

from __future__ import annotations

import logging
import pickle
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import psycopg2
from psycopg2.extras import RealDictCursor
from sklearn.preprocessing import StandardScaler
from sklearn.compose import ColumnTransformer
from sklearn.decomposition import PCA
from sklearn.preprocessing import normalize
from pymilvus import Collection, connections

from src.config import Config
from src.utils.database.milvus_utils import MilvusManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AssemblyFeatureExtractor:
    """装配体特征提取器"""
    
    # 特征提取配置常量
    FEATURE_EXTRACTION_CONFIG = {
        'milvus_collection_name': 'cad_collection',
        'structural_feature_dim': 12,    # 结构化特征维度（更新为12维）
        'shape_feature_dim': 768,        # 形状特征维度
        'semantic_feature_dim': 1024,    # 语义特征维度
        'pca_dim': 64,                   # PCA降维目标维度
        'batch_size': 1000,              # 批处理大小
        'max_assemblies_per_batch': 100, # 每批最大装配体数
        'continue_on_error': True,       # 遇到错误是否继续
        'max_retries': 3,                # 最大重试次数
        'retry_delay': 1,                # 重试延迟（秒）
        'feature_weights': {             # 特征权重
            'shape': 0.3,
            'semantic': 0.2,
            'structural': 0.5
        }
    }
    
    # 特征列名映射
    FEATURE_COLUMNS = {
        'numerical_features': [
            'length', 'width', 'height', 'area', 'volume',
            'density', 'mass', 'part_count',
            'joints_count', 'contacts_count',
            'part_volume_std', 'part_mass_std'
        ],
        'milvus_vector_fields': {
            'shape_vector': 'shape_vector',
            'semantic_vector': 'dense_vector',
        }
    }
    
    # 默认文件路径
    DEFAULT_PATHS = {
        'features_output': 'dataset/assembly_features.pkl',
        'scalers_output': 'dataset/assembly_feature_scalers.pkl',
        'pca_models_output': 'dataset/assembly_pca_models.pkl',
    }
    
    def __init__(self, 
                 db_config: Optional[Dict[str, str]] = None,
                 milvus_collection_name: str = None):
        """
        初始化特征提取器
        
        Args:
            db_config: 数据库配置（如为None则使用默认配置）
            milvus_collection_name: Milvus集合名称（如为None则使用默认）
        """
        self.config = Config()
        self.db_config = db_config or Config.POSTGRES_CONFIG
        
        # Milvus配置
        self.milvus_collection_name = (
            milvus_collection_name or 
            self.FEATURE_EXTRACTION_CONFIG['milvus_collection_name']
        )
        self.milvus_manager = MilvusManager(use_reranker=False)
        
        # 定义特征列名（与数据库表结构对应）
        self.numerical_features = self.FEATURE_COLUMNS['numerical_features']
        
        # 标准化器和PCA模型
        self.scaler = None
        self.fitted = False
        self.pca_shape = None
        self.pca_semantic = None
        self.pca_fitted = False
        
    def connect_db(self):
        """连接数据库"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def extract_structural_features(self, assembly_ids: Optional[List[str]] = None) -> pd.DataFrame:
        """
        从数据库提取装配体结构化特征
        
        Args:
            assembly_ids: 指定提取的装配体ID列表，如果为None则提取所有装配体
            
        Returns:
            包含结构化特征的DataFrame
        """
        conn = self.connect_db()
        
        try:
            # 构建SQL查询
            base_query = """
            SELECT 
                uuid as assembly_id,
                length,
                width,
                height,
                area,
                volume,
                density,
                mass,
                part_count,
                joints_count,
                contacts_count,
                part_volume_std,
                part_mass_std
            FROM cad_rag.assemblies
            """
            
            if assembly_ids:
                placeholders = ','.join(['%s'] * len(assembly_ids))
                query = f"{base_query} WHERE uuid IN ({placeholders})"
                params = assembly_ids
            else:
                query = base_query
                params = None
            
            # 执行查询
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                results = cursor.fetchall()
            
            # 转换为DataFrame
            df = pd.DataFrame(results)
            
            if df.empty:
                logger.warning("未找到任何装配体数据")
                return df
            
            # 处理空值
            df = self._handle_missing_values(df)
            
            logger.info(f"成功提取 {len(df)} 个装配体的结构化特征")
            return df
            
        except Exception as e:
            logger.error(f"提取结构化特征失败: {e}")
            raise
        finally:
            conn.close()
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        # 对数值列填充0或合理的默认值
        for col in self.numerical_features:
            if col in df.columns:
                if col in ['density']:
                    # 密度用常见材料密度填充（如钢的密度）
                    df[col] = df[col].fillna(7.85)
                elif col in ['part_count']:
                    # 零件数量至少为1
                    df[col] = df[col].fillna(1)
                else:
                    # 其他数值特征填充0
                    df[col] = df[col].fillna(0.0)
        
        return df
    
    def fit_scalers(self, df: pd.DataFrame):
        """训练标准化器"""
        if self.fitted:
            logger.warning("标准化器已经训练过，跳过训练")
            return
        
        # 提取数值特征
        feature_data = df[self.numerical_features].values
        
        # 创建并训练标准化器
        self.scaler = StandardScaler()
        self.scaler.fit(feature_data)
        self.fitted = True
        
        logger.info("标准化器训练完成")
    
    def transform_features(self, df: pd.DataFrame) -> np.ndarray:
        """转换特征（标准化）"""
        if not self.fitted:
            raise ValueError("必须先训练标准化器")
        
        # 提取并转换数值特征
        feature_data = df[self.numerical_features].values
        scaled_features = self.scaler.transform(feature_data)
        
        return scaled_features
    
    def get_assembly_vector_from_milvus(self, assembly_id: str) -> Optional[Dict[str, np.ndarray]]:
        """
        从Milvus获取装配体的向量特征
        
        Args:
            assembly_id: 装配体ID
            
        Returns:
            包含形状和语义向量的字典，如果未找到则返回None
        """
        try:
            # 连接Milvus
            if not connections.has_connection("default"):
                self.milvus_manager.connect()
            
            # 获取集合
            collection = Collection(self.milvus_collection_name)
            collection.load()
            
            # 查询向量
            expr = f'id == "{assembly_id}"'
            results = collection.query(
                expr=expr,
                output_fields=['shape_vector', 'dense_vector']
            )
            
            if not results:
                logger.warning(f"未找到装配体 {assembly_id} 的向量特征")
                return None
            
            result = results[0]
            return {
                'shape_vector': np.array(result['shape_vector']),
                'semantic_vector': np.array(result['dense_vector'])
            }
            
        except Exception as e:
            logger.error(f"从Milvus获取向量特征失败: {e}")
            return None
    
    def extract_vector_features_batch(self, assembly_ids: List[str]) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], List[str], List[str]]:
        """
        批量从Milvus提取向量特征
        
        Args:
            assembly_ids: 装配体ID列表
            
        Returns:
            tuple: (形状特征矩阵, 语义特征矩阵, 有形状特征的ID列表, 有语义特征的ID列表)
        """
        try:
            # 连接Milvus
            if not connections.has_connection("default"):
                self.milvus_manager.connect()
            
            # 获取集合
            collection = Collection(self.milvus_collection_name)
            collection.load()
            
            shape_vectors = []
            semantic_vectors = []
            shape_ids = []
            semantic_ids = []
            
            # 批量查询
            batch_size = self.FEATURE_EXTRACTION_CONFIG['batch_size']
            for i in range(0, len(assembly_ids), batch_size):
                batch_ids = assembly_ids[i:i + batch_size]
                
                # 构建查询表达式
                id_list = ', '.join([f'"{aid}"' for aid in batch_ids])
                expr = f'pk in [{id_list}]'
                
                results = collection.query(
                    expr=expr,
                    output_fields=['pk', 'shape_vector', 'dense_vector']
                )
                
                for result in results:
                    assembly_id = result['pk']
                    
                    # 处理形状向量
                    if 'shape_vector' in result and result['shape_vector']:
                        shape_vectors.append(np.array(result['shape_vector']))
                        shape_ids.append(assembly_id)
                    
                    # 处理语义向量
                    if 'dense_vector' in result and result['dense_vector']:
                        semantic_vectors.append(np.array(result['dense_vector']))
                        semantic_ids.append(assembly_id)
            
            # 转换为numpy数组
            shape_matrix = np.array(shape_vectors) if shape_vectors else None
            semantic_matrix = np.array(semantic_vectors) if semantic_vectors else None
            
            logger.info(f"批量提取向量特征完成：形状特征 {len(shape_ids)} 个，语义特征 {len(semantic_ids)} 个")
            
            return shape_matrix, semantic_matrix, shape_ids, semantic_ids
            
        except Exception as e:
            logger.error(f"批量提取向量特征失败: {e}")
            return None, None, [], []
    
    def fit_pca_models(self, features: Dict[str, Union[np.ndarray, List[str]]]):
        """训练PCA模型"""
        if self.pca_fitted:
            logger.warning("PCA模型已经训练过，跳过训练")
            return
        
        pca_dim = self.FEATURE_EXTRACTION_CONFIG['pca_dim']
        
        # 训练形状特征PCA
        if 'shape' in features and features['shape'] is not None:
            shape_data = features['shape']
            if shape_data.shape[1] > pca_dim:
                self.pca_shape = PCA(n_components=pca_dim)
                self.pca_shape.fit(shape_data)
                logger.info(f"形状特征PCA训练完成：{shape_data.shape[1]} -> {pca_dim}")
        
        # 训练语义特征PCA
        if 'semantic' in features and features['semantic'] is not None:
            semantic_data = features['semantic']
            if semantic_data.shape[1] > pca_dim:
                self.pca_semantic = PCA(n_components=pca_dim)
                self.pca_semantic.fit(semantic_data)
                logger.info(f"语义特征PCA训练完成：{semantic_data.shape[1]} -> {pca_dim}")
        
        self.pca_fitted = True
    
    def transform_features_with_pca(self, features: Dict[str, Union[np.ndarray, List[str]]]) -> Dict[str, Union[np.ndarray, List[str]]]:
        """使用PCA转换特征并进行L2归一化"""
        processed_features = {}
        
        # 复制assembly_ids
        if 'assembly_ids' in features:
            processed_features['assembly_ids'] = features['assembly_ids']
        
        # 处理结构化特征（L2归一化）
        if 'structural' in features:
            structural = features['structural']
            processed_features['structural'] = normalize(structural, norm='l2')
            logger.info(f"结构化特征L2归一化完成：{structural.shape}")
        
        # 处理形状特征（PCA降维 + L2归一化）
        if 'shape' in features and self.pca_shape is not None:
            shape_reduced = self.pca_shape.transform(features['shape'])
            processed_features['shape'] = normalize(shape_reduced, norm='l2')
            logger.info(f"形状特征PCA降维和L2归一化完成：{features['shape'].shape} -> {shape_reduced.shape}")
        elif 'shape' in features:
            # 如果不需要PCA，直接归一化
            processed_features['shape'] = normalize(features['shape'], norm='l2')
            logger.info(f"形状特征L2归一化完成：{features['shape'].shape}")
        
        # 处理语义特征（PCA降维 + L2归一化）
        if 'semantic' in features and self.pca_semantic is not None:
            semantic_reduced = self.pca_semantic.transform(features['semantic'])
            processed_features['semantic'] = normalize(semantic_reduced, norm='l2')
            logger.info(f"语义特征PCA降维和L2归一化完成：{features['semantic'].shape} -> {semantic_reduced.shape}")
        elif 'semantic' in features:
            # 如果不需要PCA，直接归一化
            processed_features['semantic'] = normalize(features['semantic'], norm='l2')
            logger.info(f"语义特征L2归一化完成：{features['semantic'].shape}")
        
        return processed_features
    
    def fuse_features(self, features: Dict[str, Union[np.ndarray, List[str]]]) -> np.ndarray:
        """
        特征融合（加权拼接）
        
        Args:
            features: 包含不同类型特征的字典
            
        Returns:
            融合后的特征矩阵
        """
        weights = self.FEATURE_EXTRACTION_CONFIG['feature_weights']
        feature_arrays = []
        
        # 按权重拼接特征
        if 'shape' in features and features['shape'] is not None:
            weighted_shape = features['shape'] * weights['shape']
            feature_arrays.append(weighted_shape)
        
        if 'semantic' in features and features['semantic'] is not None:
            weighted_semantic = features['semantic'] * weights['semantic']
            feature_arrays.append(weighted_semantic)
        
        if 'structural' in features and features['structural'] is not None:
            weighted_structural = features['structural'] * weights['structural']
            feature_arrays.append(weighted_structural)
        
        if not feature_arrays:
            logger.warning("没有可用于融合的特征")
            return np.array([])
        
        # 拼接所有特征
        fused_features = np.concatenate(feature_arrays, axis=1)
        logger.info(f"特征融合完成：{fused_features.shape}")
        
        return fused_features
    
    def align_features(self, features: Dict[str, Union[np.ndarray, List[str]]]) -> Dict[str, Union[np.ndarray, List[str]]]:
        """
        对齐不同类型的特征（确保装配体ID一致）
        
        Args:
            features: 包含不同类型特征的字典
            
        Returns:
            对齐后的特征字典
        """
        # 获取所有特征的装配体ID集合
        assembly_ids_set = set(features['assembly_ids'])
        
        # 如果有形状特征，找到交集
        if 'shape_ids' in features:
            shape_ids_set = set(features['shape_ids'])
            assembly_ids_set = assembly_ids_set.intersection(shape_ids_set)
        
        # 如果有语义特征，找到交集
        if 'semantic_ids' in features:
            semantic_ids_set = set(features['semantic_ids'])
            assembly_ids_set = assembly_ids_set.intersection(semantic_ids_set)
        
        # 转换为有序列表
        aligned_assembly_ids = sorted(list(assembly_ids_set))
        
        logger.info(f"对齐前装配体数: {len(features['assembly_ids'])}")
        logger.info(f"对齐后装配体数: {len(aligned_assembly_ids)}")
        
        # 创建索引映射
        aligned_features = {'assembly_ids': aligned_assembly_ids}
        
        # 对齐结构化特征
        if 'structural' in features:
            original_assembly_ids = features['assembly_ids']
            indices = [original_assembly_ids.index(assembly_id) for assembly_id in aligned_assembly_ids]
            aligned_features['structural'] = features['structural'][indices]
            
        # 对齐形状特征
        if 'shape' in features and 'shape_ids' in features:
            original_shape_ids = features['shape_ids']
            indices = [original_shape_ids.index(assembly_id) for assembly_id in aligned_assembly_ids]
            aligned_features['shape'] = features['shape'][indices]
            
        # 对齐语义特征
        if 'semantic' in features and 'semantic_ids' in features:
            original_semantic_ids = features['semantic_ids']
            indices = [original_semantic_ids.index(assembly_id) for assembly_id in aligned_assembly_ids]
            aligned_features['semantic'] = features['semantic'][indices]
        
        logger.info(f"特征对齐完成，最终装配体数: {len(aligned_assembly_ids)}")
        return aligned_features
    
    def extract_all_features(
        self,
        assembly_ids: Optional[List[str]] = None,
        include_shape_features: bool = True,
        include_semantic_features: bool = True,
        process_features: bool = True,
        enable_fusion: bool = False
    ) -> Dict[str, Union[np.ndarray, List[str]]]:
        """
        提取所有类型的装配体特征
        
        Args:
            assembly_ids: 指定的装配体ID列表
            include_shape_features: 是否包含形状特征
            include_semantic_features: 是否包含语义特征
            process_features: 是否进行特征处理（对齐、PCA、归一化）
            enable_fusion: 是否启用特征融合
            
        Returns:
            包含所有特征的字典
        """
        logger.info("开始提取装配体特征...")
        result = {}
        
        # 提取结构化特征
        df = self.extract_structural_features(assembly_ids)
        
        if df.empty:
            logger.warning("未找到结构化特征数据")
            return result
        
        # 如果没有训练过标准化器，则训练
        if not self.fitted:
            self.fit_scalers(df)
        
        # 转换结构化特征
        structural_features = self.transform_features(df)
        result['structural'] = structural_features
        result['assembly_ids'] = df['assembly_id'].tolist()
        
        # 提取形状特征
        if include_shape_features:
            try:
                shape_matrix, _, shape_ids, _ = self.extract_vector_features_batch(result['assembly_ids'])
                if shape_matrix is not None:
                    result['shape'] = shape_matrix
                    result['shape_ids'] = shape_ids
            except Exception as e:
                logger.error(f"提取形状特征失败: {e}")
        
        # 提取语义特征
        if include_semantic_features:
            try:
                _, semantic_matrix, _, semantic_ids = self.extract_vector_features_batch(result['assembly_ids'])
                if semantic_matrix is not None:
                    result['semantic'] = semantic_matrix
                    result['semantic_ids'] = semantic_ids
            except Exception as e:
                logger.error(f"提取语义特征失败: {e}")
        
        # 进行特征处理
        if process_features:
            logger.info("开始特征处理...")
            
            # 1. 对齐特征
            result = self.align_features(result)
            
            # 2. 训练PCA模型
            if not self.pca_fitted:
                self.fit_pca_models(result)
            
            # 3. PCA降维和L2归一化
            processed_features = self.transform_features_with_pca(result)
            
            # 4. 加权拼接（可选）
            if enable_fusion:
                fused_features = self.fuse_features(processed_features)
                if fused_features.size > 0:
                    result['fused_features'] = fused_features
            
            # 更新结果
            result.update(processed_features)
        
        logger.info(f"特征提取完成，包含: {list(result.keys())}")
        return result
    
    def get_assembly_features_by_ids(self, assembly_ids: List[str]) -> Dict[str, Union[np.ndarray, List[str]]]:
        """
        根据装配体ID获取特征
        
        Args:
            assembly_ids: 装配体ID列表
            
        Returns:
            特征字典
        """
        return self.extract_all_features(
            assembly_ids=assembly_ids,
            include_shape_features=True,
            include_semantic_features=True,
            process_features=True
        )
    
    def save_features(self, features: Dict[str, Union[np.ndarray, List[str]]], filepath: str):
        """保存特征到文件"""
        try:
            with open(filepath, 'wb') as f:
                pickle.dump(features, f)
            logger.info(f"特征已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存特征失败: {e}")
            raise
    
    def load_features(self, filepath: str) -> Dict[str, Union[np.ndarray, List[str]]]:
        """从文件加载特征"""
        try:
            with open(filepath, 'rb') as f:
                features = pickle.load(f)
            logger.info(f"特征已从 {filepath} 加载")
            return features
        except Exception as e:
            logger.error(f"加载特征失败: {e}")
            raise
    
    def save_scalers(self, filepath: str):
        """保存标准化器和PCA模型"""
        try:
            models = {
                'scaler': self.scaler,
                'pca_shape': self.pca_shape,
                'pca_semantic': self.pca_semantic,
                'fitted': self.fitted,
                'pca_fitted': self.pca_fitted
            }
            with open(filepath, 'wb') as f:
                pickle.dump(models, f)
            logger.info(f"标准化器和PCA模型已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存标准化器和PCA模型失败: {e}")
            raise
    
    def load_scalers(self, filepath: str):
        """加载标准化器和PCA模型"""
        try:
            with open(filepath, 'rb') as f:
                models = pickle.load(f)
            
            self.scaler = models.get('scaler')
            self.pca_shape = models.get('pca_shape')
            self.pca_semantic = models.get('pca_semantic')
            self.fitted = models.get('fitted', False)
            self.pca_fitted = models.get('pca_fitted', False)
            
            logger.info(f"标准化器和PCA模型已从 {filepath} 加载")
        except Exception as e:
            logger.error(f"加载标准化器和PCA模型失败: {e}")
            raise
    
    @classmethod
    def get_default_config(cls):
        """获取默认配置"""
        return {
            'extraction': cls.FEATURE_EXTRACTION_CONFIG,
            'columns': cls.FEATURE_COLUMNS,
            'paths': cls.DEFAULT_PATHS,
        }
    
    def get_feature_dimensions(self):
        """获取特征维度信息"""
        pca_dim = self.FEATURE_EXTRACTION_CONFIG['pca_dim']
        structural_dim = self.FEATURE_EXTRACTION_CONFIG['structural_feature_dim']
        
        return {
            'structural': structural_dim,
            'shape': pca_dim,
            'semantic': pca_dim,
            'fused': pca_dim + pca_dim + structural_dim,  # 融合特征维度
        }
    
    def get_batch_config(self):
        """获取批处理配置"""
        return {
            'batch_size': self.FEATURE_EXTRACTION_CONFIG['batch_size'],
            'max_assemblies_per_batch': self.FEATURE_EXTRACTION_CONFIG['max_assemblies_per_batch'],
        }
