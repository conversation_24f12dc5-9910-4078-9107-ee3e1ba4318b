#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
零件特征提取器 - 提取零件的结构化信息、形状向量和语义向量

功能：
1. 从PostgreSQL数据库中提取零件的结构化信息
2. 从Milvus向量数据库中提取形状向量和语义向量
3. 对数值特征进行Z-score标准化处理
4. 组合多模态特征用于相似度计算

结构化信息包括：
- 几何特征：长、宽、高、表面积、体积（5维）
- 物理特征：密度、质量（2维）
- 孔特征：孔数量、直径均值、直径标准差、深度均值、深度标准差（5维）
总计：12维数值向量

鉴于数值向量中各维度物理量纲与数值范围的巨大差异，直接应用可能导致数值较大维度的特征不成比例地主导相似度计算。为消除此偏差，需要对数据集中所有资产的数值向量均进行了Z-score标准化处理。该处理使得每个特征维度都近似服从均值为0、标准差为1的标准正态分布，从而实现了跨维度信息的公平度量。

向量信息：
- 形状向量：从Milvus中的cad_collection集合的shape_vector字段提取（768维）
- 语义向量：从Milvus中的cad_collection集合的dense_vector字段提取（1024维）
"""

from __future__ import annotations

import logging
import pickle
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import psycopg2
from psycopg2.extras import RealDictCursor
from sklearn.preprocessing import StandardScaler
from sklearn.compose import ColumnTransformer
from sklearn.decomposition import PCA
from sklearn.preprocessing import normalize
from pymilvus import Collection, connections

from src.config import Config
from src.utils.database.milvus_utils import MilvusManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PartFeatureExtractor:
    """零件特征提取器"""
    
    # 特征提取配置常量
    FEATURE_EXTRACTION_CONFIG = {
        'milvus_collection_name': 'cad_collection',
        'structural_feature_dim': 12,    # 结构化特征维度（修正为12维）
        'shape_feature_dim': 768,        # 形状特征维度
        'semantic_feature_dim': 1024,    # 语义特征维度
        'pca_dim': 64,                   # PCA降维目标维度
        'batch_size': 1000,              # 批处理大小
        'max_parts_per_batch': 100,      # 每批最大零件数
        'continue_on_error': True,       # 遇到错误是否继续
        'max_retries': 3,                # 最大重试次数
        'retry_delay': 1,                # 重试延迟（秒）
        'feature_weights': {             # 特征权重
            'shape': 0.3,
            'semantic': 0.2,
            'structural': 0.5
        }
    }
    
    # 特征列名映射
    FEATURE_COLUMNS = {
        'numerical_features': [
            'length', 'width', 'height', 'area', 'volume',
            'density', 'mass',
            'hole_count', 'hole_diameter_mean', 'hole_diameter_std',
            'hole_depth_mean', 'hole_depth_std'
        ],
        'milvus_vector_fields': {
            'shape_vector': 'shape_vector',
            'semantic_vector': 'dense_vector',
        }
    }
    
    # 默认文件路径
    DEFAULT_PATHS = {
        'features_output': 'dataset/part_features.pkl',
        'scalers_output': 'dataset/feature_scalers.pkl',
        'pca_models_output': 'dataset/pca_models.pkl',
    }
    
    def __init__(self, 
                 db_config: Optional[Dict[str, str]] = None,
                 milvus_collection_name: str = None):
        """
        初始化特征提取器
        
        Args:
            db_config: PostgreSQL数据库配置信息，如果为None则使用Config中的配置
            milvus_collection_name: Milvus集合名称，如果为None则使用默认配置
        """
        self.db_config = db_config or Config.POSTGRES_CONFIG
        self.milvus_collection_name = (milvus_collection_name or 
                                     self.FEATURE_EXTRACTION_CONFIG['milvus_collection_name'])
        self.scaler = StandardScaler()
        self.fitted = False
        
        # 初始化PCA模型
        self.shape_pca = PCA(n_components=self.FEATURE_EXTRACTION_CONFIG['pca_dim'])
        self.semantic_pca = PCA(n_components=self.FEATURE_EXTRACTION_CONFIG['pca_dim'])
        self.pca_fitted = False
        
        # 初始化Milvus连接
        self.milvus_manager = MilvusManager(use_reranker=False)
        
        # 定义特征列名（与数据库表结构对应）
        self.numerical_features = self.FEATURE_COLUMNS['numerical_features']
        
    def connect_db(self) -> psycopg2.connection:
        """连接数据库"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def extract_structural_features(self, part_ids: Optional[List[str]] = None) -> pd.DataFrame:
        """
        从数据库提取结构化特征
        
        Args:
            part_ids: 指定提取的零件ID列表，如果为None则提取所有零件
            
        Returns:
            包含结构化特征的DataFrame
        """
        conn = self.connect_db()
        
        try:
            # 构建SQL查询
            base_query = """
            SELECT 
                uuid as part_id,
                length,
                width,
                height,
                area,
                volume,
                density,
                mass,
                hole_count,
                hole_diameter_mean,
                hole_diameter_std,
                hole_depth_mean,
                hole_depth_std
            FROM cad_rag.parts
            """
            
            if part_ids:
                placeholders = ','.join(['%s'] * len(part_ids))
                query = f"{base_query} WHERE uuid IN ({placeholders})"
                params = part_ids
            else:
                query = base_query
                params = None
            
            # 执行查询
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                results = cursor.fetchall()
            
            # 转换为DataFrame
            df = pd.DataFrame(results)
            
            if df.empty:
                logger.warning("未找到任何零件数据")
                return df
            
            # 处理空值
            df = self._handle_missing_values(df)
            
            logger.info(f"成功提取 {len(df)} 个零件的结构化特征")
            return df
            
        except Exception as e:
            logger.error(f"提取结构化特征失败: {e}")
            raise
        finally:
            conn.close()
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        # 数值特征的缺失值用0填充
        for col in self.numerical_features:
            if col in df.columns:
                df[col] = df[col].fillna(0)
        
        return df
    
    def fit_scalers(self, df: pd.DataFrame) -> None:
        """训练标准化器"""
        if df.empty:
            logger.warning("DataFrame为空，无法训练标准化器")
            return
        
        # 训练数值特征的标准化器
        numerical_data = df[self.numerical_features]
        self.scaler.fit(numerical_data)
        
        self.fitted = True
        logger.info("标准化器训练完成")
    
    def transform_features(self, df: pd.DataFrame) -> np.ndarray:
        """
        转换特征：标准化数值特征
        
        Args:
            df: 包含原始特征的DataFrame
            
        Returns:
            标准化后的特征向量数组
        """
        if not self.fitted:
            raise ValueError("请先调用fit_scalers()训练标准化器")
        
        if df.empty:
            return np.array([])
        
        # 标准化数值特征
        numerical_data = df[self.numerical_features]
        scaled_numerical = self.scaler.transform(numerical_data)
        
        logger.info(f"特征转换完成，输出维度: {scaled_numerical.shape}")
        return scaled_numerical
    
    def extract_shape_features(self, part_ids: List[str]) -> Tuple[np.ndarray, List[str]]:
        """
        从Milvus中提取形状特征（shape_vector字段）
        
        Args:
            part_ids: 零件ID列表
            
        Returns:
            Tuple[np.ndarray, List[str]]: 形状特征向量数组和成功提取的零件ID列表
        """
        try:
            self.milvus_manager.connect()
            collection = Collection(name=self.milvus_collection_name)
            collection.load()
            
            shape_features = []
            successful_ids = []
            missing_parts = []
            
            for part_id in part_ids:
                try:
                    # 查询指定零件的形状向量
                    expr = f'pk == "{part_id}"'
                    query_result = collection.query(
                        expr=expr, 
                        output_fields=["shape_vector"],
                        limit=1
                    )
                    
                    if query_result:
                        shape_vector = query_result[0]["shape_vector"]
                        if shape_vector is not None:
                            shape_features.append(np.array(shape_vector, dtype=np.float32))
                            successful_ids.append(part_id)
                        else:
                            missing_parts.append(part_id)
                    else:
                        missing_parts.append(part_id)
                        
                except Exception as e:
                    logger.warning(f"提取零件 {part_id} 的形状特征时出错: {e}")
                    missing_parts.append(part_id)
            
            if missing_parts:
                logger.warning(f"缺失形状特征的零件 ({len(missing_parts)}个): {missing_parts[:10]}...")
            
            if shape_features:
                result = np.vstack(shape_features)
                logger.info(f"成功从Milvus提取 {len(shape_features)} 个零件的形状特征，维度: {result.shape}")
                return result, successful_ids
            else:
                logger.warning("未能提取到任何形状特征")
                return np.array([]), []
                
        except Exception as e:
            logger.error(f"从Milvus提取形状特征失败: {e}")
            raise
    
    def extract_semantic_features(self, part_ids: List[str]) -> Tuple[np.ndarray, List[str]]:
        """
        从Milvus中提取语义特征（dense_vector字段）
        
        Args:
            part_ids: 零件ID列表
            
        Returns:
            Tuple[np.ndarray, List[str]]: 语义特征向量数组和成功提取的零件ID列表
        """
        try:
            self.milvus_manager.connect()
            collection = Collection(name=self.milvus_collection_name)
            collection.load()
            
            semantic_features = []
            successful_ids = []
            missing_parts = []
            
            for part_id in part_ids:
                try:
                    # 查询指定零件的语义向量
                    expr = f'pk == "{part_id}"'
                    query_result = collection.query(
                        expr=expr, 
                        output_fields=["dense_vector"],
                        limit=1
                    )
                    
                    if query_result:
                        dense_vector = query_result[0]["dense_vector"]
                        if dense_vector is not None:
                            semantic_features.append(np.array(dense_vector, dtype=np.float32))
                            successful_ids.append(part_id)
                        else:
                            missing_parts.append(part_id)
                    else:
                        missing_parts.append(part_id)
                        
                except Exception as e:
                    logger.warning(f"提取零件 {part_id} 的语义特征时出错: {e}")
                    missing_parts.append(part_id)
            
            if missing_parts:
                logger.warning(f"缺失语义特征的零件 ({len(missing_parts)}个): {missing_parts[:10]}...")
            
            if semantic_features:
                result = np.vstack(semantic_features)
                logger.info(f"成功从Milvus提取 {len(semantic_features)} 个零件的语义特征，维度: {result.shape}")
                return result, successful_ids
            else:
                logger.warning("未能提取到任何语义特征")
                return np.array([]), []
                
        except Exception as e:
            logger.error(f"从Milvus提取语义特征失败: {e}")
            raise
    
    def extract_all_features(
        self,
        part_ids: Optional[List[str]] = None,
        include_shape_features: bool = True,
        include_semantic_features: bool = True,
        process_features: bool = True,
        enable_fusion: bool = False
    ) -> Dict[str, Union[np.ndarray, List[str]]]:
        """
        提取所有特征
        
        Args:
            part_ids: 零件ID列表，如果为None则提取所有零件
            include_shape_features: 是否包含形状特征
            include_semantic_features: 是否包含语义特征
            process_features: 是否进行特征处理（对齐、PCA降维、L2归一化、加权拼接）
            enable_fusion: 是否启用加权拼接融合特征
            
        Returns:
            包含所有特征的字典
        """
        result = {}
        
        # 提取结构化特征
        df = self.extract_structural_features(part_ids)
        
        if df.empty:
            logger.warning("未找到结构化特征数据")
            return result
        
        # 如果没有训练过标准化器，则训练
        if not self.fitted:
            self.fit_scalers(df)
        
        # 转换结构化特征
        structural_features = self.transform_features(df)
        result['structural'] = structural_features
        result['part_ids'] = df['part_id'].tolist()
        
        # 提取形状特征
        if include_shape_features:
            try:
                shape_features, shape_ids = self.extract_shape_features(result['part_ids'])
                if shape_features.size > 0:
                    result['shape'] = shape_features
                    result['shape_ids'] = shape_ids
            except Exception as e:
                logger.error(f"提取形状特征失败: {e}")
        
        # 提取语义特征
        if include_semantic_features:
            try:
                semantic_features, semantic_ids = self.extract_semantic_features(result['part_ids'])
                if semantic_features.size > 0:
                    result['semantic'] = semantic_features
                    result['semantic_ids'] = semantic_ids
            except Exception as e:
                logger.error(f"提取语义特征失败: {e}")
        
        # 进行特征处理
        if process_features:
            logger.info("开始特征处理...")
            
            # 1. 对齐特征
            result = self.align_features(result)
            
            # 2. 训练PCA模型
            if not self.pca_fitted:
                self.fit_pca_models(result)
            
            # 3. PCA降维和L2归一化
            processed_features = self.transform_features_with_pca(result)
            
            # 4. 加权拼接（可选）
            if enable_fusion:
                fused_features = self.fuse_features(processed_features)
                if fused_features.size > 0:
                    result['fused_features'] = fused_features
            
            # 更新结果
            result.update(processed_features)
        
        logger.info(f"特征提取完成，包含: {list(result.keys())}")
        return result
    
    def save_features(self, features: Dict[str, Union[np.ndarray, List[str]]], output_path: str) -> None:
        """保存特征到文件"""
        try:
            with open(output_path, 'wb') as f:
                pickle.dump(features, f)
            logger.info(f"特征已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存特征失败: {e}")
            raise
    
    def load_features(self, input_path: str) -> Dict[str, Union[np.ndarray, List[str]]]:
        """从文件加载特征"""
        try:
            with open(input_path, 'rb') as f:
                features = pickle.load(f)
            logger.info(f"特征已从 {input_path} 加载")
            return features
        except Exception as e:
            logger.error(f"加载特征失败: {e}")
            raise
    
    def save_scalers(self, output_path: str) -> None:
        """保存标准化器"""
        try:
            scalers = {
                'scaler': self.scaler,
                'fitted': self.fitted,
                'shape_pca': self.shape_pca,
                'semantic_pca': self.semantic_pca,
                'pca_fitted': self.pca_fitted
            }
            with open(output_path, 'wb') as f:
                pickle.dump(scalers, f)
            logger.info(f"标准化器和PCA模型已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存标准化器失败: {e}")
            raise
    
    def load_scalers(self, input_path: str) -> None:
        """加载标准化器"""
        try:
            with open(input_path, 'rb') as f:
                scalers = pickle.load(f)
            self.scaler = scalers['scaler']
            self.fitted = scalers['fitted']
            # 兼容旧版本文件
            if 'shape_pca' in scalers:
                self.shape_pca = scalers['shape_pca']
                self.semantic_pca = scalers['semantic_pca']
                self.pca_fitted = scalers['pca_fitted']
            logger.info(f"标准化器和PCA模型已从 {input_path} 加载")
        except Exception as e:
            logger.error(f"加载标准化器失败: {e}")
            raise
    
    def get_part_features_by_ids(self, part_ids: List[str]) -> Dict[str, Union[np.ndarray, List[str]]]:
        """
        根据零件ID获取完整的特征向量
        
        Args:
            part_ids: 零件ID列表
            
        Returns:
            包含所有特征的字典，包含对应的ID信息
        """
        return self.extract_all_features(
            part_ids=part_ids,
            include_shape_features=True,
            include_semantic_features=True
        )
    
    def get_part_vector_from_milvus(self, part_id: str) -> Dict[str, Optional[np.ndarray]]:
        """
        从Milvus中获取单个零件的向量特征
        
        Args:
            part_id: 零件ID
            
        Returns:
            包含shape_vector和dense_vector的字典
        """
        try:
            self.milvus_manager.connect()
            collection = Collection(name=self.milvus_collection_name)
            collection.load()
            
            # 查询指定零件的向量特征
            expr = f'pk == "{part_id}"'
            query_result = collection.query(
                expr=expr, 
                output_fields=["shape_vector", "dense_vector"],
                limit=1
            )
            
            if query_result:
                record = query_result[0]
                return {
                    'shape_vector': np.array(record["shape_vector"], dtype=np.float32) if record["shape_vector"] else None,
                    'dense_vector': np.array(record["dense_vector"], dtype=np.float32) if record["dense_vector"] else None
                }
            else:
                logger.warning(f"未找到零件 {part_id} 的向量特征")
                return {'shape_vector': None, 'dense_vector': None}
                
        except Exception as e:
            logger.error(f"从Milvus获取零件 {part_id} 向量特征失败: {e}")
            raise

    @classmethod
    def get_default_config(cls):
        """获取默认配置"""
        return {
            'extraction': cls.FEATURE_EXTRACTION_CONFIG,
            'columns': cls.FEATURE_COLUMNS,
            'paths': cls.DEFAULT_PATHS,
        }
    
    def get_feature_dimensions(self):
        """获取特征维度信息"""
        pca_dim = self.FEATURE_EXTRACTION_CONFIG['pca_dim']
        structural_dim = self.FEATURE_EXTRACTION_CONFIG['structural_feature_dim']
        
        return {
            'structural': structural_dim,
            'shape': pca_dim,
            'semantic': pca_dim,
            'fused': pca_dim + pca_dim + structural_dim,  # 融合特征维度
        }
    
    def get_batch_config(self):
        """获取批处理配置"""
        return {
            'batch_size': self.FEATURE_EXTRACTION_CONFIG['batch_size'],
            'max_parts_per_batch': self.FEATURE_EXTRACTION_CONFIG['max_parts_per_batch'],
        }
    
    def align_features(self, features: Dict[str, Union[np.ndarray, List[str]]]) -> Dict[str, Union[np.ndarray, List[str]]]:
        """
        对齐特征，去除缺失特征的零件
        
        Args:
            features: 包含所有特征的字典
            
        Returns:
            对齐后的特征字典
        """
        if 'part_ids' not in features:
            logger.warning("缺少part_ids，无法对齐特征")
            return features
            
        # 获取所有特征的ID集合
        part_ids_set = set(features['part_ids'])
        
        # 如果有形状特征，找到交集
        if 'shape_ids' in features:
            shape_ids_set = set(features['shape_ids'])
            part_ids_set = part_ids_set.intersection(shape_ids_set)
            
        # 如果有语义特征，找到交集
        if 'semantic_ids' in features:
            semantic_ids_set = set(features['semantic_ids'])
            part_ids_set = part_ids_set.intersection(semantic_ids_set)
        
        # 转换为有序列表
        aligned_part_ids = sorted(list(part_ids_set))
        
        logger.info(f"对齐前零件数: {len(features['part_ids'])}")
        logger.info(f"对齐后零件数: {len(aligned_part_ids)}")
        
        # 创建索引映射
        aligned_features = {'part_ids': aligned_part_ids}
        
        # 对齐结构化特征
        if 'structural' in features:
            original_part_ids = features['part_ids']
            indices = [original_part_ids.index(part_id) for part_id in aligned_part_ids]
            aligned_features['structural'] = features['structural'][indices]
            
        # 对齐形状特征
        if 'shape' in features and 'shape_ids' in features:
            original_shape_ids = features['shape_ids']
            indices = [original_shape_ids.index(part_id) for part_id in aligned_part_ids]
            aligned_features['shape'] = features['shape'][indices]
            
        # 对齐语义特征
        if 'semantic' in features and 'semantic_ids' in features:
            original_semantic_ids = features['semantic_ids']
            indices = [original_semantic_ids.index(part_id) for part_id in aligned_part_ids]
            aligned_features['semantic'] = features['semantic'][indices]
            
        logger.info(f"特征对齐完成，最终零件数: {len(aligned_part_ids)}")
        return aligned_features
    
    def fit_pca_models(self, features: Dict[str, Union[np.ndarray, List[str]]]) -> None:
        """
        训练PCA模型
        
        Args:
            features: 包含形状和语义特征的字典
        """
        pca_dim = self.FEATURE_EXTRACTION_CONFIG['pca_dim']
        
        # 训练形状特征PCA
        if 'shape' in features:
            shape_features = features['shape']
            if shape_features.shape[1] > pca_dim:
                logger.info(f"训练形状特征PCA模型，从{shape_features.shape[1]}维降至{pca_dim}维")
                self.shape_pca.fit(shape_features)
            else:
                logger.info(f"形状特征维度({shape_features.shape[1]})小于等于目标维度({pca_dim})，跳过PCA")
                self.shape_pca = None
        
        # 训练语义特征PCA
        if 'semantic' in features:
            semantic_features = features['semantic']
            if semantic_features.shape[1] > pca_dim:
                logger.info(f"训练语义特征PCA模型，从{semantic_features.shape[1]}维降至{pca_dim}维")
                self.semantic_pca.fit(semantic_features)
            else:
                logger.info(f"语义特征维度({semantic_features.shape[1]})小于等于目标维度({pca_dim})，跳过PCA")
                self.semantic_pca = None
        
        self.pca_fitted = True
        logger.info("PCA模型训练完成")
    
    def transform_features_with_pca(self, features: Dict[str, Union[np.ndarray, List[str]]]) -> Dict[str, np.ndarray]:
        """
        使用PCA降维和L2归一化处理特征
        
        Args:
            features: 包含原始特征的字典
            
        Returns:
            处理后的特征字典
        """
        if not self.pca_fitted:
            raise ValueError("请先调用fit_pca_models()训练PCA模型")
            
        processed_features = {}
        
        # 处理结构化特征（已经标准化）
        if 'structural' in features:
            structural_features = features['structural']
            # L2归一化
            structural_normalized = normalize(structural_features, norm='l2')
            processed_features['structural'] = structural_normalized
            logger.info(f"结构化特征L2归一化完成，维度: {structural_normalized.shape}")
        
        # 处理形状特征
        if 'shape' in features:
            shape_features = features['shape']
            
            # PCA降维
            if self.shape_pca is not None:
                shape_reduced = self.shape_pca.transform(shape_features)
                logger.info(f"形状特征PCA降维完成，维度: {shape_reduced.shape}")
            else:
                shape_reduced = shape_features
                logger.info(f"形状特征跳过PCA，维度: {shape_reduced.shape}")
            
            # L2归一化
            shape_normalized = normalize(shape_reduced, norm='l2')
            processed_features['shape'] = shape_normalized
            logger.info(f"形状特征L2归一化完成，维度: {shape_normalized.shape}")
        
        # 处理语义特征
        if 'semantic' in features:
            semantic_features = features['semantic']
            
            # PCA降维
            if self.semantic_pca is not None:
                semantic_reduced = self.semantic_pca.transform(semantic_features)
                logger.info(f"语义特征PCA降维完成，维度: {semantic_reduced.shape}")
            else:
                semantic_reduced = semantic_features
                logger.info(f"语义特征跳过PCA，维度: {semantic_reduced.shape}")
            
            # L2归一化
            semantic_normalized = normalize(semantic_reduced, norm='l2')
            processed_features['semantic'] = semantic_normalized
            logger.info(f"语义特征L2归一化完成，维度: {semantic_normalized.shape}")
        
        return processed_features
    
    def fuse_features(self, processed_features: Dict[str, np.ndarray]) -> np.ndarray:
        """
        通过加权拼接融合特征
        
        Args:
            processed_features: 处理后的特征字典
            
        Returns:
            融合后的特征向量
        """
        weights = self.FEATURE_EXTRACTION_CONFIG['feature_weights']
        feature_vectors = []
        
        # 按照权重顺序拼接特征
        if 'shape' in processed_features:
            shape_weighted = processed_features['shape'] * weights['shape']
            feature_vectors.append(shape_weighted)
            logger.info(f"形状特征加权 (权重: {weights['shape']})，维度: {shape_weighted.shape}")
        
        if 'semantic' in processed_features:
            semantic_weighted = processed_features['semantic'] * weights['semantic']
            feature_vectors.append(semantic_weighted)
            logger.info(f"语义特征加权 (权重: {weights['semantic']})，维度: {semantic_weighted.shape}")
        
        if 'structural' in processed_features:
            structural_weighted = processed_features['structural'] * weights['structural']
            feature_vectors.append(structural_weighted)
            logger.info(f"结构化特征加权 (权重: {weights['structural']})，维度: {structural_weighted.shape}")
        
        # 拼接所有特征
        if feature_vectors:
            fused_features = np.concatenate(feature_vectors, axis=1)
            logger.info(f"特征融合完成，最终维度: {fused_features.shape}")
            return fused_features
        else:
            logger.warning("没有可融合的特征")
            return np.array([])
    
    def save_pca_models(self, output_path: str) -> None:
        """保存PCA模型"""
        try:
            pca_models = {
                'shape_pca': self.shape_pca,
                'semantic_pca': self.semantic_pca,
                'pca_fitted': self.pca_fitted
            }
            with open(output_path, 'wb') as f:
                pickle.dump(pca_models, f)
            logger.info(f"PCA模型已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存PCA模型失败: {e}")
            raise
    
    def load_pca_models(self, input_path: str) -> None:
        """加载PCA模型"""
        try:
            with open(input_path, 'rb') as f:
                pca_models = pickle.load(f)
            self.shape_pca = pca_models['shape_pca']
            self.semantic_pca = pca_models['semantic_pca']
            self.pca_fitted = pca_models['pca_fitted']
            logger.info(f"PCA模型已从 {input_path} 加载")
        except Exception as e:
            logger.error(f"加载PCA模型失败: {e}")
            raise

def main():
    """主函数示例"""
    # 创建特征提取器，使用默认配置
    extractor = PartFeatureExtractor()
    
    # 提取所有特征并进行处理
    features = extractor.extract_all_features(
        include_shape_features=True,
        include_semantic_features=True,
        process_features=True
    )
    
    # 保存特征
    extractor.save_features(features, 'dataset/part_features.pkl')
    
    # 保存标准化器和PCA模型
    extractor.save_scalers('dataset/feature_scalers.pkl')
    
    # 打印特征信息
    print("=== 特征提取结果 ===")
    for feature_type, data in features.items():
        if isinstance(data, np.ndarray):
            print(f"{feature_type}: {data.shape}")
        elif isinstance(data, list):
            print(f"{feature_type}: {len(data)} items")
    
    # 打印特征维度信息
    print("\n=== 特征维度信息 ===")
    dimensions = extractor.get_feature_dimensions()
    for feature_type, dim in dimensions.items():
        print(f"{feature_type}: {dim}维")
    
    # 打印权重信息
    print("\n=== 特征权重信息 ===")
    weights = extractor.FEATURE_EXTRACTION_CONFIG['feature_weights']
    for feature_type, weight in weights.items():
        print(f"{feature_type}: {weight}")
    
    # 示例：获取特定零件的特征
    if 'part_ids' in features and features['part_ids']:
        sample_part_id = features['part_ids'][0]
        print(f"\n=== 获取单个零件特征示例 (ID: {sample_part_id}) ===")
        
        # 从Milvus获取向量特征
        vector_features = extractor.get_part_vector_from_milvus(sample_part_id)
        for vector_type, vector_data in vector_features.items():
            if vector_data is not None:
                print(f"{vector_type}: {vector_data.shape}")
            else:
                print(f"{vector_type}: None")
    
    print("\n特征提取完成！")
    print("包含以下处理步骤:")
    print("1. 结构化特征提取和Z-score标准化")
    print("2. 形状和语义特征提取")
    print("3. 特征对齐（去除缺失特征的零件）")
    print("4. PCA降维（形状和语义特征降至64维）")
    print("5. L2归一化（所有特征类型）")
    print("6. 加权拼接（形状:0.3, 语义:0.3, 结构化:0.4）")


if __name__ == "__main__":
    main()
