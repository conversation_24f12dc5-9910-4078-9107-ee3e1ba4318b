import os
import sys
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models.LLM import MultiProviderLLM

# 1. OpenAI
# openai_llm = MultiProviderLLM(
#     provider="openai",
#     api_url="https://api.openai.com",
#     api_key="sk-...",
#     model="gpt-4o-preview",
# )
# reply = openai_llm.chat([{"role": "user", "content": "用一句话介绍量子计算"}])
# print(reply)

# 2. Azure OpenAI
# azure_llm = MultiProviderLLM(
#     provider="azure",
#     api_url="https://my-res.openai.azure.com",
#     api_key="az-...",
#     deployment="gpt4",
# )
# print(azure_llm.chat([{"role": "user", "content": "Hi!"}]))

# 3. Gemini + 图片
llm = MultiProviderLLM(
    provider="openai",
    api_url="http://**************:4000/",
    api_key="sk-YyiBg6DSn1Fc2KBNU6ZYtw",
    model="DeepSeek-V3-0324",
)


print(
    llm.chat(
        messages=[{"role": "user", "content": "你是哪个模型"}]
    )
)
