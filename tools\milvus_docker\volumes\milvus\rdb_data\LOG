2025/07/07-06:10:53.995739 42 RocksDB version: 6.29.5
2025/07/07-06:10:53.996195 42 Git sha 0
2025/07/07-06:10:53.996196 42 Compile date 2024-11-15 11:22:58
2025/07/07-06:10:53.996197 42 DB SUMMARY
2025/07/07-06:10:53.996197 42 DB Session ID:  4FUN369NNPH8ZW9K11KB
2025/07/07-06:10:54.025988 42 CURRENT file:  CURRENT
2025/07/07-06:10:54.025996 42 IDENTITY file:  IDENTITY
2025/07/07-06:10:54.026545 42 MANIFEST file:  MANIFEST-000068 size: 3003 Bytes
2025/07/07-06:10:54.026547 42 SST files in /var/lib/milvus/rdb_data dir, Total Num: 9, files: 000056.sst 000057.sst 000072.sst 000076.sst 000077.sst 000078.sst 000079.sst 000080.sst 000081.sst 
2025/07/07-06:10:54.026549 42 Write Ahead Log file in /var/lib/milvus/rdb_data: 000069.log size: 45014388 ; 
2025/07/07-06:10:54.026550 42                         Options.error_if_exists: 0
2025/07/07-06:10:54.026551 42                       Options.create_if_missing: 1
2025/07/07-06:10:54.026552 42                         Options.paranoid_checks: 1
2025/07/07-06:10:54.026552 42             Options.flush_verify_memtable_count: 1
2025/07/07-06:10:54.026553 42                               Options.track_and_verify_wals_in_manifest: 0
2025/07/07-06:10:54.026553 42                                     Options.env: 0x7fe38a56cd00
2025/07/07-06:10:54.026554 42                                      Options.fs: PosixFileSystem
2025/07/07-06:10:54.026555 42                                Options.info_log: 0x7fe28ba90140
2025/07/07-06:10:54.026555 42                Options.max_file_opening_threads: 16
2025/07/07-06:10:54.026556 42                              Options.statistics: (nil)
2025/07/07-06:10:54.026556 42                               Options.use_fsync: 0
2025/07/07-06:10:54.026557 42                       Options.max_log_file_size: 0
2025/07/07-06:10:54.026557 42                  Options.max_manifest_file_size: 1073741824
2025/07/07-06:10:54.026558 42                   Options.log_file_time_to_roll: 0
2025/07/07-06:10:54.026559 42                       Options.keep_log_file_num: 1000
2025/07/07-06:10:54.026559 42                    Options.recycle_log_file_num: 0
2025/07/07-06:10:54.026560 42                         Options.allow_fallocate: 1
2025/07/07-06:10:54.026560 42                        Options.allow_mmap_reads: 0
2025/07/07-06:10:54.026561 42                       Options.allow_mmap_writes: 0
2025/07/07-06:10:54.026561 42                        Options.use_direct_reads: 0
2025/07/07-06:10:54.026561 42                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/07-06:10:54.026562 42          Options.create_missing_column_families: 1
2025/07/07-06:10:54.026562 42                              Options.db_log_dir: 
2025/07/07-06:10:54.026563 42                                 Options.wal_dir: 
2025/07/07-06:10:54.026563 42                Options.table_cache_numshardbits: 6
2025/07/07-06:10:54.026564 42                         Options.WAL_ttl_seconds: 0
2025/07/07-06:10:54.026564 42                       Options.WAL_size_limit_MB: 0
2025/07/07-06:10:54.026565 42                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/07-06:10:54.026565 42             Options.manifest_preallocation_size: 4194304
2025/07/07-06:10:54.026566 42                     Options.is_fd_close_on_exec: 1
2025/07/07-06:10:54.026566 42                   Options.advise_random_on_open: 1
2025/07/07-06:10:54.026567 42                   Options.experimental_mempurge_threshold: 0.000000
2025/07/07-06:10:54.026570 42                    Options.db_write_buffer_size: 0
2025/07/07-06:10:54.026570 42                    Options.write_buffer_manager: 0x7fe28e440280
2025/07/07-06:10:54.026571 42         Options.access_hint_on_compaction_start: 1
2025/07/07-06:10:54.026571 42  Options.new_table_reader_for_compaction_inputs: 0
2025/07/07-06:10:54.026572 42           Options.random_access_max_buffer_size: 1048576
2025/07/07-06:10:54.026572 42                      Options.use_adaptive_mutex: 0
2025/07/07-06:10:54.026573 42                            Options.rate_limiter: (nil)
2025/07/07-06:10:54.026574 42     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/07-06:10:54.027048 42                       Options.wal_recovery_mode: 2
2025/07/07-06:10:54.027049 42                  Options.enable_thread_tracking: 0
2025/07/07-06:10:54.027050 42                  Options.enable_pipelined_write: 0
2025/07/07-06:10:54.027051 42                  Options.unordered_write: 0
2025/07/07-06:10:54.027052 42         Options.allow_concurrent_memtable_write: 1
2025/07/07-06:10:54.027052 42      Options.enable_write_thread_adaptive_yield: 1
2025/07/07-06:10:54.027053 42             Options.write_thread_max_yield_usec: 100
2025/07/07-06:10:54.027053 42            Options.write_thread_slow_yield_usec: 3
2025/07/07-06:10:54.027054 42                               Options.row_cache: None
2025/07/07-06:10:54.027054 42                              Options.wal_filter: None
2025/07/07-06:10:54.027055 42             Options.avoid_flush_during_recovery: 0
2025/07/07-06:10:54.027055 42             Options.allow_ingest_behind: 0
2025/07/07-06:10:54.027056 42             Options.preserve_deletes: 0
2025/07/07-06:10:54.027056 42             Options.two_write_queues: 0
2025/07/07-06:10:54.027057 42             Options.manual_wal_flush: 0
2025/07/07-06:10:54.027057 42             Options.atomic_flush: 0
2025/07/07-06:10:54.027058 42             Options.avoid_unnecessary_blocking_io: 0
2025/07/07-06:10:54.027058 42                 Options.persist_stats_to_disk: 0
2025/07/07-06:10:54.027059 42                 Options.write_dbid_to_manifest: 0
2025/07/07-06:10:54.027059 42                 Options.log_readahead_size: 0
2025/07/07-06:10:54.027060 42                 Options.file_checksum_gen_factory: Unknown
2025/07/07-06:10:54.027060 42                 Options.best_efforts_recovery: 0
2025/07/07-06:10:54.027061 42                Options.max_bgerror_resume_count: 2147483647
2025/07/07-06:10:54.027061 42            Options.bgerror_resume_retry_interval: 1000000
2025/07/07-06:10:54.027062 42             Options.allow_data_in_errors: 0
2025/07/07-06:10:54.027062 42             Options.db_host_id: __hostname__
2025/07/07-06:10:54.027063 42             Options.max_background_jobs: 2
2025/07/07-06:10:54.027063 42             Options.max_background_compactions: -1
2025/07/07-06:10:54.027064 42             Options.max_subcompactions: 1
2025/07/07-06:10:54.027064 42             Options.avoid_flush_during_shutdown: 0
2025/07/07-06:10:54.027065 42           Options.writable_file_max_buffer_size: 1048576
2025/07/07-06:10:54.027065 42             Options.delayed_write_rate : 16777216
2025/07/07-06:10:54.027066 42             Options.max_total_wal_size: 0
2025/07/07-06:10:54.027066 42             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/07-06:10:54.027067 42                   Options.stats_dump_period_sec: 600
2025/07/07-06:10:54.027067 42                 Options.stats_persist_period_sec: 600
2025/07/07-06:10:54.027068 42                 Options.stats_history_buffer_size: 1048576
2025/07/07-06:10:54.027068 42                          Options.max_open_files: -1
2025/07/07-06:10:54.027069 42                          Options.bytes_per_sync: 0
2025/07/07-06:10:54.027069 42                      Options.wal_bytes_per_sync: 0
2025/07/07-06:10:54.027070 42                   Options.strict_bytes_per_sync: 0
2025/07/07-06:10:54.027070 42       Options.compaction_readahead_size: 0
2025/07/07-06:10:54.027071 42                  Options.max_background_flushes: 1
2025/07/07-06:10:54.027071 42 Compression algorithms supported:
2025/07/07-06:10:54.027073 42 	kZSTD supported: 1
2025/07/07-06:10:54.027074 42 	kXpressCompression supported: 0
2025/07/07-06:10:54.027075 42 	kBZip2Compression supported: 0
2025/07/07-06:10:54.027075 42 	kZSTDNotFinalCompression supported: 1
2025/07/07-06:10:54.027076 42 	kLZ4Compression supported: 0
2025/07/07-06:10:54.027076 42 	kZlibCompression supported: 0
2025/07/07-06:10:54.027077 42 	kLZ4HCCompression supported: 0
2025/07/07-06:10:54.027077 42 	kSnappyCompression supported: 0
2025/07/07-06:10:54.027079 42 Fast CRC32 supported: Not supported on x86
2025/07/07-06:10:54.034659 42 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000068
2025/07/07-06:10:54.057992 42 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/07-06:10:54.057998 42               Options.comparator: leveldb.BytewiseComparator
2025/07/07-06:10:54.058000 42           Options.merge_operator: None
2025/07/07-06:10:54.058000 42        Options.compaction_filter: None
2025/07/07-06:10:54.058001 42        Options.compaction_filter_factory: None
2025/07/07-06:10:54.058001 42  Options.sst_partitioner_factory: None
2025/07/07-06:10:54.058002 42         Options.memtable_factory: SkipListFactory
2025/07/07-06:10:54.058002 42            Options.table_factory: BlockBasedTable
2025/07/07-06:10:54.058018 42            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fe28e500660)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fe28e440010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/07-06:10:54.058019 42        Options.write_buffer_size: 67108864
2025/07/07-06:10:54.058020 42  Options.max_write_buffer_number: 2
2025/07/07-06:10:54.058020 42        Options.compression[0]: NoCompression
2025/07/07-06:10:54.058021 42        Options.compression[1]: NoCompression
2025/07/07-06:10:54.058022 42        Options.compression[2]: ZSTD
2025/07/07-06:10:54.058022 42        Options.compression[3]: ZSTD
2025/07/07-06:10:54.058023 42        Options.compression[4]: ZSTD
2025/07/07-06:10:54.058023 42                  Options.bottommost_compression: Disabled
2025/07/07-06:10:54.058024 42       Options.prefix_extractor: nullptr
2025/07/07-06:10:54.058024 42   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/07-06:10:54.058025 42             Options.num_levels: 5
2025/07/07-06:10:54.058025 42        Options.min_write_buffer_number_to_merge: 1
2025/07/07-06:10:54.058026 42     Options.max_write_buffer_number_to_maintain: 0
2025/07/07-06:10:54.058026 42     Options.max_write_buffer_size_to_maintain: 0
2025/07/07-06:10:54.058027 42            Options.bottommost_compression_opts.window_bits: -14
2025/07/07-06:10:54.058027 42                  Options.bottommost_compression_opts.level: 32767
2025/07/07-06:10:54.058028 42               Options.bottommost_compression_opts.strategy: 0
2025/07/07-06:10:54.058029 42         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/07-06:10:54.058029 42         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/07-06:10:54.058030 42         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/07-06:10:54.058030 42                  Options.bottommost_compression_opts.enabled: false
2025/07/07-06:10:54.058031 42         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/07-06:10:54.058031 42            Options.compression_opts.window_bits: -14
2025/07/07-06:10:54.058032 42                  Options.compression_opts.level: 32767
2025/07/07-06:10:54.058032 42               Options.compression_opts.strategy: 0
2025/07/07-06:10:54.058033 42         Options.compression_opts.max_dict_bytes: 0
2025/07/07-06:10:54.058033 42         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/07-06:10:54.058262 42         Options.compression_opts.parallel_threads: 1
2025/07/07-06:10:54.058263 42                  Options.compression_opts.enabled: false
2025/07/07-06:10:54.058264 42         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/07-06:10:54.058265 42      Options.level0_file_num_compaction_trigger: 4
2025/07/07-06:10:54.058266 42          Options.level0_slowdown_writes_trigger: 20
2025/07/07-06:10:54.058266 42              Options.level0_stop_writes_trigger: 36
2025/07/07-06:10:54.058267 42                   Options.target_file_size_base: 67108864
2025/07/07-06:10:54.058267 42             Options.target_file_size_multiplier: 2
2025/07/07-06:10:54.058268 42                Options.max_bytes_for_level_base: 268435456
2025/07/07-06:10:54.058268 42 Options.level_compaction_dynamic_level_bytes: 0
2025/07/07-06:10:54.058269 42          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/07-06:10:54.058270 42 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/07-06:10:54.058271 42 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/07-06:10:54.058272 42 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/07-06:10:54.058272 42 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/07-06:10:54.058273 42 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/07-06:10:54.058273 42 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/07-06:10:54.058274 42 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/07-06:10:54.058274 42       Options.max_sequential_skip_in_iterations: 8
2025/07/07-06:10:54.058275 42                    Options.max_compaction_bytes: 1677721600
2025/07/07-06:10:54.058275 42                        Options.arena_block_size: 1048576
2025/07/07-06:10:54.058276 42   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/07-06:10:54.058276 42   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/07-06:10:54.058277 42       Options.rate_limit_delay_max_milliseconds: 100
2025/07/07-06:10:54.058277 42                Options.disable_auto_compactions: 0
2025/07/07-06:10:54.058279 42                        Options.compaction_style: kCompactionStyleLevel
2025/07/07-06:10:54.058280 42                          Options.compaction_pri: kMinOverlappingRatio
2025/07/07-06:10:54.058280 42 Options.compaction_options_universal.size_ratio: 1
2025/07/07-06:10:54.058281 42 Options.compaction_options_universal.min_merge_width: 2
2025/07/07-06:10:54.058281 42 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/07-06:10:54.058282 42 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/07-06:10:54.058283 42 Options.compaction_options_universal.compression_size_percent: -1
2025/07/07-06:10:54.058283 42 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/07-06:10:54.058284 42 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/07-06:10:54.058284 42 Options.compaction_options_fifo.allow_compaction: 0
2025/07/07-06:10:54.058287 42                   Options.table_properties_collectors: 
2025/07/07-06:10:54.058288 42                   Options.inplace_update_support: 0
2025/07/07-06:10:54.058289 42                 Options.inplace_update_num_locks: 10000
2025/07/07-06:10:54.058289 42               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/07-06:10:54.058290 42               Options.memtable_whole_key_filtering: 0
2025/07/07-06:10:54.058290 42   Options.memtable_huge_page_size: 0
2025/07/07-06:10:54.058291 42                           Options.bloom_locality: 0
2025/07/07-06:10:54.058291 42                    Options.max_successive_merges: 0
2025/07/07-06:10:54.058292 42                Options.optimize_filters_for_hits: 0
2025/07/07-06:10:54.058292 42                Options.paranoid_file_checks: 0
2025/07/07-06:10:54.058293 42                Options.force_consistency_checks: 1
2025/07/07-06:10:54.058293 42                Options.report_bg_io_stats: 0
2025/07/07-06:10:54.058294 42                               Options.ttl: 2592000
2025/07/07-06:10:54.059617 42          Options.periodic_compaction_seconds: 0
2025/07/07-06:10:54.059619 42                       Options.enable_blob_files: false
2025/07/07-06:10:54.059620 42                           Options.min_blob_size: 0
2025/07/07-06:10:54.059620 42                          Options.blob_file_size: 268435456
2025/07/07-06:10:54.059621 42                   Options.blob_compression_type: NoCompression
2025/07/07-06:10:54.059621 42          Options.enable_blob_garbage_collection: false
2025/07/07-06:10:54.059622 42      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/07-06:10:54.059623 42 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/07-06:10:54.059623 42          Options.blob_compaction_readahead_size: 0
2025/07/07-06:10:54.062432 42 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/07/07-06:10:54.062434 42               Options.comparator: leveldb.BytewiseComparator
2025/07/07-06:10:54.062436 42           Options.merge_operator: None
2025/07/07-06:10:54.062437 42        Options.compaction_filter: None
2025/07/07-06:10:54.062437 42        Options.compaction_filter_factory: None
2025/07/07-06:10:54.062438 42  Options.sst_partitioner_factory: None
2025/07/07-06:10:54.062438 42         Options.memtable_factory: SkipListFactory
2025/07/07-06:10:54.062439 42            Options.table_factory: BlockBasedTable
2025/07/07-06:10:54.062447 42            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fe28e500660)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fe28e440010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/07-06:10:54.062448 42        Options.write_buffer_size: 67108864
2025/07/07-06:10:54.062448 42  Options.max_write_buffer_number: 2
2025/07/07-06:10:54.062449 42        Options.compression[0]: NoCompression
2025/07/07-06:10:54.062449 42        Options.compression[1]: NoCompression
2025/07/07-06:10:54.062450 42        Options.compression[2]: ZSTD
2025/07/07-06:10:54.062451 42        Options.compression[3]: ZSTD
2025/07/07-06:10:54.062451 42        Options.compression[4]: ZSTD
2025/07/07-06:10:54.062452 42                  Options.bottommost_compression: Disabled
2025/07/07-06:10:54.062452 42       Options.prefix_extractor: nullptr
2025/07/07-06:10:54.062453 42   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/07-06:10:54.062453 42             Options.num_levels: 5
2025/07/07-06:10:54.062454 42        Options.min_write_buffer_number_to_merge: 1
2025/07/07-06:10:54.062454 42     Options.max_write_buffer_number_to_maintain: 0
2025/07/07-06:10:54.062455 42     Options.max_write_buffer_size_to_maintain: 0
2025/07/07-06:10:54.062455 42            Options.bottommost_compression_opts.window_bits: -14
2025/07/07-06:10:54.062456 42                  Options.bottommost_compression_opts.level: 32767
2025/07/07-06:10:54.062456 42               Options.bottommost_compression_opts.strategy: 0
2025/07/07-06:10:54.062457 42         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/07-06:10:54.062457 42         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/07-06:10:54.062667 42         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/07-06:10:54.062669 42                  Options.bottommost_compression_opts.enabled: false
2025/07/07-06:10:54.062670 42         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/07-06:10:54.062671 42            Options.compression_opts.window_bits: -14
2025/07/07-06:10:54.062672 42                  Options.compression_opts.level: 32767
2025/07/07-06:10:54.062672 42               Options.compression_opts.strategy: 0
2025/07/07-06:10:54.062673 42         Options.compression_opts.max_dict_bytes: 0
2025/07/07-06:10:54.062673 42         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/07-06:10:54.062674 42         Options.compression_opts.parallel_threads: 1
2025/07/07-06:10:54.062674 42                  Options.compression_opts.enabled: false
2025/07/07-06:10:54.062675 42         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/07-06:10:54.062675 42      Options.level0_file_num_compaction_trigger: 4
2025/07/07-06:10:54.062676 42          Options.level0_slowdown_writes_trigger: 20
2025/07/07-06:10:54.062677 42              Options.level0_stop_writes_trigger: 36
2025/07/07-06:10:54.062677 42                   Options.target_file_size_base: 67108864
2025/07/07-06:10:54.062678 42             Options.target_file_size_multiplier: 2
2025/07/07-06:10:54.062678 42                Options.max_bytes_for_level_base: 268435456
2025/07/07-06:10:54.062679 42 Options.level_compaction_dynamic_level_bytes: 0
2025/07/07-06:10:54.062679 42          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/07-06:10:54.062680 42 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/07-06:10:54.062681 42 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/07-06:10:54.062681 42 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/07-06:10:54.062682 42 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/07-06:10:54.062682 42 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/07-06:10:54.062683 42 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/07-06:10:54.062683 42 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/07-06:10:54.062684 42       Options.max_sequential_skip_in_iterations: 8
2025/07/07-06:10:54.062684 42                    Options.max_compaction_bytes: 1677721600
2025/07/07-06:10:54.062685 42                        Options.arena_block_size: 1048576
2025/07/07-06:10:54.062685 42   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/07-06:10:54.062686 42   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/07-06:10:54.062686 42       Options.rate_limit_delay_max_milliseconds: 100
2025/07/07-06:10:54.062687 42                Options.disable_auto_compactions: 0
2025/07/07-06:10:54.062688 42                        Options.compaction_style: kCompactionStyleLevel
2025/07/07-06:10:54.062688 42                          Options.compaction_pri: kMinOverlappingRatio
2025/07/07-06:10:54.062689 42 Options.compaction_options_universal.size_ratio: 1
2025/07/07-06:10:54.062689 42 Options.compaction_options_universal.min_merge_width: 2
2025/07/07-06:10:54.062690 42 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/07-06:10:54.062690 42 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/07-06:10:54.062691 42 Options.compaction_options_universal.compression_size_percent: -1
2025/07/07-06:10:54.062691 42 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/07-06:10:54.062692 42 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/07-06:10:54.062692 42 Options.compaction_options_fifo.allow_compaction: 0
2025/07/07-06:10:54.062694 42                   Options.table_properties_collectors: 
2025/07/07-06:10:54.062694 42                   Options.inplace_update_support: 0
2025/07/07-06:10:54.062695 42                 Options.inplace_update_num_locks: 10000
2025/07/07-06:10:54.062695 42               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/07-06:10:54.063393 42               Options.memtable_whole_key_filtering: 0
2025/07/07-06:10:54.063395 42   Options.memtable_huge_page_size: 0
2025/07/07-06:10:54.063396 42                           Options.bloom_locality: 0
2025/07/07-06:10:54.063397 42                    Options.max_successive_merges: 0
2025/07/07-06:10:54.063398 42                Options.optimize_filters_for_hits: 0
2025/07/07-06:10:54.063398 42                Options.paranoid_file_checks: 0
2025/07/07-06:10:54.063399 42                Options.force_consistency_checks: 1
2025/07/07-06:10:54.063400 42                Options.report_bg_io_stats: 0
2025/07/07-06:10:54.063400 42                               Options.ttl: 2592000
2025/07/07-06:10:54.063401 42          Options.periodic_compaction_seconds: 0
2025/07/07-06:10:54.063401 42                       Options.enable_blob_files: false
2025/07/07-06:10:54.063402 42                           Options.min_blob_size: 0
2025/07/07-06:10:54.063402 42                          Options.blob_file_size: 268435456
2025/07/07-06:10:54.063403 42                   Options.blob_compression_type: NoCompression
2025/07/07-06:10:54.063403 42          Options.enable_blob_garbage_collection: false
2025/07/07-06:10:54.063404 42      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/07-06:10:54.063405 42 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/07-06:10:54.063406 42          Options.blob_compaction_readahead_size: 0
2025/07/07-06:10:54.278875 42 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000068 succeeded,manifest_file_number is 68, next_file_number is 83, last_sequence is 2111390, log_number is 63,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 62
2025/07/07-06:10:54.278883 42 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 63
2025/07/07-06:10:54.278884 42 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 63
2025/07/07-06:10:54.287077 42 [db/version_set.cc:4409] Creating manifest 84
2025/07/07-06:10:54.392176 42 EVENT_LOG_v1 {"time_micros": 1751868654392171, "job": 1, "event": "recovery_started", "wal_files": [69]}
2025/07/07-06:10:54.392182 42 [db/db_impl/db_impl_open.cc:888] Recovering log #69 mode 2
2025/07/07-06:10:56.055024 42 EVENT_LOG_v1 {"time_micros": 1751868656055006, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 85, "file_size": 20409982, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 20392125, "index_size": 16926, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 31531513, "raw_average_key_size": 49, "raw_value_size": 10867611, "raw_average_value_size": 17, "num_data_blocks": 312, "num_entries": 639153, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1751868655, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "4FUN369NNPH8ZW9K11KB", "orig_file_number": 85}}
2025/07/07-06:10:56.055329 42 [db/version_set.cc:4409] Creating manifest 86
2025/07/07-06:10:56.194012 42 EVENT_LOG_v1 {"time_micros": 1751868656194007, "job": 1, "event": "recovery_finished"}
2025/07/07-06:10:56.317872 42 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000069.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/07-06:10:56.318051 42 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fe28bb90700
2025/07/07-06:10:56.319102 42 DB pointer 0x7fe28ba21c00
2025/07/07-06:10:59.322430 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/07-06:10:59.322444 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5.3 total, 5.3 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   19.46 MB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     42.5      0.46              0.00         1    0.458       0      0       0.0       0.0
  L1      4/0   252.85 MB   1.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
  L2      5/0   274.15 MB   0.1      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum     10/0   546.47 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     42.5      0.46              0.00         1    0.458       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     42.5      0.46              0.00         1    0.458       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     42.5      0.46              0.00         1    0.458       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5.3 total, 5.3 interval
Flush(GB): cumulative 0.019, interval 0.019
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.02 GB write, 3.70 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.5 seconds
Interval compaction: 0.02 GB write, 3.70 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.5 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fe28e440010#8 capacity: 951.98 MB collections: 1 last_copies: 2 last_secs: 4e-05 secs_since: 5
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5.3 total, 5.3 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fe28e440010#8 capacity: 951.98 MB collections: 1 last_copies: 2 last_secs: 4e-05 secs_since: 5
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
