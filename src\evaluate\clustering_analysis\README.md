# 聚类分析工具使用指南

本目录包含了用于分析零件特征的聚类分析工具，支持从 `scripts/extract_part_features.py` 生成的 `part_features.pkl` 文件中读取不同类型的特征进行分析。

## 文件说明

### 核心工具

1. **`hdbscan_cluster_embeddings.py`** - 使用 HDBSCAN 算法对零件特征进行聚类分析
2. **`visualize_hdbscan_clusters.py`** - 可视化 HDBSCAN 聚类结果
3. **`visualize_part_embeddings.py`** - 使用降维算法（TSNE、PCA、UMAP）对特征进行可视化

### 辅助工具

4. **`clustering_analysis_example.py`** - 完整的使用示例脚本

## 支持的特征类型

- **structural**: 结构化特征（几何、物理、孔特征等）
- **shape**: 形状特征（CLIP 形状向量）
- **semantic**: 语义特征（文本嵌入向量）
- **fused_features**: 融合特征（加权拼接的多模态特征）

## 使用方法

### 1. 准备特征文件

首先需要运行特征提取脚本生成 `part_features.pkl` 文件：

```bash
# 提取所有特征但不进行融合
python scripts/extract_part_features.py --max-parts 100

# 提取所有特征并启用融合
python scripts/extract_part_features.py --enable-fusion --max-parts 100
```

### 2. 聚类分析

对不同类型的特征进行 HDBSCAN 聚类：

```bash
# 分析形状特征
python evaluate_scripts/clustering_analysis/hdbscan_cluster_embeddings.py \
    --embed dataset/part_features.pkl \
    --feature-type shape \
    --out dataset/hdbscan_labels_shape.pkl \
    --min_cluster_size 15

# 分析融合特征
python evaluate_scripts/clustering_analysis/hdbscan_cluster_embeddings.py \
    --embed dataset/part_features.pkl \
    --feature-type fused_features \
    --out dataset/hdbscan_labels_fused.pkl \
    --min_cluster_size 15

# 分析结构化特征
python evaluate_scripts/clustering_analysis/hdbscan_cluster_embeddings.py \
    --embed dataset/part_features.pkl \
    --feature-type structural \
    --out dataset/hdbscan_labels_structural.pkl \
    --min_cluster_size 15
```

### 3. 可视化聚类结果

```bash
# 可视化形状特征的聚类结果
python evaluate_scripts/clustering_analysis/visualize_hdbscan_clusters.py \
    --labels dataset/hdbscan_labels_shape.pkl \
    --img_dir datasets/fusion360_assembly \
    --out_dir visualization_results/clusters_shape \
    --top_n 15

# 可视化融合特征的聚类结果
python evaluate_scripts/clustering_analysis/visualize_hdbscan_clusters.py \
    --labels dataset/hdbscan_labels_fused.pkl \
    --img_dir datasets/fusion360_assembly \
    --out_dir visualization_results/clusters_fused \
    --top_n 15
```

### 4. 特征降维可视化

```bash
# 使用 UMAP 对形状特征进行降维可视化
python evaluate_scripts/clustering_analysis/visualize_part_embeddings.py \
    --feature_path dataset/part_features.pkl \
    --feature_type shape \
    --method umap \
    --output_dir visualization_results/shape_umap

# 使用 TSNE 对融合特征进行降维可视化
python evaluate_scripts/clustering_analysis/visualize_part_embeddings.py \
    --feature_path dataset/part_features.pkl \
    --feature_type fused_features \
    --method tsne \
    --output_dir visualization_results/fused_tsne
```

### 5. 运行完整示例

```bash
python evaluate_scripts/clustering_analysis/clustering_analysis_example.py
```

## 参数说明

### HDBSCAN 聚类参数

- `--feature-type`: 特征类型（structural, shape, semantic, fused_features）
- `--min_cluster_size`: 最小簇大小
- `--metric`: 距离度量（euclidean, cosine）
- `--norm`: 是否进行 L2 归一化
- `--pca`: 是否使用 PCA 降维
- `--pca-components`: PCA 降维后的维度

### 可视化参数

- `--top_n`: 显示前 N 个最大的簇
- `--samples_per_cluster`: 每个簇显示的样本数
- `--min_cluster_size`: 最小簇大小过滤
- `--color_by`: 着色方式（assembly, part_id, random）
- `--method`: 降维方法（tsne, pca, umap）

## 输出文件

- **聚类标签**: `dataset/hdbscan_labels_*.pkl`
- **聚类可视化**: `visualization_results/clusters_*/`
  - `index.html`: 聚类结果总览
  - `cluster_*.png`: 各簇的图片网格
- **特征降维可视化**: `visualization_results/embeddings_*/`
  - 降维后的散点图可视化

## 兼容性

这些工具同时支持：

- 新格式：`part_features.pkl`（推荐）
- 旧格式：`clip_features.pkl`（通过 `--legacy-format` 参数）

## 依赖要求

```bash
pip install hdbscan umap-learn matplotlib scikit-learn numpy pandas tqdm pillow
```

## 注意事项

1. 确保图片目录 `datasets/fusion360_assembly` 存在且包含零件图片
2. 对于大数据集，建议调整 `--min_cluster_size` 参数
3. TSNE 降维对于高维特征可能需要较长时间
4. 融合特征需要先运行 `--enable-fusion` 选项生成
