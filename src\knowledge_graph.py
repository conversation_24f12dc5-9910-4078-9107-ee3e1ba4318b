from typing import Dict, List, Any, Optional, Tuple, Union
from neo4j import GraphDatabase, exceptions
import logging
import time
from src.config import Config
from src.data_formats import CADModelData, AssemblyData, SubAssemblyData, PartData, FeatureData
from src.data_converters import get_converter
import numpy as np
class CADKnowledgeGraph:
    """
    CAD知识图谱操作类，用于管理装配体模型的知识图谱
    
    实体类型:
    - Assembly: 顶层装配体  
    - SubAssembly: 子装配体 
    - Part: 零件
    - Feature: 特征
    
    关系类型:
    - hasSubAssembly: 装配体包含子装配体
    - hasPart: 装配体/子装配体包含零件
    - hasFeature: 零件包含特征
    """
    
    def __init__(self, uri: str = None, user: str = None, password: str = None):
        """
        初始化知识图谱连接
        
        Args:
            uri: Neo4j数据库URI，例如"bolt://localhost:7687"，如果为None则使用环境变量
            user: 用户名，如果为None则使用环境变量
            password: 密码，如果为None则使用环境变量
        """
        # 如果参数为None，则使用环境变量中的配置
        self.uri = uri or Config.NEO4J_URI
        self.user = user or Config.NEO4J_USER
        self.password = password or Config.NEO4J_PASSWORD
        
        self.driver = None
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
            logging.info("成功连接到Neo4j数据库")
        except Exception as e:
            logging.error(f"连接Neo4j数据库失败: {e}")
            raise
    
    @classmethod
    def from_config(cls):
        """
        使用配置创建知识图谱实例
        
        Returns:
            CADKnowledgeGraph实例
        """
        config = Config.get_neo4j_config()
        return cls(
            uri=config["uri"],
            user=config["user"],
            password=config["password"]
        )
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logging.info("已关闭Neo4j数据库连接")
    
    def create_assembly(self, name: str, area: float = 0.0, volume: float = 0.0, 
                       density: float = 0.0, mass: float = 0.0, length: float = 0.0, 
                       width: float = 0.0, height: float = 0.0, part_count: float = 0.0,
                       industry: str = None, category: str = None, description: str = None,
                       assembly_id: str = None, shape_embedding: np.ndarray = None) -> str:
        """
        创建顶层装配体
        
        Args:
            name: 装配体名称
            length: 长度
            width: 宽度
            height: 高度
            area: 表面积
            volume: 体积
            density: 密度
            mass: 质量
            part_count: 零件数量
            industry: 行业标签
            category: 类别标签
            description: 描述信息
            assembly_id: 指定的装配体ID，如果不提供则使用Neo4j生成的ID
            shape_embedding: 形状嵌入向量
            
        Returns:
            装配体ID
        """
        start_time = time.time()
        props = {
            "uuid": assembly_id,
            "name": name,
            "length": length,
            "width": width,
            "height": height,
            "area": area,
            "volume": volume,
            "density": density,
            "mass": mass,
            "part_count": part_count,
            "shape_embedding": shape_embedding.tolist() if shape_embedding is not None else None
        }
        if industry:
            props["industry"] = industry
        if category:
            props["category"] = category
        if description:
            props["description"] = description
        
        prop_str = ", ".join([f"{key}: ${key}" for key in props.keys()])
        
        query = f"""
        CREATE (a:Assembly {{{prop_str}}})
        RETURN a.uuid as uuid
        """
        
        with self.driver.session() as session:
            result = session.run(query, props)
            record = result.single()
            end_time = time.time()
            execution_time = end_time - start_time
            logging.info(f"创建装配体耗时: {execution_time:.4f}秒")
            return record["uuid"] if record else None
    
    def create_subassembly(self, name: str, parent_id: str, 
                          subassembly_id: str = None) -> str:
        """
        创建子装配体并与父装配体建立关系
        
        Args:
            name: 子装配体名称
            parent_id: 父装配体ID
            subassembly_id: 指定的子装配体ID，如果不提供则使用Neo4j生成的ID
            
        Returns:
            子装配体ID
        """
        query = """
        MATCH (parent) 
        WHERE parent.uuid = $parent_id
        CREATE (sub:SubAssembly {
            uuid: $subassembly_id,
            name: $name
        })
        CREATE (parent)-[:hasSubAssembly]->(sub)
        RETURN sub.uuid as uuid
        """
        
        params = {
            "parent_id": parent_id,
            "subassembly_id": subassembly_id,
            "name": name
        }
        
        with self.driver.session() as session:
            result = session.run(query, params)
            record = result.single()
            return record["uuid"] if record else None
    
    def create_part(self, name: str, area: float = 0.0, volume: float = 0.0, 
                   density: float = 0.0, mass: float = 0.0, length: float = 0.0,
                   width: float = 0.0, height: float = 0.0, hole_count: float = 0.0,
                   parent_id: str = None, material: str = None, description: str = None,
                   part_id: str = None, shape_embedding: np.ndarray = None) -> str:
        """
        创建零件并与父装配体或子装配体建立关系
        
        Args:
            name: 零件名称
            length: 长度
            width: 宽度
            height: 高度
            area: 表面积
            volume: 体积
            density: 密度
            mass: 质量
            hole_count: 孔数量
            parent_id: 父装配体或子装配体ID
            material: 材料
            description: 描述信息
            part_id: 指定的零件ID，如果不提供则使用Neo4j生成的ID
            shape_embedding: 形状嵌入向量
            
        Returns:
            零件ID
        """
        node_props = {
            "uuid": part_id,
            "name": name,
            "length": length,
            "width": width,
            "height": height,
            "area": area,
            "volume": volume,
            "density": density,
            "mass": mass,
            "hole_count": hole_count,
            "shape_embedding": shape_embedding.tolist() if shape_embedding is not None else None
        }
        if material:
            node_props["material"] = material
        if description:
            node_props["description"] = description
            
        prop_str = ", ".join([f"{key}: ${key}" for key in node_props.keys()])
        
        query = f"""
        MATCH (parent) 
        WHERE parent.uuid = $parent_id
        CREATE (p:Part {{{prop_str}}})
        CREATE (parent)-[:hasPart]->(p)
        RETURN p.uuid as uuid
        """
        
        params = {"parent_id": parent_id, **node_props}
        
        with self.driver.session() as session:
            result = session.run(query, params)
            record = result.single()
            return record["uuid"] if record else None
    
    def create_feature(self, name: str, type: str, length: float = 0.0, 
                      diameter: float = 0.0, parent_id: str = None, feature_id: str = None) -> str:
        """
        创建特征并与父零件建立关系
        
        Args:
            name: 特征名称
            type: 特征类型
            length: 特征长度
            diameter: 特征直径
            parent_id: 父零件ID
            feature_id: 指定的特征ID，如果不提供则使用Neo4j生成的ID
        
        Returns:
            特征ID
        """
        query = """
        MATCH (parent) 
        WHERE parent.uuid = $parent_id
        CREATE (f:Feature {
            uuid: $feature_id,
            name: $name,
            type: $type,
            length: $length,
            diameter: $diameter
        })
        CREATE (parent)-[:hasFeature]->(f)
        RETURN f.uuid as uuid
        """
        
        params = {
            "parent_id": parent_id,
            "feature_id": feature_id,
            "name": name,
            "type": type,
            "length": length,
            "diameter": diameter
        }
        
        with self.driver.session() as session:
            result = session.run(query, params)
            record = result.single()
            return record["uuid"] if record else None
    
    def get_assembly_structure(self, assembly_id: str) -> Dict:
        """
        获取装配体的完整结构
        
        Args:
            assembly_id: 装配体ID
            
        Returns:
            装配体结构的字典表示
        """
        query = """
        MATCH (a:Assembly) WHERE a.uuid = $assembly_id
        OPTIONAL MATCH (a)-[:hasSubAssembly*1..]->(sub)
        OPTIONAL MATCH (a)-[:hasPart*1..]->(p)
        OPTIONAL MATCH (p)-[:hasFeature]->(f)
        RETURN a, collect(distinct sub) as subassemblies, 
               collect(distinct p) as parts, collect(distinct f) as features
        """
        
        params = {"assembly_id": assembly_id}
        
        with self.driver.session() as session:
            result = session.run(query, params)
            record = result.single()
            if not record:
                return None
                
            assembly = dict(record["a"])
            assembly["subassemblies"] = [dict(sub) for sub in record["subassemblies"]]
            assembly["parts"] = [dict(part) for part in record["parts"]]
            assembly["features"] = [dict(feature) for feature in record["features"]]
            return assembly
    
    def search_by_property(self, property_name: str, property_value: Any) -> List[Dict]:
        """
        根据属性搜索实体
        
        Args:
            property_name: 属性名称
            property_value: 属性值
            
        Returns:
            匹配的实体列表
        """
        query = f"""
        MATCH (n) 
        WHERE n.{property_name} = $property_value
        RETURN n, labels(n) as types
        """
        
        params = {"property_value": property_value}
        
        with self.driver.session() as session:
            result = session.run(query, params)
            return [{"entity": dict(record["n"]), "types": record["types"]} 
                   for record in result]
    
    def get_related_entities(self, entity_id: str, relation_type: str = None) -> List[Dict]:
        """
        获取与指定实体相关的实体
        
        Args:
            entity_id: 实体ID
            relation_type: 关系类型，如果为None则获取所有关系
            
        Returns:
            相关实体列表
        """
        if relation_type:
            query = f"""
            MATCH (n)-[r:{relation_type}]->(related)
            WHERE n.uuid = $entity_id
            RETURN related, type(r) as relation_type
            """
        else:
            query = """
            MATCH (n)-[r]->(related)
            WHERE n.uuid = $entity_id
            RETURN related, type(r) as relation_type
            """
        
        params = {"entity_id": entity_id}
        
        with self.driver.session() as session:
            result = session.run(query, params)
            return [{"entity": dict(record["related"]), 
                    "relation": record["relation_type"]} 
                   for record in result]
    
    def delete_entity(self, entity_id: str, cascade: bool = False) -> bool:
        """
        删除实体
        
        Args:
            entity_id: 实体ID
            cascade: 是否级联删除关联实体
            
        Returns:
            是否删除成功
        """
        if cascade:
            query = """
            MATCH (n) 
            WHERE n.uuid = $entity_id
            OPTIONAL MATCH (n)-[r*1..]->(related)
            DETACH DELETE n, related
            """
        else:
            query = """
            MATCH (n) 
            WHERE n.uuid = $entity_id
            DETACH DELETE n
            """
        
        params = {"entity_id": entity_id}
        
        with self.driver.session() as session:
            session.run(query, params)
            return True
    
    def clear_database(self) -> None:
        """清空数据库中的所有节点和关系"""
        query = "MATCH (n) DETACH DELETE n"
        
        with self.driver.session() as session:
            session.run(query)
            logging.info("已清空数据库")
    
    def import_from_json(self, json_data: Dict, format_type: str = "fusion360") -> str:
        """
        从JSON数据导入装配体结构
        
        Args:
            json_data: 装配体JSON数据
            format_type: 数据格式类型，例如 "fusion360", "onshape" 等
            
        Returns:
            顶层装配体ID
        """
        start_time = time.time()
        # 使用适当的转换器将源数据转换为统一格式
        converter = get_converter(format_type)
        cad_data = converter.convert(json_data)
        
        # 导入统一格式数据
        assembly_id = self.import_from_unified_format(cad_data)
        
        end_time = time.time()
        execution_time = end_time - start_time
        logging.info(f"从JSON导入装配体总耗时: {execution_time:.4f}秒")
        return assembly_id
    
    def import_from_unified_format(self, cad_data: CADModelData) -> str:
        """
        从统一格式数据导入装配体结构
        
        Args:
            cad_data: 统一格式的CAD模型数据
            
        Returns:
            顶层装配体ID
        """
        start_time = time.time()
        assembly = cad_data.assembly
        
        # 创建顶层装配体
        assembly_id = self.create_assembly(
            name=assembly.name,
            length=assembly.length,
            width=assembly.width,
            height=assembly.height,
            area=assembly.area,
            volume=assembly.volume,
            density=assembly.density,
            mass=assembly.mass,
            part_count=assembly.part_count,
            industry=assembly.industry,
            category=assembly.category,
            description=assembly.description,
            assembly_id=assembly.assembly_id,  # 使用数据对象自带的ID
            shape_embedding=assembly.shape_embedding
        )
        
        if not assembly_id:
            raise RuntimeError("创建顶层装配体失败")
        
        # 处理子装配体
        for subassembly in assembly.subassemblies:
            self._import_subassembly(subassembly, assembly_id)
        
        # 处理零件
        for part in assembly.parts:
            self._import_part(part, assembly_id)
        
        end_time = time.time()
        execution_time = end_time - start_time
        logging.info(f"导入装配体 {assembly.name} 总耗时: {execution_time:.4f}秒")
        return assembly_id
    
    def _import_subassembly(self, subassembly: SubAssemblyData, parent_id: str) -> str:
        """
        导入子装配体
        
        Args:
            subassembly: 子装配体数据
            parent_id: 父节点ID
            
        Returns:
            子装配体ID
        """
        # 创建子装配体
        subassembly_id = self.create_subassembly(
            name=subassembly.name,
            parent_id=parent_id,
            subassembly_id=subassembly.subassembly_id  # 使用数据对象自带的ID
        )
        
        if not subassembly_id:
            raise RuntimeError(f"创建子装配体 {subassembly.name} 失败")
        
        # 递归处理子装配体
        for child_subassembly in subassembly.subassemblies:
            self._import_subassembly(child_subassembly, subassembly_id)
        
        # 处理零件
        for part in subassembly.parts:
            self._import_part(part, subassembly_id)
        
        return subassembly_id
    
    def _import_part(self, part: PartData, parent_id: str) -> str:
        """
        导入零件
        
        Args:
            part: 零件数据
            parent_id: 父节点ID
            
        Returns:
            零件ID
        """
        # 创建零件
        part_id = self.create_part(
            name=part.name,
            length=part.length,
            width=part.width,
            height=part.height,
            area=part.area,
            volume=part.volume,
            density=part.density,
            mass=part.mass,
            hole_count=part.hole_count,
            parent_id=parent_id,
            material=part.material,
            description=part.description,
            part_id=part.part_id,  # 使用数据对象自带的ID
            shape_embedding=part.shape_embedding
        )
        
        if not part_id:
            raise RuntimeError(f"创建零件 {part.name} 失败")
        
        # 处理特征
        for feature in part.features:
            self._import_feature(feature, part_id)
        
        return part_id
    
    def _import_feature(self, feature: FeatureData, parent_id: str) -> str:
        """
        导入特征
        
        Args:
            feature: 特征数据
            parent_id: 父节点ID
        
        Returns:
            特征ID
        """
        # 创建特征
        feature_id = self.create_feature(
            name=feature.name,
            type=feature.type,
            length=feature.length,
            diameter=feature.diameter,
            parent_id=parent_id,
            feature_id=feature.feature_id  # 使用数据对象自带的ID
        )
        
        if not feature_id:
            raise RuntimeError(f"创建特征 {feature.name} 失败")
        
        return feature_id
    
    def run_cypher(self, cypher_query: str):
        """
        执行输入的 Cypher 语句，返回查询结果，包含异常处理。

        参数:
            cypher_query (str): 完整的 Cypher 查询语句。

        返回:
            list[dict]: 查询结果（每条记录是一个字典），或在出错时返回错误信息。
        """
        try:
            with self.driver.session() as session:
                result = session.run(cypher_query)
                return [record.data() for record in result]
        except exceptions.CypherSyntaxError as e:
            return {"error": "Cypher 语法错误", "details": str(e)}
        except exceptions.Neo4jError as e:
            return {"error": "Neo4j 错误", "details": str(e)}
        except Exception as e:
            return {"error": "未知错误", "details": str(e)}