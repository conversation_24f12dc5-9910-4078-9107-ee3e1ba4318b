version: '3.8'

services:
  neo4j:
    image: neo4j:latest
    container_name: neo4j-gds-apoc
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["graph-data-science","apoc"]
      - NEO4J_dbms_default__database=neo4j
      - NEO4J_dbms_security_procedures_unrestricted=gds.*,apoc.*
      - NEO4J_dbms_security_procedures_allowlist=gds.*,apoc.*
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
    volumes:
      - .\data:/data
      - .\logs:/logs
      - .\import:/var/lib/neo4j/import
      - .\plugins:/plugins
      - .\conf:/var/lib/neo4j/conf
    restart: unless-stopped
    networks:
      - neo4j-network

networks:
  neo4j-network:
    driver: bridge
