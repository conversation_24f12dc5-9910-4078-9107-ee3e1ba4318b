{"plan_id": "6efef693-1fad-4eee-ade8-9a39cf9e9711", "query_text": "给我找一个包含Cabos和Shell的装配体，用于流体流量的测量和监控，并且重量要超过100", "intent_analysis": {"main_query_object": "装配体", "shape_geometry_constraints": [], "attribute_constraints": {"mass": ">100"}, "semantic_descriptions": ["用于流体流量的测量和监控"], "structural_relationship_constraints": ["包含Cabos", "包含Shell"], "semantic_description": ""}, "steps": [{"agent": "StructuralRelationshipAgent", "parameters": {"query_text": "包含Cabos和Shell的装配体"}, "step_name": "structural_query", "description": "首先查询包含指定组件的装配体", "id": "step1"}, {"agent": "StructuredDataAgent", "parameters": {"query_text": "质量超过100的装配体", "input_ids": "来自structural_query的结果"}, "step_name": "attribute_filter", "description": "对结构查询结果进行质量筛选", "depends_on": "structural_query", "id": "step2"}, {"agent": "GeometrySemanticAgent", "parameters": {"query_text": "用于流体流量的测量和监控", "input_ids": "来自attribute_filter的结果", "shape_weight": 0.2, "top_k": 10}, "step_name": "semantic_filter", "description": "对筛选后的结果进行功能匹配", "depends_on": "attribute_filter", "id": "step3"}], "result_fusion": {"merge_method": "intersection", "ranking_criteria": [{"field": "mass", "direction": "desc"}, {"field": "semantic_similarity", "direction": "desc"}], "description": "先按质量降序，再按语义相似度降序"}}