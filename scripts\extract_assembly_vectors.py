#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
提取装配体向量表示，先运行scripts\neo4j_to_pyg_optimized.py

本脚本从保存的装配体pt文件中提取所有装配体节点的ID和向量表示，
支持不同的图类型和初始化方法。

使用方法：
python scripts/extract_assembly_vectors.py [--base-dir 基础目录] [--graph-type 图类型] [--output-path 输出路径]

功能：
1. 遍历所有装配体目录
2. 加载对应的图文件
3. 提取Assembly节点的向量表示
4. 保存为pickle文件
5. 提供统计信息和可视化分析
"""

import os
import sys
import argparse
import logging
import pickle
from typing import Dict, List, Tuple, Optional
import numpy as np

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import torch
    print("✓ PyTorch 可用")
except ImportError:
    print("✗ 需要安装 PyTorch")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AssemblyVectorExtractor:
    """装配体向量提取器"""
    
    def __init__(self, base_dir: str = "datasets/fusion360_assembly"):
        """
        初始化提取器
        
        Args:
            base_dir: 装配体文件基础目录
        """
        self.base_dir = base_dir
        self.assembly_representations = {}
        
    def extract_representations(self) -> Dict[str, np.ndarray]:
        """
        提取装配体向量表示

        Returns:
            Dict[str, np.ndarray]: {assembly_id: assembly_vector}
        """
        logger.info("开始提取装配体向量表示")

        if not os.path.exists(self.base_dir):
            logger.error(f"基础目录不存在: {self.base_dir}")
            return {}

        assembly_representations = {}
        failed_count = 0

        # 遍历所有装配体目录
        assembly_dirs = [d for d in os.listdir(self.base_dir)
                        if os.path.isdir(os.path.join(self.base_dir, d))]

        logger.info(f"找到 {len(assembly_dirs)} 个装配体目录")

        for i, assembly_dir in enumerate(assembly_dirs, 1):
            assembly_path = os.path.join(self.base_dir, assembly_dir)

            # 构建图文件路径
            graph_file = os.path.join(assembly_path, "assembly.pt")
            
            if os.path.exists(graph_file):
                try:
                    # 加载图数据
                    data = torch.load(graph_file, map_location='cpu', weights_only=False)
                    
                    # 获取装配体节点的向量表示
                    if hasattr(data, 'assembly_node_idx') and data.assembly_node_idx is not None:
                        assembly_vector = data.x[data.assembly_node_idx].numpy()
                        assembly_id = data.assembly_id
                        assembly_representations[assembly_id] = assembly_vector
                        
                        if i % 100 == 0:
                            logger.info(f"进度: {i}/{len(assembly_dirs)} - 当前: {assembly_id}")
                    else:
                        logger.warning(f"装配体 {assembly_dir} 没有assembly_node_idx")
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"加载装配体图文件失败 {graph_file}: {e}")
                    failed_count += 1
            else:
                logger.debug(f"图文件不存在: {graph_file}")
                failed_count += 1
        
        logger.info(f"提取完成: 成功 {len(assembly_representations)} 个，失败 {failed_count} 个")
        self.assembly_representations = assembly_representations
        return assembly_representations
    
    def save_representations(self, representations: Dict[str, np.ndarray], 
                           output_path: str):
        """
        保存装配体向量表示
        
        Args:
            representations: 装配体向量表示字典
            output_path: 输出文件路径
        """
        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        try:
            with open(output_path, 'wb') as f:
                pickle.dump(representations, f)
            
            logger.info(f"装配体向量表示已保存到: {output_path}")
            logger.info(f"包含 {len(representations)} 个装配体")
            
            # 打印统计信息
            if representations:
                vectors = list(representations.values())
                vector_array = np.array(vectors)
                
                logger.info(f"向量维度: {vector_array.shape[1]}")
                logger.info(f"向量范围: [{vector_array.min():.4f}, {vector_array.max():.4f}]")
                logger.info(f"向量均值: {vector_array.mean():.4f}")
                logger.info(f"向量标准差: {vector_array.std():.4f}")
                
        except Exception as e:
            logger.error(f"保存装配体向量表示失败: {e}")
            raise
    
    def load_representations(self, input_path: str) -> Dict[str, np.ndarray]:
        """
        加载装配体向量表示
        
        Args:
            input_path: 输入文件路径
            
        Returns:
            Dict[str, np.ndarray]: 装配体向量表示字典
        """
        try:
            with open(input_path, 'rb') as f:
                representations = pickle.load(f)
            
            logger.info(f"从 {input_path} 加载了 {len(representations)} 个装配体向量表示")
            
            if representations:
                vectors = list(representations.values())
                vector_array = np.array(vectors)
                logger.info(f"向量维度: {vector_array.shape[1]}")
                
            self.assembly_representations = representations
            return representations
            
        except Exception as e:
            logger.error(f"加载装配体向量表示失败: {e}")
            return {}
    
    def analyze_representations(self, representations: Optional[Dict[str, np.ndarray]] = None):
        """
        分析装配体向量表示
        
        Args:
            representations: 装配体向量表示字典，如果为None则使用内部存储的
        """
        if representations is None:
            representations = self.assembly_representations
        
        if not representations:
            logger.warning("没有可分析的向量表示")
            return
        
        logger.info("=== 装配体向量表示分析 ===")
        
        # 基本统计
        assembly_ids = list(representations.keys())
        vectors = list(representations.values())
        vector_array = np.array(vectors)
        
        logger.info(f"装配体数量: {len(assembly_ids)}")
        logger.info(f"向量维度: {vector_array.shape[1]}")
        logger.info(f"向量范围: [{vector_array.min():.4f}, {vector_array.max():.4f}]")
        logger.info(f"向量均值: {vector_array.mean():.4f}")
        logger.info(f"向量标准差: {vector_array.std():.4f}")
        
        # 零向量统计
        zero_vectors = np.sum(np.all(vector_array == 0, axis=1))
        logger.info(f"零向量数量: {zero_vectors} ({zero_vectors/len(vectors)*100:.1f}%)")
        
        # 向量相似性分析
        if len(vectors) > 1:
            # 计算余弦相似度矩阵的统计信息
            from sklearn.metrics.pairwise import cosine_similarity
            similarity_matrix = cosine_similarity(vector_array)
            
            # 排除对角线元素
            mask = np.ones_like(similarity_matrix, dtype=bool)
            np.fill_diagonal(mask, False)
            similarities = similarity_matrix[mask]
            
            logger.info(f"向量相似度统计:")
            logger.info(f"  平均相似度: {similarities.mean():.4f}")
            logger.info(f"  相似度标准差: {similarities.std():.4f}")
            logger.info(f"  最大相似度: {similarities.max():.4f}")
            logger.info(f"  最小相似度: {similarities.min():.4f}")
        
        # 显示前几个装配体ID
        logger.info(f"前10个装配体ID: {assembly_ids[:10]}")
    
    def find_similar_assemblies(self, target_assembly_id: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """
        找到与目标装配体最相似的装配体
        
        Args:
            target_assembly_id: 目标装配体ID
            top_k: 返回最相似的k个装配体
            
        Returns:
            List[Tuple[str, float]]: [(assembly_id, similarity_score), ...]
        """
        if not self.assembly_representations:
            logger.error("没有加载装配体向量表示")
            return []
        
        if target_assembly_id not in self.assembly_representations:
            logger.error(f"目标装配体 {target_assembly_id} 不存在")
            return []
        
        target_vector = self.assembly_representations[target_assembly_id]
        similarities = []
        
        for assembly_id, vector in self.assembly_representations.items():
            if assembly_id != target_assembly_id:
                # 计算余弦相似度
                similarity = np.dot(target_vector, vector) / (np.linalg.norm(target_vector) * np.linalg.norm(vector))
                similarities.append((assembly_id, similarity))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='提取装配体向量表示')
    parser.add_argument('--base-dir', default='datasets/fusion360_assembly',
                       help='装配体文件基础目录')
    parser.add_argument('--output-path', default='dataset/assembly_representations.pkl',
                       help='输出文件路径')
    parser.add_argument('--analyze', action='store_true',
                       help='分析提取的向量表示')
    parser.add_argument('--load-existing',
                       help='加载已存在的向量表示文件进行分析')
    parser.add_argument('--find-similar',
                       help='找到与指定装配体最相似的装配体')

    args = parser.parse_args()

    # 创建提取器
    extractor = AssemblyVectorExtractor(args.base_dir)

    try:
        if args.load_existing:
            # 加载已存在的文件
            representations = extractor.load_representations(args.load_existing)
        else:
            # 提取向量表示
            representations = extractor.extract_representations()

            if representations:
                # 保存结果
                extractor.save_representations(representations, args.output_path)
            else:
                logger.warning("未提取到任何装配体向量表示")
                return
        
        # 分析向量表示
        if args.analyze:
            extractor.analyze_representations(representations)
        
        # 查找相似装配体
        if args.find_similar:
            similar_assemblies = extractor.find_similar_assemblies(args.find_similar)
            if similar_assemblies:
                logger.info(f"与装配体 {args.find_similar} 最相似的装配体:")
                for i, (assembly_id, similarity) in enumerate(similar_assemblies, 1):
                    logger.info(f"  {i}. {assembly_id}: {similarity:.4f}")
            else:
                logger.warning(f"未找到与 {args.find_similar} 相似的装配体")
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
