2025/07/07-06:10:52.270413 42 RocksDB version: 6.29.5
2025/07/07-06:10:52.271163 42 Git sha 0
2025/07/07-06:10:52.271167 42 Compile date 2024-11-15 11:22:58
2025/07/07-06:10:52.271172 42 DB SUMMARY
2025/07/07-06:10:52.271172 42 DB Session ID:  4FUN369NNPH8ZW9K11KA
2025/07/07-06:10:52.314886 42 CURRENT file:  CURRENT
2025/07/07-06:10:52.314894 42 IDENTITY file:  IDENTITY
2025/07/07-06:10:52.315487 42 MANIFEST file:  MANIFEST-000032 size: 911 Bytes
2025/07/07-06:10:52.315489 42 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000036.sst 
2025/07/07-06:10:52.315493 42 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000033.log size: 38035153 ; 
2025/07/07-06:10:52.315495 42                         Options.error_if_exists: 0
2025/07/07-06:10:52.315496 42                       Options.create_if_missing: 1
2025/07/07-06:10:52.315497 42                         Options.paranoid_checks: 1
2025/07/07-06:10:52.315497 42             Options.flush_verify_memtable_count: 1
2025/07/07-06:10:52.315498 42                               Options.track_and_verify_wals_in_manifest: 0
2025/07/07-06:10:52.315499 42                                     Options.env: 0x7fe38a56cd00
2025/07/07-06:10:52.315500 42                                      Options.fs: PosixFileSystem
2025/07/07-06:10:52.315500 42                                Options.info_log: 0x7fe28ba90050
2025/07/07-06:10:52.315501 42                Options.max_file_opening_threads: 16
2025/07/07-06:10:52.315502 42                              Options.statistics: (nil)
2025/07/07-06:10:52.315502 42                               Options.use_fsync: 0
2025/07/07-06:10:52.315503 42                       Options.max_log_file_size: 0
2025/07/07-06:10:52.315503 42                  Options.max_manifest_file_size: 1073741824
2025/07/07-06:10:52.315504 42                   Options.log_file_time_to_roll: 0
2025/07/07-06:10:52.315505 42                       Options.keep_log_file_num: 1000
2025/07/07-06:10:52.315505 42                    Options.recycle_log_file_num: 0
2025/07/07-06:10:52.315506 42                         Options.allow_fallocate: 1
2025/07/07-06:10:52.315506 42                        Options.allow_mmap_reads: 0
2025/07/07-06:10:52.315507 42                       Options.allow_mmap_writes: 0
2025/07/07-06:10:52.315507 42                        Options.use_direct_reads: 0
2025/07/07-06:10:52.315508 42                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/07-06:10:52.315509 42          Options.create_missing_column_families: 0
2025/07/07-06:10:52.315509 42                              Options.db_log_dir: 
2025/07/07-06:10:52.315510 42                                 Options.wal_dir: 
2025/07/07-06:10:52.315510 42                Options.table_cache_numshardbits: 6
2025/07/07-06:10:52.315511 42                         Options.WAL_ttl_seconds: 0
2025/07/07-06:10:52.315511 42                       Options.WAL_size_limit_MB: 0
2025/07/07-06:10:52.315512 42                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/07-06:10:52.315513 42             Options.manifest_preallocation_size: 4194304
2025/07/07-06:10:52.315513 42                     Options.is_fd_close_on_exec: 1
2025/07/07-06:10:52.315514 42                   Options.advise_random_on_open: 1
2025/07/07-06:10:52.315514 42                   Options.experimental_mempurge_threshold: 0.000000
2025/07/07-06:10:52.315803 42                    Options.db_write_buffer_size: 0
2025/07/07-06:10:52.315804 42                    Options.write_buffer_manager: 0x7fe28e4400a0
2025/07/07-06:10:52.315805 42         Options.access_hint_on_compaction_start: 1
2025/07/07-06:10:52.315806 42  Options.new_table_reader_for_compaction_inputs: 0
2025/07/07-06:10:52.315806 42           Options.random_access_max_buffer_size: 1048576
2025/07/07-06:10:52.315807 42                      Options.use_adaptive_mutex: 0
2025/07/07-06:10:52.315808 42                            Options.rate_limiter: (nil)
2025/07/07-06:10:52.315817 42     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/07-06:10:52.315818 42                       Options.wal_recovery_mode: 2
2025/07/07-06:10:52.328755 42                  Options.enable_thread_tracking: 0
2025/07/07-06:10:52.328762 42                  Options.enable_pipelined_write: 0
2025/07/07-06:10:52.328763 42                  Options.unordered_write: 0
2025/07/07-06:10:52.328764 42         Options.allow_concurrent_memtable_write: 1
2025/07/07-06:10:52.328764 42      Options.enable_write_thread_adaptive_yield: 1
2025/07/07-06:10:52.328765 42             Options.write_thread_max_yield_usec: 100
2025/07/07-06:10:52.328765 42            Options.write_thread_slow_yield_usec: 3
2025/07/07-06:10:52.328766 42                               Options.row_cache: None
2025/07/07-06:10:52.328766 42                              Options.wal_filter: None
2025/07/07-06:10:52.328767 42             Options.avoid_flush_during_recovery: 0
2025/07/07-06:10:52.328767 42             Options.allow_ingest_behind: 0
2025/07/07-06:10:52.328768 42             Options.preserve_deletes: 0
2025/07/07-06:10:52.328768 42             Options.two_write_queues: 0
2025/07/07-06:10:52.328769 42             Options.manual_wal_flush: 0
2025/07/07-06:10:52.328769 42             Options.atomic_flush: 0
2025/07/07-06:10:52.328770 42             Options.avoid_unnecessary_blocking_io: 0
2025/07/07-06:10:52.328770 42                 Options.persist_stats_to_disk: 0
2025/07/07-06:10:52.328771 42                 Options.write_dbid_to_manifest: 0
2025/07/07-06:10:52.328771 42                 Options.log_readahead_size: 0
2025/07/07-06:10:52.328772 42                 Options.file_checksum_gen_factory: Unknown
2025/07/07-06:10:52.328772 42                 Options.best_efforts_recovery: 0
2025/07/07-06:10:52.328773 42                Options.max_bgerror_resume_count: 2147483647
2025/07/07-06:10:52.328773 42            Options.bgerror_resume_retry_interval: 1000000
2025/07/07-06:10:52.328774 42             Options.allow_data_in_errors: 0
2025/07/07-06:10:52.328774 42             Options.db_host_id: __hostname__
2025/07/07-06:10:52.328778 42             Options.max_background_jobs: 2
2025/07/07-06:10:52.328778 42             Options.max_background_compactions: -1
2025/07/07-06:10:52.328779 42             Options.max_subcompactions: 1
2025/07/07-06:10:52.328779 42             Options.avoid_flush_during_shutdown: 0
2025/07/07-06:10:52.328780 42           Options.writable_file_max_buffer_size: 1048576
2025/07/07-06:10:52.328780 42             Options.delayed_write_rate : 16777216
2025/07/07-06:10:52.328781 42             Options.max_total_wal_size: 0
2025/07/07-06:10:52.328781 42             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/07-06:10:52.328782 42                   Options.stats_dump_period_sec: 600
2025/07/07-06:10:52.328782 42                 Options.stats_persist_period_sec: 600
2025/07/07-06:10:52.328783 42                 Options.stats_history_buffer_size: 1048576
2025/07/07-06:10:52.328783 42                          Options.max_open_files: -1
2025/07/07-06:10:52.328784 42                          Options.bytes_per_sync: 0
2025/07/07-06:10:52.328784 42                      Options.wal_bytes_per_sync: 0
2025/07/07-06:10:52.328785 42                   Options.strict_bytes_per_sync: 0
2025/07/07-06:10:52.328785 42       Options.compaction_readahead_size: 0
2025/07/07-06:10:52.328786 42                  Options.max_background_flushes: 1
2025/07/07-06:10:52.328786 42 Compression algorithms supported:
2025/07/07-06:10:52.328788 42 	kZSTD supported: 1
2025/07/07-06:10:52.328789 42 	kXpressCompression supported: 0
2025/07/07-06:10:52.328789 42 	kBZip2Compression supported: 0
2025/07/07-06:10:52.328790 42 	kZSTDNotFinalCompression supported: 1
2025/07/07-06:10:52.328790 42 	kLZ4Compression supported: 0
2025/07/07-06:10:52.328791 42 	kZlibCompression supported: 0
2025/07/07-06:10:52.328792 42 	kLZ4HCCompression supported: 0
2025/07/07-06:10:52.328792 42 	kSnappyCompression supported: 0
2025/07/07-06:10:52.328797 42 Fast CRC32 supported: Not supported on x86
2025/07/07-06:10:52.358129 42 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000032
2025/07/07-06:10:52.380462 42 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/07-06:10:52.380474 42               Options.comparator: leveldb.BytewiseComparator
2025/07/07-06:10:52.380476 42           Options.merge_operator: None
2025/07/07-06:10:52.380477 42        Options.compaction_filter: None
2025/07/07-06:10:52.380478 42        Options.compaction_filter_factory: None
2025/07/07-06:10:52.380478 42  Options.sst_partitioner_factory: None
2025/07/07-06:10:52.380480 42         Options.memtable_factory: SkipListFactory
2025/07/07-06:10:52.380481 42            Options.table_factory: BlockBasedTable
2025/07/07-06:10:52.380518 42            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fe28e5000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fe28e440010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/07-06:10:52.380523 42        Options.write_buffer_size: 67108864
2025/07/07-06:10:52.380524 42  Options.max_write_buffer_number: 2
2025/07/07-06:10:52.380527 42        Options.compression[0]: NoCompression
2025/07/07-06:10:52.380528 42        Options.compression[1]: NoCompression
2025/07/07-06:10:52.380529 42        Options.compression[2]: ZSTD
2025/07/07-06:10:52.380530 42        Options.compression[3]: ZSTD
2025/07/07-06:10:52.380531 42        Options.compression[4]: ZSTD
2025/07/07-06:10:52.380532 42                  Options.bottommost_compression: Disabled
2025/07/07-06:10:52.380533 42       Options.prefix_extractor: nullptr
2025/07/07-06:10:52.380533 42   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/07-06:10:52.380534 42             Options.num_levels: 5
2025/07/07-06:10:52.380535 42        Options.min_write_buffer_number_to_merge: 1
2025/07/07-06:10:52.380536 42     Options.max_write_buffer_number_to_maintain: 0
2025/07/07-06:10:52.380537 42     Options.max_write_buffer_size_to_maintain: 0
2025/07/07-06:10:52.380538 42            Options.bottommost_compression_opts.window_bits: -14
2025/07/07-06:10:52.380538 42                  Options.bottommost_compression_opts.level: 32767
2025/07/07-06:10:52.380539 42               Options.bottommost_compression_opts.strategy: 0
2025/07/07-06:10:52.380540 42         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/07-06:10:52.380541 42         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/07-06:10:52.380542 42         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/07-06:10:52.380543 42                  Options.bottommost_compression_opts.enabled: false
2025/07/07-06:10:52.380544 42         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/07-06:10:52.380545 42            Options.compression_opts.window_bits: -14
2025/07/07-06:10:52.380545 42                  Options.compression_opts.level: 32767
2025/07/07-06:10:52.380546 42               Options.compression_opts.strategy: 0
2025/07/07-06:10:52.380547 42         Options.compression_opts.max_dict_bytes: 0
2025/07/07-06:10:52.380548 42         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/07-06:10:52.380549 42         Options.compression_opts.parallel_threads: 1
2025/07/07-06:10:52.380741 42                  Options.compression_opts.enabled: false
2025/07/07-06:10:52.380748 42         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/07-06:10:52.380749 42      Options.level0_file_num_compaction_trigger: 4
2025/07/07-06:10:52.380750 42          Options.level0_slowdown_writes_trigger: 20
2025/07/07-06:10:52.380751 42              Options.level0_stop_writes_trigger: 36
2025/07/07-06:10:52.380752 42                   Options.target_file_size_base: 67108864
2025/07/07-06:10:52.380753 42             Options.target_file_size_multiplier: 2
2025/07/07-06:10:52.380754 42                Options.max_bytes_for_level_base: 268435456
2025/07/07-06:10:52.380755 42 Options.level_compaction_dynamic_level_bytes: 0
2025/07/07-06:10:52.380756 42          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/07-06:10:52.380759 42 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/07-06:10:52.380761 42 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/07-06:10:52.380762 42 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/07-06:10:52.380763 42 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/07-06:10:52.380763 42 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/07-06:10:52.380764 42 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/07-06:10:52.380765 42 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/07-06:10:52.380766 42       Options.max_sequential_skip_in_iterations: 8
2025/07/07-06:10:52.380767 42                    Options.max_compaction_bytes: 1677721600
2025/07/07-06:10:52.380768 42                        Options.arena_block_size: 1048576
2025/07/07-06:10:52.380769 42   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/07-06:10:52.380770 42   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/07-06:10:52.380770 42       Options.rate_limit_delay_max_milliseconds: 100
2025/07/07-06:10:52.380771 42                Options.disable_auto_compactions: 0
2025/07/07-06:10:52.380775 42                        Options.compaction_style: kCompactionStyleLevel
2025/07/07-06:10:52.380776 42                          Options.compaction_pri: kMinOverlappingRatio
2025/07/07-06:10:52.380777 42 Options.compaction_options_universal.size_ratio: 1
2025/07/07-06:10:52.380778 42 Options.compaction_options_universal.min_merge_width: 2
2025/07/07-06:10:52.380779 42 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/07-06:10:52.380780 42 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/07-06:10:52.380781 42 Options.compaction_options_universal.compression_size_percent: -1
2025/07/07-06:10:52.380782 42 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/07-06:10:52.380783 42 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/07-06:10:52.380796 42 Options.compaction_options_fifo.allow_compaction: 0
2025/07/07-06:10:52.380802 42                   Options.table_properties_collectors: 
2025/07/07-06:10:52.380803 42                   Options.inplace_update_support: 0
2025/07/07-06:10:52.380804 42                 Options.inplace_update_num_locks: 10000
2025/07/07-06:10:52.380806 42               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/07-06:10:52.380807 42               Options.memtable_whole_key_filtering: 0
2025/07/07-06:10:52.380808 42   Options.memtable_huge_page_size: 0
2025/07/07-06:10:52.380809 42                           Options.bloom_locality: 0
2025/07/07-06:10:52.380810 42                    Options.max_successive_merges: 0
2025/07/07-06:10:52.380811 42                Options.optimize_filters_for_hits: 0
2025/07/07-06:10:52.380812 42                Options.paranoid_file_checks: 0
2025/07/07-06:10:52.380813 42                Options.force_consistency_checks: 1
2025/07/07-06:10:52.380814 42                Options.report_bg_io_stats: 0
2025/07/07-06:10:52.380815 42                               Options.ttl: 2592000
2025/07/07-06:10:52.380815 42          Options.periodic_compaction_seconds: 0
2025/07/07-06:10:52.381019 42                       Options.enable_blob_files: false
2025/07/07-06:10:52.381023 42                           Options.min_blob_size: 0
2025/07/07-06:10:52.381024 42                          Options.blob_file_size: 268435456
2025/07/07-06:10:52.381025 42                   Options.blob_compression_type: NoCompression
2025/07/07-06:10:52.381026 42          Options.enable_blob_garbage_collection: false
2025/07/07-06:10:52.381027 42      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/07-06:10:52.381030 42 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/07-06:10:52.381031 42          Options.blob_compaction_readahead_size: 0
2025/07/07-06:10:52.477126 42 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000032 succeeded,manifest_file_number is 32, next_file_number is 38, last_sequence is 2111466, log_number is 28,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 8
2025/07/07-06:10:52.477134 42 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 28
2025/07/07-06:10:52.505574 42 [db/version_set.cc:4409] Creating manifest 39
2025/07/07-06:10:52.718578 42 EVENT_LOG_v1 {"time_micros": 1751868652718568, "job": 1, "event": "recovery_started", "wal_files": [33]}
2025/07/07-06:10:52.718583 42 [db/db_impl/db_impl_open.cc:888] Recovering log #33 mode 2
2025/07/07-06:10:53.742449 42 EVENT_LOG_v1 {"time_micros": 1751868653742419, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 40, "file_size": 1228, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 258, "index_size": 53, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 345, "raw_average_key_size": 49, "raw_value_size": 60, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 7, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1751868653, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "70bc45a2-e82b-4473-bc30-6ae251c53e8f", "db_session_id": "4FUN369NNPH8ZW9K11KA", "orig_file_number": 40}}
2025/07/07-06:10:53.742817 42 [db/version_set.cc:4409] Creating manifest 41
2025/07/07-06:10:53.875132 42 EVENT_LOG_v1 {"time_micros": 1751868653875126, "job": 1, "event": "recovery_finished"}
2025/07/07-06:10:53.984418 42 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000033.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/07-06:10:53.986733 42 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fe28bb90000
2025/07/07-06:10:53.989137 42 DB pointer 0x7fe28ba20000
2025/07/07-06:10:53.989755 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/07-06:10:53.989766 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1.6 total, 1.6 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.20 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.085       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0    3.63 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.085       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.08              0.00         1    0.085       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.08              0.00         1    0.085       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1.6 total, 1.6 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fe28e440010#8 capacity: 951.98 MB collections: 1 last_copies: 0 last_secs: 4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
