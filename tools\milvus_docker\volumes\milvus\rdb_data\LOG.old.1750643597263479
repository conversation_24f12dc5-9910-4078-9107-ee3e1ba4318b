2025/06/20-08:13:58.455148 37 RocksDB version: 6.29.5
2025/06/20-08:13:58.455424 37 Git sha 0
2025/06/20-08:13:58.455427 37 Compile date 2024-11-15 11:22:58
2025/06/20-08:13:58.455428 37 DB SUMMARY
2025/06/20-08:13:58.455429 37 DB Session ID:  01XBVBB5Q8PHIN08ZKZR
2025/06/20-08:13:58.456237 37 CURRENT file:  CURRENT
2025/06/20-08:13:58.456239 37 IDENTITY file:  IDENTITY
2025/06/20-08:13:58.456488 37 MANIFEST file:  MANIFEST-000004 size: 596 Bytes
2025/06/20-08:13:58.456490 37 SST files in /var/lib/milvus/rdb_data dir, Total Num: 1, files: 000014.sst 
2025/06/20-08:13:58.456491 37 Write Ahead Log file in /var/lib/milvus/rdb_data: 000012.log size: 19045644 ; 
2025/06/20-08:13:58.456492 37                         Options.error_if_exists: 0
2025/06/20-08:13:58.456493 37                       Options.create_if_missing: 1
2025/06/20-08:13:58.456493 37                         Options.paranoid_checks: 1
2025/06/20-08:13:58.456494 37             Options.flush_verify_memtable_count: 1
2025/06/20-08:13:58.456494 37                               Options.track_and_verify_wals_in_manifest: 0
2025/06/20-08:13:58.456495 37                                     Options.env: 0x7fbc8ea59d00
2025/06/20-08:13:58.456496 37                                      Options.fs: PosixFileSystem
2025/06/20-08:13:58.456496 37                                Options.info_log: 0x7fbb91890140
2025/06/20-08:13:58.456497 37                Options.max_file_opening_threads: 16
2025/06/20-08:13:58.456497 37                              Options.statistics: (nil)
2025/06/20-08:13:58.456498 37                               Options.use_fsync: 0
2025/06/20-08:13:58.456498 37                       Options.max_log_file_size: 0
2025/06/20-08:13:58.456499 37                  Options.max_manifest_file_size: 1073741824
2025/06/20-08:13:58.456499 37                   Options.log_file_time_to_roll: 0
2025/06/20-08:13:58.456500 37                       Options.keep_log_file_num: 1000
2025/06/20-08:13:58.456500 37                    Options.recycle_log_file_num: 0
2025/06/20-08:13:58.456501 37                         Options.allow_fallocate: 1
2025/06/20-08:13:58.456501 37                        Options.allow_mmap_reads: 0
2025/06/20-08:13:58.456502 37                       Options.allow_mmap_writes: 0
2025/06/20-08:13:58.456502 37                        Options.use_direct_reads: 0
2025/06/20-08:13:58.456503 37                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/20-08:13:58.456503 37          Options.create_missing_column_families: 1
2025/06/20-08:13:58.456504 37                              Options.db_log_dir: 
2025/06/20-08:13:58.456504 37                                 Options.wal_dir: 
2025/06/20-08:13:58.456505 37                Options.table_cache_numshardbits: 6
2025/06/20-08:13:58.456505 37                         Options.WAL_ttl_seconds: 0
2025/06/20-08:13:58.456505 37                       Options.WAL_size_limit_MB: 0
2025/06/20-08:13:58.456506 37                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/20-08:13:58.456506 37             Options.manifest_preallocation_size: 4194304
2025/06/20-08:13:58.456507 37                     Options.is_fd_close_on_exec: 1
2025/06/20-08:13:58.456507 37                   Options.advise_random_on_open: 1
2025/06/20-08:13:58.456508 37                   Options.experimental_mempurge_threshold: 0.000000
2025/06/20-08:13:58.456510 37                    Options.db_write_buffer_size: 0
2025/06/20-08:13:58.456510 37                    Options.write_buffer_manager: 0x7fbb98060280
2025/06/20-08:13:58.456511 37         Options.access_hint_on_compaction_start: 1
2025/06/20-08:13:58.456511 37  Options.new_table_reader_for_compaction_inputs: 0
2025/06/20-08:13:58.456512 37           Options.random_access_max_buffer_size: 1048576
2025/06/20-08:13:58.456512 37                      Options.use_adaptive_mutex: 0
2025/06/20-08:13:58.456512 37                            Options.rate_limiter: (nil)
2025/06/20-08:13:58.456513 37     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/20-08:13:58.456514 37                       Options.wal_recovery_mode: 2
2025/06/20-08:13:58.456981 37                  Options.enable_thread_tracking: 0
2025/06/20-08:13:58.456982 37                  Options.enable_pipelined_write: 0
2025/06/20-08:13:58.456983 37                  Options.unordered_write: 0
2025/06/20-08:13:58.456984 37         Options.allow_concurrent_memtable_write: 1
2025/06/20-08:13:58.456985 37      Options.enable_write_thread_adaptive_yield: 1
2025/06/20-08:13:58.456985 37             Options.write_thread_max_yield_usec: 100
2025/06/20-08:13:58.456986 37            Options.write_thread_slow_yield_usec: 3
2025/06/20-08:13:58.456986 37                               Options.row_cache: None
2025/06/20-08:13:58.456986 37                              Options.wal_filter: None
2025/06/20-08:13:58.456987 37             Options.avoid_flush_during_recovery: 0
2025/06/20-08:13:58.456988 37             Options.allow_ingest_behind: 0
2025/06/20-08:13:58.456988 37             Options.preserve_deletes: 0
2025/06/20-08:13:58.456988 37             Options.two_write_queues: 0
2025/06/20-08:13:58.456989 37             Options.manual_wal_flush: 0
2025/06/20-08:13:58.456989 37             Options.atomic_flush: 0
2025/06/20-08:13:58.456990 37             Options.avoid_unnecessary_blocking_io: 0
2025/06/20-08:13:58.456990 37                 Options.persist_stats_to_disk: 0
2025/06/20-08:13:58.456991 37                 Options.write_dbid_to_manifest: 0
2025/06/20-08:13:58.456991 37                 Options.log_readahead_size: 0
2025/06/20-08:13:58.456992 37                 Options.file_checksum_gen_factory: Unknown
2025/06/20-08:13:58.456992 37                 Options.best_efforts_recovery: 0
2025/06/20-08:13:58.456992 37                Options.max_bgerror_resume_count: 2147483647
2025/06/20-08:13:58.456993 37            Options.bgerror_resume_retry_interval: 1000000
2025/06/20-08:13:58.456993 37             Options.allow_data_in_errors: 0
2025/06/20-08:13:58.456994 37             Options.db_host_id: __hostname__
2025/06/20-08:13:58.456994 37             Options.max_background_jobs: 2
2025/06/20-08:13:58.456995 37             Options.max_background_compactions: -1
2025/06/20-08:13:58.456995 37             Options.max_subcompactions: 1
2025/06/20-08:13:58.456996 37             Options.avoid_flush_during_shutdown: 0
2025/06/20-08:13:58.456996 37           Options.writable_file_max_buffer_size: 1048576
2025/06/20-08:13:58.456997 37             Options.delayed_write_rate : 16777216
2025/06/20-08:13:58.456997 37             Options.max_total_wal_size: 0
2025/06/20-08:13:58.456998 37             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/20-08:13:58.456998 37                   Options.stats_dump_period_sec: 600
2025/06/20-08:13:58.456999 37                 Options.stats_persist_period_sec: 600
2025/06/20-08:13:58.456999 37                 Options.stats_history_buffer_size: 1048576
2025/06/20-08:13:58.457000 37                          Options.max_open_files: -1
2025/06/20-08:13:58.457000 37                          Options.bytes_per_sync: 0
2025/06/20-08:13:58.457001 37                      Options.wal_bytes_per_sync: 0
2025/06/20-08:13:58.457001 37                   Options.strict_bytes_per_sync: 0
2025/06/20-08:13:58.457001 37       Options.compaction_readahead_size: 0
2025/06/20-08:13:58.457002 37                  Options.max_background_flushes: 1
2025/06/20-08:13:58.457002 37 Compression algorithms supported:
2025/06/20-08:13:58.457003 37 	kZSTD supported: 1
2025/06/20-08:13:58.457004 37 	kXpressCompression supported: 0
2025/06/20-08:13:58.457005 37 	kBZip2Compression supported: 0
2025/06/20-08:13:58.457005 37 	kZSTDNotFinalCompression supported: 1
2025/06/20-08:13:58.457006 37 	kLZ4Compression supported: 0
2025/06/20-08:13:58.457006 37 	kZlibCompression supported: 0
2025/06/20-08:13:58.457007 37 	kLZ4HCCompression supported: 0
2025/06/20-08:13:58.457007 37 	kSnappyCompression supported: 0
2025/06/20-08:13:58.457008 37 Fast CRC32 supported: Not supported on x86
2025/06/20-08:13:58.460006 37 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000004
2025/06/20-08:13:58.464474 37 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/06/20-08:13:58.464479 37               Options.comparator: leveldb.BytewiseComparator
2025/06/20-08:13:58.464480 37           Options.merge_operator: None
2025/06/20-08:13:58.464480 37        Options.compaction_filter: None
2025/06/20-08:13:58.464481 37        Options.compaction_filter_factory: None
2025/06/20-08:13:58.464481 37  Options.sst_partitioner_factory: None
2025/06/20-08:13:58.464482 37         Options.memtable_factory: SkipListFactory
2025/06/20-08:13:58.464482 37            Options.table_factory: BlockBasedTable
2025/06/20-08:13:58.464493 37            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fbb98001320)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fbb98060010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/20-08:13:58.464493 37        Options.write_buffer_size: 67108864
2025/06/20-08:13:58.464494 37  Options.max_write_buffer_number: 2
2025/06/20-08:13:58.464495 37        Options.compression[0]: NoCompression
2025/06/20-08:13:58.464495 37        Options.compression[1]: NoCompression
2025/06/20-08:13:58.464496 37        Options.compression[2]: ZSTD
2025/06/20-08:13:58.464496 37        Options.compression[3]: ZSTD
2025/06/20-08:13:58.464497 37        Options.compression[4]: ZSTD
2025/06/20-08:13:58.464497 37                  Options.bottommost_compression: Disabled
2025/06/20-08:13:58.464498 37       Options.prefix_extractor: nullptr
2025/06/20-08:13:58.464498 37   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/20-08:13:58.464499 37             Options.num_levels: 5
2025/06/20-08:13:58.464499 37        Options.min_write_buffer_number_to_merge: 1
2025/06/20-08:13:58.464500 37     Options.max_write_buffer_number_to_maintain: 0
2025/06/20-08:13:58.464500 37     Options.max_write_buffer_size_to_maintain: 0
2025/06/20-08:13:58.464501 37            Options.bottommost_compression_opts.window_bits: -14
2025/06/20-08:13:58.464501 37                  Options.bottommost_compression_opts.level: 32767
2025/06/20-08:13:58.464502 37               Options.bottommost_compression_opts.strategy: 0
2025/06/20-08:13:58.464502 37         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/20-08:13:58.464503 37         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/20-08:13:58.464503 37         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/20-08:13:58.464504 37                  Options.bottommost_compression_opts.enabled: false
2025/06/20-08:13:58.464504 37         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/20-08:13:58.464505 37            Options.compression_opts.window_bits: -14
2025/06/20-08:13:58.464505 37                  Options.compression_opts.level: 32767
2025/06/20-08:13:58.464506 37               Options.compression_opts.strategy: 0
2025/06/20-08:13:58.464506 37         Options.compression_opts.max_dict_bytes: 0
2025/06/20-08:13:58.464506 37         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/20-08:13:58.464507 37         Options.compression_opts.parallel_threads: 1
2025/06/20-08:13:58.464596 37                  Options.compression_opts.enabled: false
2025/06/20-08:13:58.464597 37         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/20-08:13:58.464598 37      Options.level0_file_num_compaction_trigger: 4
2025/06/20-08:13:58.464598 37          Options.level0_slowdown_writes_trigger: 20
2025/06/20-08:13:58.464599 37              Options.level0_stop_writes_trigger: 36
2025/06/20-08:13:58.464599 37                   Options.target_file_size_base: 67108864
2025/06/20-08:13:58.464600 37             Options.target_file_size_multiplier: 2
2025/06/20-08:13:58.464600 37                Options.max_bytes_for_level_base: 268435456
2025/06/20-08:13:58.464601 37 Options.level_compaction_dynamic_level_bytes: 0
2025/06/20-08:13:58.464601 37          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/20-08:13:58.464603 37 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/20-08:13:58.464603 37 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/20-08:13:58.464604 37 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/20-08:13:58.464604 37 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/20-08:13:58.464605 37 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/20-08:13:58.464605 37 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/20-08:13:58.464606 37 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/20-08:13:58.464606 37       Options.max_sequential_skip_in_iterations: 8
2025/06/20-08:13:58.464607 37                    Options.max_compaction_bytes: 1677721600
2025/06/20-08:13:58.464607 37                        Options.arena_block_size: 1048576
2025/06/20-08:13:58.464608 37   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/20-08:13:58.464608 37   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/20-08:13:58.464609 37       Options.rate_limit_delay_max_milliseconds: 100
2025/06/20-08:13:58.464609 37                Options.disable_auto_compactions: 0
2025/06/20-08:13:58.464610 37                        Options.compaction_style: kCompactionStyleLevel
2025/06/20-08:13:58.464611 37                          Options.compaction_pri: kMinOverlappingRatio
2025/06/20-08:13:58.464612 37 Options.compaction_options_universal.size_ratio: 1
2025/06/20-08:13:58.464612 37 Options.compaction_options_universal.min_merge_width: 2
2025/06/20-08:13:58.464613 37 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/20-08:13:58.464613 37 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/20-08:13:58.464614 37 Options.compaction_options_universal.compression_size_percent: -1
2025/06/20-08:13:58.464614 37 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/20-08:13:58.464615 37 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/20-08:13:58.464615 37 Options.compaction_options_fifo.allow_compaction: 0
2025/06/20-08:13:58.464618 37                   Options.table_properties_collectors: 
2025/06/20-08:13:58.464619 37                   Options.inplace_update_support: 0
2025/06/20-08:13:58.464619 37                 Options.inplace_update_num_locks: 10000
2025/06/20-08:13:58.464620 37               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/20-08:13:58.464621 37               Options.memtable_whole_key_filtering: 0
2025/06/20-08:13:58.464621 37   Options.memtable_huge_page_size: 0
2025/06/20-08:13:58.464622 37                           Options.bloom_locality: 0
2025/06/20-08:13:58.464622 37                    Options.max_successive_merges: 0
2025/06/20-08:13:58.464623 37                Options.optimize_filters_for_hits: 0
2025/06/20-08:13:58.464623 37                Options.paranoid_file_checks: 0
2025/06/20-08:13:58.464623 37                Options.force_consistency_checks: 1
2025/06/20-08:13:58.464624 37                Options.report_bg_io_stats: 0
2025/06/20-08:13:58.464624 37                               Options.ttl: 2592000
2025/06/20-08:13:58.464625 37          Options.periodic_compaction_seconds: 0
2025/06/20-08:13:58.464882 37                       Options.enable_blob_files: false
2025/06/20-08:13:58.464883 37                           Options.min_blob_size: 0
2025/06/20-08:13:58.464884 37                          Options.blob_file_size: 268435456
2025/06/20-08:13:58.464885 37                   Options.blob_compression_type: NoCompression
2025/06/20-08:13:58.464885 37          Options.enable_blob_garbage_collection: false
2025/06/20-08:13:58.464886 37      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/20-08:13:58.464887 37 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/20-08:13:58.464887 37          Options.blob_compaction_readahead_size: 0
2025/06/20-08:13:58.465224 37 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/06/20-08:13:58.465226 37               Options.comparator: leveldb.BytewiseComparator
2025/06/20-08:13:58.465227 37           Options.merge_operator: None
2025/06/20-08:13:58.465228 37        Options.compaction_filter: None
2025/06/20-08:13:58.465228 37        Options.compaction_filter_factory: None
2025/06/20-08:13:58.465229 37  Options.sst_partitioner_factory: None
2025/06/20-08:13:58.465229 37         Options.memtable_factory: SkipListFactory
2025/06/20-08:13:58.465230 37            Options.table_factory: BlockBasedTable
2025/06/20-08:13:58.465236 37            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fbb98001320)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fbb98060010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/20-08:13:58.465237 37        Options.write_buffer_size: 67108864
2025/06/20-08:13:58.465237 37  Options.max_write_buffer_number: 2
2025/06/20-08:13:58.465238 37        Options.compression[0]: NoCompression
2025/06/20-08:13:58.465239 37        Options.compression[1]: NoCompression
2025/06/20-08:13:58.465239 37        Options.compression[2]: ZSTD
2025/06/20-08:13:58.465240 37        Options.compression[3]: ZSTD
2025/06/20-08:13:58.465240 37        Options.compression[4]: ZSTD
2025/06/20-08:13:58.465241 37                  Options.bottommost_compression: Disabled
2025/06/20-08:13:58.465241 37       Options.prefix_extractor: nullptr
2025/06/20-08:13:58.465242 37   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/20-08:13:58.465242 37             Options.num_levels: 5
2025/06/20-08:13:58.465242 37        Options.min_write_buffer_number_to_merge: 1
2025/06/20-08:13:58.465243 37     Options.max_write_buffer_number_to_maintain: 0
2025/06/20-08:13:58.465243 37     Options.max_write_buffer_size_to_maintain: 0
2025/06/20-08:13:58.465244 37            Options.bottommost_compression_opts.window_bits: -14
2025/06/20-08:13:58.465244 37                  Options.bottommost_compression_opts.level: 32767
2025/06/20-08:13:58.465245 37               Options.bottommost_compression_opts.strategy: 0
2025/06/20-08:13:58.465245 37         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/20-08:13:58.465246 37         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/20-08:13:58.465246 37         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/20-08:13:58.465313 37                  Options.bottommost_compression_opts.enabled: false
2025/06/20-08:13:58.465315 37         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/20-08:13:58.465316 37            Options.compression_opts.window_bits: -14
2025/06/20-08:13:58.465317 37                  Options.compression_opts.level: 32767
2025/06/20-08:13:58.465317 37               Options.compression_opts.strategy: 0
2025/06/20-08:13:58.465318 37         Options.compression_opts.max_dict_bytes: 0
2025/06/20-08:13:58.465318 37         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/20-08:13:58.465319 37         Options.compression_opts.parallel_threads: 1
2025/06/20-08:13:58.465319 37                  Options.compression_opts.enabled: false
2025/06/20-08:13:58.465320 37         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/20-08:13:58.465320 37      Options.level0_file_num_compaction_trigger: 4
2025/06/20-08:13:58.465321 37          Options.level0_slowdown_writes_trigger: 20
2025/06/20-08:13:58.465321 37              Options.level0_stop_writes_trigger: 36
2025/06/20-08:13:58.465322 37                   Options.target_file_size_base: 67108864
2025/06/20-08:13:58.465322 37             Options.target_file_size_multiplier: 2
2025/06/20-08:13:58.465323 37                Options.max_bytes_for_level_base: 268435456
2025/06/20-08:13:58.465323 37 Options.level_compaction_dynamic_level_bytes: 0
2025/06/20-08:13:58.465324 37          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/20-08:13:58.465325 37 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/20-08:13:58.465325 37 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/20-08:13:58.465326 37 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/20-08:13:58.465326 37 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/20-08:13:58.465327 37 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/20-08:13:58.465327 37 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/20-08:13:58.465327 37 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/20-08:13:58.465328 37       Options.max_sequential_skip_in_iterations: 8
2025/06/20-08:13:58.465328 37                    Options.max_compaction_bytes: 1677721600
2025/06/20-08:13:58.465329 37                        Options.arena_block_size: 1048576
2025/06/20-08:13:58.465329 37   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/20-08:13:58.465330 37   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/20-08:13:58.465330 37       Options.rate_limit_delay_max_milliseconds: 100
2025/06/20-08:13:58.465331 37                Options.disable_auto_compactions: 0
2025/06/20-08:13:58.465331 37                        Options.compaction_style: kCompactionStyleLevel
2025/06/20-08:13:58.465332 37                          Options.compaction_pri: kMinOverlappingRatio
2025/06/20-08:13:58.465333 37 Options.compaction_options_universal.size_ratio: 1
2025/06/20-08:13:58.465333 37 Options.compaction_options_universal.min_merge_width: 2
2025/06/20-08:13:58.465333 37 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/20-08:13:58.465334 37 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/20-08:13:58.465334 37 Options.compaction_options_universal.compression_size_percent: -1
2025/06/20-08:13:58.465335 37 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/20-08:13:58.465335 37 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/20-08:13:58.465336 37 Options.compaction_options_fifo.allow_compaction: 0
2025/06/20-08:13:58.465337 37                   Options.table_properties_collectors: 
2025/06/20-08:13:58.465337 37                   Options.inplace_update_support: 0
2025/06/20-08:13:58.465338 37                 Options.inplace_update_num_locks: 10000
2025/06/20-08:13:58.465338 37               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/20-08:13:58.465339 37               Options.memtable_whole_key_filtering: 0
2025/06/20-08:13:58.465408 37   Options.memtable_huge_page_size: 0
2025/06/20-08:13:58.465410 37                           Options.bloom_locality: 0
2025/06/20-08:13:58.465411 37                    Options.max_successive_merges: 0
2025/06/20-08:13:58.465411 37                Options.optimize_filters_for_hits: 0
2025/06/20-08:13:58.465412 37                Options.paranoid_file_checks: 0
2025/06/20-08:13:58.465413 37                Options.force_consistency_checks: 1
2025/06/20-08:13:58.465413 37                Options.report_bg_io_stats: 0
2025/06/20-08:13:58.465414 37                               Options.ttl: 2592000
2025/06/20-08:13:58.465414 37          Options.periodic_compaction_seconds: 0
2025/06/20-08:13:58.465415 37                       Options.enable_blob_files: false
2025/06/20-08:13:58.465415 37                           Options.min_blob_size: 0
2025/06/20-08:13:58.465416 37                          Options.blob_file_size: 268435456
2025/06/20-08:13:58.465416 37                   Options.blob_compression_type: NoCompression
2025/06/20-08:13:58.465417 37          Options.enable_blob_garbage_collection: false
2025/06/20-08:13:58.465418 37      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/20-08:13:58.465418 37 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/20-08:13:58.465419 37          Options.blob_compaction_readahead_size: 0
2025/06/20-08:13:58.539293 37 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 16, last_sequence is 755613, log_number is 12,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 12
2025/06/20-08:13:58.539300 37 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 12
2025/06/20-08:13:58.539301 37 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 5
2025/06/20-08:13:58.559689 37 [db/version_set.cc:4409] Creating manifest 17
2025/06/20-08:13:58.688722 37 EVENT_LOG_v1 {"time_micros": 1750407238688716, "job": 1, "event": "recovery_started", "wal_files": [12]}
2025/06/20-08:13:58.688727 37 [db/db_impl/db_impl_open.cc:888] Recovering log #12 mode 2
2025/06/20-08:13:59.567292 37 EVENT_LOG_v1 {"time_micros": 1750407239567273, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 18, "file_size": 8293809, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 8285985, "index_size": 6895, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 12850977, "raw_average_key_size": 49, "raw_value_size": 4414312, "raw_average_value_size": 17, "num_data_blocks": 127, "num_entries": 259616, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750407239, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9e9850eb-445c-47b0-b67c-28a3f314a128", "db_session_id": "01XBVBB5Q8PHIN08ZKZR", "orig_file_number": 18}}
2025/06/20-08:13:59.567479 37 [db/version_set.cc:4409] Creating manifest 19
2025/06/20-08:13:59.623924 37 EVENT_LOG_v1 {"time_micros": 1750407239623920, "job": 1, "event": "recovery_finished"}
2025/06/20-08:13:59.712535 37 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data/000012.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/20-08:13:59.712666 37 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fbb91990700
2025/06/20-08:13:59.713392 37 DB pointer 0x7fbb91821c00
2025/06/20-08:14:02.713697 63 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/20-08:14:02.713709 63 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4.2 total, 4.2 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    7.91 MB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     25.7      0.31              0.00         1    0.308       0      0       0.0       0.0
  L1      1/0   63.74 MB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0   71.65 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     25.7      0.31              0.00         1    0.308       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     25.7      0.31              0.00         1    0.308       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     25.7      0.31              0.00         1    0.308       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4.2 total, 4.2 interval
Flush(GB): cumulative 0.008, interval 0.008
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.01 GB write, 1.86 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.3 seconds
Interval compaction: 0.01 GB write, 1.86 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.3 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fbb98060010#8 capacity: 951.98 MB collections: 1 last_copies: 2 last_secs: 4.1e-05 secs_since: 4
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4.2 total, 4.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fbb98060010#8 capacity: 951.98 MB collections: 1 last_copies: 2 last_secs: 4.1e-05 secs_since: 4
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/06/20-08:24:02.717399 63 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/20-08:24:02.717672 63 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 604.3 total, 600.0 interval
Cumulative writes: 5751 writes, 5751 keys, 4432 commit groups, 1.3 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5751 writes, 0 syncs, 5751.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5751 writes, 5751 keys, 4432 commit groups, 1.3 writes per commit group, ingest: 0.39 MB, 0.00 MB/s
Interval WAL: 5751 writes, 0 syncs, 5751.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    7.91 MB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     25.7      0.31              0.00         1    0.308       0      0       0.0       0.0
  L1      1/0   63.74 MB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0   71.65 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     25.7      0.31              0.00         1    0.308       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     25.7      0.31              0.00         1    0.308       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 604.3 total, 600.0 interval
Flush(GB): cumulative 0.008, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.01 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.3 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fbb98060010#8 capacity: 951.98 MB collections: 2 last_copies: 2 last_secs: 0.003079 secs_since: 4
Block cache entry stats(count,size,portion): DataBlock(8,311.62 KB,0.0319671%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 604.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fbb98060010#8 capacity: 951.98 MB collections: 2 last_copies: 2 last_secs: 0.003079 secs_since: 4
Block cache entry stats(count,size,portion): DataBlock(8,311.62 KB,0.0319671%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
