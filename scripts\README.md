# Neo4j 装配体图转换器（对比学习优化版）

## 功能特性

- **层次结构图**: 保持 Assembly → SubAssembly → Part 的装配层次关系
- **移除 Feature 节点**: 简化图结构，专注于装配层次
- **多种初始化方法**: 支持零向量、自底向上聚合、随机初始化
- **装配体向量提取**: 从图文件中提取 Assembly 节点向量表示
- **对比学习优化**: 专门为对比学习任务设计

## 快速开始

### 1. 生成装配体图

```bash
# 使用自底向上聚合初始化（推荐用于对比学习）
python scripts/neo4j_to_pyg_optimized.py --init-method bottom_up --aggregation-mode mean --max-assemblies 100

# 输出: datasets/fusion360_assembly/{assembly_id}/assembly.pt
```

### 2. 提取装配体向量

```bash
# 提取并分析装配体向量表示
python scripts/extract_assembly_vectors.py --analyze

# 输出: dataset/assembly_representations.pkl
```

## 初始化方法

### `zero` - 零向量初始化

Assembly 和 SubAssembly 节点初始化为零向量，让 GNN 从零开始学习。

### `bottom_up` - 自底向上聚合（推荐）

父节点特征 = 所有子节点特征的聚合，为对比学习提供有意义的初始表示。
支持两种聚合模式：

- `mean`: 平均池化（默认）
- `max`: 最大池化

### `random` - 随机初始化

使用小随机值初始化，适合传统的可学习嵌入。

## 使用示例

### 生成图文件

```bash
# 自底向上初始化（平均池化）
python scripts/neo4j_to_pyg_optimized.py --init-method bottom_up --aggregation-mode mean

# 自底向上初始化（最大池化）
python scripts/neo4j_to_pyg_optimized.py --init-method bottom_up --aggregation-mode max

# 零向量初始化
python scripts/neo4j_to_pyg_optimized.py --init-method zero

# 随机初始化
python scripts/neo4j_to_pyg_optimized.py --init-method random
```

### 提取和分析向量

```bash
# 基本提取
python scripts/extract_assembly_vectors.py

# 详细分析
python scripts/extract_assembly_vectors.py --analyze

# 指定输出路径
python scripts/extract_assembly_vectors.py --output-path my_vectors.pkl

# 加载已有文件分析
python scripts/extract_assembly_vectors.py --load-existing dataset/assembly_representations.pkl --analyze

# 查找相似装配体
python scripts/extract_assembly_vectors.py --load-existing dataset/assembly_representations.pkl --find-similar "assembly_id"
```

## 输出格式

### 图文件 (.pt)

```python
import torch
data = torch.load('datasets/fusion360_assembly/assembly_id/assembly.pt')

# 属性
data.x                    # 节点特征矩阵 (num_nodes, 64)
data.edge_index          # 边索引 (2, num_edges)
data.node_type           # 节点类型: 0=Assembly, 1=SubAssembly, 2=Part
data.edge_type           # 边类型: 0=hasSubAssembly, 1=hasPart
data.assembly_node_idx   # Assembly节点索引
data.assembly_id         # 装配体ID
```

### 向量文件 (.pkl)

```python
import pickle
with open('dataset/assembly_representations.pkl', 'rb') as f:
    vectors = pickle.load(f)

# 格式: {assembly_id: assembly_vector}
# vectors['assembly_id'] -> np.array([64维向量])
```

## 对比学习应用

### 加载图数据

```python
import torch
data = torch.load('datasets/fusion360_assembly/assembly_id/assembly.pt')
assembly_repr = data.x[data.assembly_node_idx]  # Assembly节点表示
```

### 批量加载向量

```python
from scripts.extract_assembly_vectors import AssemblyVectorExtractor

extractor = AssemblyVectorExtractor()
vectors = extractor.extract_representations()

# 转换为张量
assembly_ids = list(vectors.keys())
assembly_tensors = torch.stack([torch.tensor(vectors[aid]) for aid in assembly_ids])
```

### GNN 训练示例

```python
import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv

class AssemblyGNN(torch.nn.Module):
    def __init__(self):
        super().__init__()
        self.gnn = GCNConv(64, 128)

    def forward(self, data):
        x = F.relu(self.gnn(data.x, data.edge_index))
        return x[data.assembly_node_idx]  # 返回Assembly节点表示

# 对比学习训练
model = AssemblyGNN()
for batch in dataloader:
    assembly_repr = model(batch)
    loss = contrastive_loss(assembly_repr, labels)
```

## 完整工作流程

```bash
# 步骤1: 生成图文件（使用平均池化）
python scripts/neo4j_to_pyg_optimized.py --init-method bottom_up --aggregation-mode mean --max-assemblies 100

# 步骤2: 提取装配体向量
python scripts/extract_assembly_vectors.py --analyze

# 步骤3: 在对比学习中使用
# 加载图文件或向量文件进行训练
```

## 故障排除

### Neo4j 连接失败

```bash
# 检查Neo4j服务
docker ps | grep neo4j
```

### 特征文件不存在

确保`dataset/fused_part_features.pkl`文件存在，包含零件的 64 维融合特征。

### 内存不足

```bash
# 减少处理数量
python scripts/neo4j_to_pyg_optimized.py --max-assemblies 50
```
