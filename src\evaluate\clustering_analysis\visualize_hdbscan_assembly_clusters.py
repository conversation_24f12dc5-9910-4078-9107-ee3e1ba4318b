#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化装配体 HDBSCAN 聚类结果，为每个簇生成图片网格。

支持装配体特征格式（assembly_features.pkl）和不同的特征类型。

用法示例：
python evaluate_scripts/clustering_analysis/visualize_hdbscan_assembly_clusters.py \
    --labels dataset/hdbscan_assembly_labels_shape.pkl \
    --img_dir datasets/fusion360_assembly

# 可视化融合特征的聚类结果
python evaluate_scripts/clustering_analysis/visualize_hdbscan_assembly_clusters.py \
    --labels dataset/hdbscan_assembly_labels_fused_features.pkl \
    --img_dir datasets/fusion360_assembly \
    --top_n 10 --samples_per_cluster 16

# 自定义输出目录
python evaluate_scripts/clustering_analysis/visualize_hdbscan_assembly_clusters.py \
    --labels dataset/hdbscan_assembly_labels_shape.pkl \
    --img_dir datasets/fusion360_assembly \
    --out_dir visualization_results/my_custom_assembly_clusters

可视化选项：
--top_n 10          # 只显示前10个最大的簇
--samples_per_cluster 16  # 每个簇展示16张图片
--min_cluster_size 5      # 只显示大于5个样本的簇（装配体数量较少）
"""
import argparse
import os
import pickle
import random
from collections import defaultdict
from pathlib import Path

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from tqdm import tqdm
import psycopg2
from psycopg2.extras import RealDictCursor

import sys
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.config import Config


def get_assembly_info_from_db(assembly_ids):
    """从PostgreSQL数据库中获取装配体的详细信息"""
    try:
        # 连接数据库
        conn = psycopg2.connect(**Config.POSTGRES_CONFIG)
        
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询装配体的详细信息
            placeholders = ','.join(['%s'] * len(assembly_ids))
            query = f"""
                SELECT uuid, name, description, length, width, height, 
                       volume, area, part_count, mass, density
                FROM cad_rag.assemblies 
                WHERE uuid IN ({placeholders})
            """
            cursor.execute(query, assembly_ids)
            results = cursor.fetchall()
            
            # 构建字典映射
            assembly_mapping = {row['uuid']: row for row in results}
            
            print(f"从数据库获取了 {len(assembly_mapping)} 个装配体的详细信息")
            return assembly_mapping
            
    except Exception as e:
        print(f"从数据库获取装配体信息失败: {e}")
        # 返回默认值
        return {aid: {'name': f'assembly_{aid[:8]}', 'description': '未知'} for aid in assembly_ids}
    finally:
        if 'conn' in locals():
            conn.close()


def load_assembly_clustering_results(labels_path):
    """加载装配体聚类结果"""
    with open(labels_path, 'rb') as f:
        data = pickle.load(f)
    
    labels = data['labels']
    assembly_ids = data['assembly_ids']
    
    # 检查是否有装配体详细信息，如果没有则从数据库读取
    if 'assembly_info' in data:
        assembly_info = data['assembly_info']
        print("使用已有的装配体详细信息")
    else:
        print("装配体详细信息不存在，从数据库中读取...")
        assembly_info = get_assembly_info_from_db(assembly_ids)
    
    feature_type = data.get('feature_type', 'unknown')
    
    # 构建每个簇的样本索引
    clusters = defaultdict(list)
    for i, label in enumerate(labels):
        if label != -1:  # 忽略噪声点
            clusters[int(label)].append(i)
    
    return clusters, labels, assembly_ids, assembly_info, data.get('params', {}), feature_type


def get_cluster_stats(clusters):
    """获取簇的统计信息"""
    stats = []
    for cluster_id, indices in clusters.items():
        stats.append((cluster_id, len(indices)))
    
    # 按簇大小排序
    stats.sort(key=lambda x: x[1], reverse=True)
    return stats


def create_assembly_cluster_grid(cluster_indices, assembly_ids, assembly_info, img_dir, 
                                samples_per_cluster=16, grid_size=None):
    """为一个装配体簇创建图片网格"""
    if grid_size is None:
        # 自动计算网格大小
        grid_size = int(np.ceil(np.sqrt(samples_per_cluster)))
    
    # 如果簇太大，随机采样
    if len(cluster_indices) > samples_per_cluster:
        sample_indices = random.sample(cluster_indices, samples_per_cluster)
    else:
        sample_indices = cluster_indices
    
    # 创建网格
    fig = plt.figure(figsize=(grid_size*2.5, grid_size*2.5))
    
    for i, idx in enumerate(sample_indices):
        if i >= samples_per_cluster:
            break
            
        assembly_id = assembly_ids[idx]
        assembly_detail = assembly_info.get(assembly_id, {})
        assembly_name = assembly_detail.get('name', assembly_id[:8])
        
        # 构建图片路径 - 装配体图片位于 datasets/fusion360_assembly/{assembly_id}/assembly.png
        img_path = os.path.join(img_dir, assembly_id, "assembly.png")
        
        # 备用路径（兼容其他可能的路径格式）
        backup_paths = [
            os.path.join(img_dir, f"{assembly_id}.png"),           # 直接使用assembly_id
            os.path.join(img_dir, f"{assembly_name}.png"),         # 使用assembly名称
            os.path.join(img_dir, "assemblies", f"{assembly_id}.png"),  # 在assemblies子目录中
        ]
        
        # 如果主路径不存在，尝试备用路径
        if not os.path.exists(img_path):
            for backup_path in backup_paths:
                if os.path.exists(backup_path):
                    img_path = backup_path
                    break
            else:
                img_path = None  # 没有找到任何有效路径
        
        if img_path and os.path.exists(img_path):
            try:
                # 添加子图
                ax = fig.add_subplot(grid_size, grid_size, i + 1)
                img = Image.open(img_path)
                ax.imshow(img)
                ax.axis('off')
                
                # 设置标题，包含更多信息
                title_text = f"{assembly_name}"
                if assembly_detail.get('part_count'):
                    title_text += f"\n({assembly_detail['part_count']}零件)"
                ax.set_title(title_text, fontsize=8)
            except Exception as e:
                print(f"无法加载装配体图片 {img_path}: {e}")
        else:
            # 如果图片不存在，显示装配体信息
            ax = fig.add_subplot(grid_size, grid_size, i + 1)
            
            # 显示装配体基本信息
            info_text = f"装配体: {assembly_name}\n"
            if assembly_detail.get('part_count'):
                info_text += f"零件数: {assembly_detail['part_count']}\n"
            if assembly_detail.get('volume'):
                info_text += f"体积: {assembly_detail['volume']:.2f}\n"
            if assembly_detail.get('mass'):
                info_text += f"质量: {assembly_detail['mass']:.2f}\n"
            info_text += f"ID: {assembly_id[:8]}..."
            
            ax.text(0.5, 0.5, info_text, 
                   ha='center', va='center', fontsize=6, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.7))
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
    
    plt.tight_layout()
    return fig


def visualize_assembly_clusters(clusters, assembly_ids, assembly_info, img_dir, out_dir,
                               top_n=None, samples_per_cluster=16, min_cluster_size=0, feature_type="unknown"):
    """可视化多个装配体簇"""
    # 获取簇统计信息
    stats = get_cluster_stats(clusters)
    
    # 过滤小簇
    if min_cluster_size > 0:
        stats = [(cid, size) for cid, size in stats if size >= min_cluster_size]
    
    # 限制簇数量
    if top_n is not None:
        stats = stats[:top_n]
    
    # 创建输出目录
    os.makedirs(out_dir, exist_ok=True)
    
    # 为每个簇创建可视化
    for cluster_id, size in tqdm(stats, desc="生成装配体簇可视化"):
        cluster_indices = clusters[cluster_id]
        
        # 创建图片网格
        fig = create_assembly_cluster_grid(
            cluster_indices, assembly_ids, assembly_info, img_dir, 
            samples_per_cluster=samples_per_cluster
        )
        
        # 设置标题
        plt.suptitle(f"装配体簇 #{cluster_id} - 大小: {size} ({feature_type} 特征)", fontsize=16)
        
        # 保存图片
        out_path = os.path.join(out_dir, f"assembly_cluster_{cluster_id:03d}_{size}.png")
        plt.savefig(out_path, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
    # 创建索引HTML文件
    create_assembly_html_index(out_dir, stats, feature_type, assembly_ids, assembly_info, clusters)
    
    return len(stats)


def create_assembly_html_index(out_dir, stats, feature_type="unknown", assembly_ids=None, assembly_info=None, clusters=None):
    """创建装配体HTML索引文件，方便浏览所有簇"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>装配体HDBSCAN聚类结果可视化 - {feature_type} 特征</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .cluster-grid {{ display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 20px; }}
            .cluster-item {{ border: 1px solid #ddd; padding: 15px; border-radius: 8px; }}
            .cluster-item img {{ max-width: 100%; height: auto; }}
            .cluster-info {{ background: #f9f9f9; padding: 10px; margin-bottom: 10px; border-radius: 5px; }}
            h1, h2 {{ color: #333; }}
            .feature-info {{ background: #e8f4f8; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
            .summary {{ background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
            .stat-item {{ margin-bottom: 5px; }}
        </style>
    </head>
    <body>
        <h1>装配体HDBSCAN聚类结果可视化</h1>
        <div class="feature-info">
            <strong>特征类型:</strong> {feature_type}<br>
            <strong>簇数量:</strong> {len(stats)}<br>
            <strong>分析对象:</strong> 装配体
        </div>
    """
    
    # 添加聚类统计摘要
    if clusters and assembly_ids and assembly_info:
        total_assemblies = len(assembly_ids)
        clustered_assemblies = sum(len(indices) for indices in clusters.values())
        html_content += f"""
        <div class="summary">
            <h3>聚类统计摘要</h3>
            <div class="stat-item"><strong>总装配体数:</strong> {total_assemblies}</div>
            <div class="stat-item"><strong>成功聚类装配体数:</strong> {clustered_assemblies}</div>
            <div class="stat-item"><strong>聚类覆盖率:</strong> {clustered_assemblies/total_assemblies*100:.1f}%</div>
        </div>
        """
    
    html_content += '<div class="cluster-grid">'
    
    for cluster_id, size in stats:
        img_filename = f"assembly_cluster_{cluster_id:03d}_{size}.png"
        
        # 计算簇中装配体的统计信息
        cluster_info = ""
        if clusters and assembly_ids and assembly_info:
            cluster_indices = clusters[cluster_id]
            cluster_assemblies = [assembly_ids[i] for i in cluster_indices]
            
            # 统计零件数量
            part_counts = [assembly_info.get(aid, {}).get('part_count', 0) for aid in cluster_assemblies if assembly_info.get(aid, {}).get('part_count')]
            if part_counts:
                avg_parts = np.mean(part_counts)
                cluster_info += f"平均零件数: {avg_parts:.1f}<br>"
            
            # 统计体积
            volumes = [assembly_info.get(aid, {}).get('volume', 0) for aid in cluster_assemblies if assembly_info.get(aid, {}).get('volume')]
            if volumes:
                avg_volume = np.mean(volumes)
                cluster_info += f"平均体积: {avg_volume:.2f}<br>"
        
        html_content += f"""
        <div class="cluster-item">
            <h2>装配体簇 #{cluster_id} (大小: {size})</h2>
            <div class="cluster-info">
                {cluster_info}
            </div>
            <a href="{img_filename}" target="_blank">
                <img src="{img_filename}" alt="装配体簇 {cluster_id}">
            </a>
        </div>
        """
    
    html_content += """
        </div>
    </body>
    </html>
    """
    
    with open(os.path.join(out_dir, "index.html"), "w", encoding="utf-8") as f:
        f.write(html_content)


def main():
    parser = argparse.ArgumentParser(description="可视化装配体 HDBSCAN 聚类结果")
    parser.add_argument("--labels", default="dataset/hdbscan_assembly_labels_structural.pkl", help="装配体聚类标签文件路径")
    parser.add_argument("--img_dir", default="datasets/fusion360_assembly", help="原始图片目录")
    parser.add_argument("--out_dir", default=None, help="输出目录，如果不指定则根据特征类型自动命名")
    parser.add_argument("--top_n", type=int, default=None, help="只显示前N个最大的簇")
    parser.add_argument("--samples_per_cluster", type=int, default=16, help="每个簇显示的样本数")
    parser.add_argument("--min_cluster_size", type=int, default=0, help="最小簇大小过滤")
    args = parser.parse_args()
    
    # 加载聚类结果
    print(f"加载装配体聚类结果: {args.labels}")
    clusters, labels, assembly_ids, assembly_info, params, feature_type = load_assembly_clustering_results(args.labels)
    
    # 如果没有指定输出目录，根据特征类型自动命名
    if args.out_dir is None:
        args.out_dir = f"visualization_results/{feature_type}_assembly_clusters"
    
    # 打印参数
    print(f"装配体特征类型: {feature_type}")
    print(f"输出目录: {args.out_dir}")
    print("聚类参数:")
    for k, v in params.items():
        print(f"  {k}: {v}")
    
    # 统计信息
    n_clusters = len(clusters)
    n_noise = np.sum(labels == -1)
    n_total = len(labels)
    
    print(f"共 {n_clusters} 个装配体簇, {n_noise} 个噪声点 ({n_noise/n_total:.2%})")
    
    # 可视化簇
    print(f"开始生成装配体可视化到: {args.out_dir}")
    visualized = visualize_assembly_clusters(
        clusters, assembly_ids, assembly_info, args.img_dir, args.out_dir,
        top_n=args.top_n, 
        samples_per_cluster=args.samples_per_cluster,
        min_cluster_size=args.min_cluster_size,
        feature_type=feature_type
    )
    
    print(f"已生成 {visualized} 个装配体簇的可视化")
    print(f"请在浏览器中打开 {os.path.join(args.out_dir, 'index.html')} 查看结果")


if __name__ == "__main__":
    main()
