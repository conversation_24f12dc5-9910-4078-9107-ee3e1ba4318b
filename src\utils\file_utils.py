import os
import json
import shutil
import random
import glob
from minio import Minio
from minio.error import S3Error

def get_fusion360_test_paths(dataset_dir="datasets/fusion360_assembly"):
    """
    获取Fusion360数据集中测试集的文件夹路径
    
    参数:
        dataset_dir (str): Fusion360数据集文件夹路径
        
    返回:
        list: 所有测试集文件夹的完整路径列表
    """
    # 读取train_test.json文件
    json_path = os.path.join(dataset_dir, 'train_test.json')
    
    with open(json_path, 'r') as f:
        data = json.load(f)
    
    # 获取test数组
    test_folders = data.get('test', [])
    
    # 拼接路径
    test_paths = [os.path.join(dataset_dir, folder) for folder in test_folders]
    
    return test_paths

def move_test_folders_to_destination(dataset_dir="datasets/fusion360_assembly", destination_dir="E:\\dataset\\fusion360\\assemply_test"):
    """
    将Fusion360数据集中的测试集文件夹移动到指定目标路径
    
    参数:
        dataset_dir (str): Fusion360数据集文件夹路径
        destination_dir (str): 目标路径，默认为E:\dataset\fusion360\assemply_test
        
    返回:
        list: 成功移动的文件夹列表
    """
    # 获取测试集文件夹路径
    test_paths = get_fusion360_test_paths(dataset_dir)
    
    # 确保目标文件夹存在
    os.makedirs(destination_dir, exist_ok=True)
    
    moved_folders = []
    
    # 移动每个测试集文件夹
    for path in test_paths:
        folder_name = os.path.basename(path)
        destination_path = os.path.join(destination_dir, folder_name)
        
        try:
            # 如果目标路径已存在，先删除
            if os.path.exists(destination_path):
                if os.path.isdir(destination_path):
                    shutil.rmtree(destination_path)
                else:
                    os.remove(destination_path)
            
            # 移动文件夹
            shutil.move(path, destination_path)
            moved_folders.append(folder_name)
            print(f"已成功移动: {folder_name} 到 {destination_path}")
        except Exception as e:
            print(f"移动 {folder_name} 失败: {str(e)}")
    
    return moved_folders

def sample_and_copy_folders(source_dir="E:\\dataset\\fusion360\\assemply\\assemply", 
                           destination_dir="E:\\dataset\\fusion360\\assemply\\assembly_demonstration",
                           sample_count=100,
                           file_types=(".png", ".step", ".json")):
    """
    从源目录随机抽取指定数量的子文件夹，并只复制特定类型的文件到目标目录
    
    参数:
        source_dir (str): 源文件夹路径
        destination_dir (str): 目标文件夹路径
        sample_count (int): 要抽取的子文件夹数量
        file_types (tuple): 要复制的文件类型列表
        
    返回:
        list: 成功抽样并复制的文件夹列表
    """
    # 确保目标文件夹存在
    os.makedirs(destination_dir, exist_ok=True)
    
    # 获取所有子文件夹
    all_subfolders = [f for f in os.listdir(source_dir) 
                     if os.path.isdir(os.path.join(source_dir, f))]
    
    # 如果子文件夹数量小于要抽取的数量，调整抽样数量
    sample_count = min(sample_count, len(all_subfolders))
    
    # 随机抽取指定数量的子文件夹
    sampled_folders = random.sample(all_subfolders, sample_count)
    
    copied_folders = []
    
    # 处理每个抽样的文件夹
    for folder in sampled_folders:
        source_folder_path = os.path.join(source_dir, folder)
        dest_folder_path = os.path.join(destination_dir, folder)
        
        try:
            # 确保目标子文件夹存在
            os.makedirs(dest_folder_path, exist_ok=True)
            
            # 查找所有符合条件的文件
            for file_type in file_types:
                files = glob.glob(os.path.join(source_folder_path, f"*{file_type}"))
                
                # 复制每个文件
                for file_path in files:
                    file_name = os.path.basename(file_path)
                    dest_file_path = os.path.join(dest_folder_path, file_name)
                    
                    # 复制文件
                    shutil.copy2(file_path, dest_file_path)
            
            copied_folders.append(folder)
            print(f"已成功处理: {folder}")
        except Exception as e:
            print(f"处理 {folder} 失败: {str(e)}")
    
    print(f"总共成功处理 {len(copied_folders)} 个文件夹")
    return copied_folders

def upload_files_to_minio(source_dir="E:\\dataset\\fusion360\\assemply\\assemply",
                         bucket_name="cad-models",
                         minio_endpoint="localhost:9000",
                         minio_access_key="minioadmin",
                         minio_secret_key="minioadmin",
                         file_types=(".glb", ".png"),
                         secure=False):
    """
    遍历指定文件夹，将所有符合指定类型的文件上传到MinIO存储桶，保持相同的目录结构
    
    参数:
        source_dir (str): 源文件夹路径
        bucket_name (str): MinIO存储桶名称
        minio_endpoint (str): MinIO服务端点
        minio_access_key (str): MinIO访问密钥
        minio_secret_key (str): MinIO秘密密钥
        file_types (tuple): 要上传的文件类型列表
        secure (bool): 是否使用HTTPS连接
        
    返回:
        dict: 上传统计信息，包含成功和失败的文件数量
    """
    # 初始化MinIO客户端
    try:
        client = Minio(
            minio_endpoint,
            access_key=minio_access_key,
            secret_key=minio_secret_key,
            secure=secure
        )
        
        # 确保存储桶存在
        if not client.bucket_exists(bucket_name):
            client.make_bucket(bucket_name)
            print(f"创建存储桶 {bucket_name} 成功")
    except S3Error as e:
        print(f"MinIO连接或初始化错误: {e}")
        return {"success": 0, "failed": 0, "error": str(e)}
    
    # 统计上传结果
    stats = {"success": 0, "failed": 0, "skipped": 0}
    
    # 遍历源目录
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            # 检查文件类型是否匹配
            if any(file.lower().endswith(ext) for ext in file_types):
                # 获取文件的完整路径
                file_path = os.path.join(root, file)
                
                # 计算相对路径，用作对象名称
                rel_path = os.path.relpath(file_path, source_dir)
                # 将Windows路径分隔符转换为POSIX风格
                object_name = rel_path.replace("\\", "/")
                
                try:
                    # 上传文件
                    client.fput_object(
                        bucket_name,
                        object_name,
                        file_path
                    )
                    stats["success"] += 1
                    print(f"成功上传: {object_name}")
                except S3Error as e:
                    stats["failed"] += 1
                    print(f"上传失败 {object_name}: {e}")
            else:
                stats["skipped"] += 1
    
    print(f"上传完成。成功: {stats['success']}, 失败: {stats['failed']}, 跳过: {stats['skipped']}")
    return stats


if __name__ == "__main__":
    # move_test_folders_to_destination()
    # sample_and_copy_folders()
    
    # 示例：上传文件到MinIO
    upload_files_to_minio(
        source_dir="E:\\dataset\\fusion360\\assembly\\assembly",
        bucket_name="cad-models",
        minio_endpoint="*************:9000",
        minio_access_key="minioadmin",
        minio_secret_key="minioadmin",
        file_types=(".glb")
    )