#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试装配体图片路径的脚本

用于验证装配体图片路径的正确性
"""

import os
import sys
from pathlib import Path

def test_assembly_image_paths():
    """测试装配体图片路径"""
    print("=== 测试装配体图片路径 ===")
    
    base_img_dir = "datasets/fusion360_assembly"
    
    if not os.path.exists(base_img_dir):
        print(f"✗ 基础图片目录不存在: {base_img_dir}")
        return False
    
    print(f"✓ 基础图片目录存在: {base_img_dir}")
    
    # 列出所有装配体子目录
    assembly_dirs = []
    for item in os.listdir(base_img_dir):
        item_path = os.path.join(base_img_dir, item)
        if os.path.isdir(item_path):
            assembly_dirs.append(item)
    
    print(f"发现 {len(assembly_dirs)} 个装配体目录")
    
    if len(assembly_dirs) == 0:
        print("✗ 没有找到任何装配体目录")
        return False
    
    # 检查前几个装配体的图片文件
    valid_images = 0
    test_count = min(10, len(assembly_dirs))  # 测试前10个或所有的装配体
    
    for i, assembly_id in enumerate(assembly_dirs[:test_count]):
        assembly_img_path = os.path.join(base_img_dir, assembly_id, "assembly.png")
        
        if os.path.exists(assembly_img_path):
            valid_images += 1
            print(f"✓ 装配体 {assembly_id}: 图片存在")
        else:
            print(f"✗ 装配体 {assembly_id}: 图片不存在于 {assembly_img_path}")
            
            # 检查该目录下是否有其他图片文件
            assembly_dir_path = os.path.join(base_img_dir, assembly_id)
            if os.path.exists(assembly_dir_path):
                files = os.listdir(assembly_dir_path)
                image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                if image_files:
                    print(f"  该目录下的图片文件: {image_files}")
    
    print(f"\n总结: {valid_images}/{test_count} 个装配体有有效的图片文件")
    
    if valid_images > 0:
        print("✓ 找到了装配体图片，路径格式正确")
        return True
    else:
        print("✗ 没有找到任何有效的装配体图片")
        return False


def show_sample_path_structure():
    """显示示例路径结构"""
    print("\n=== 期望的装配体图片路径结构 ===")
    print("datasets/")
    print("└── fusion360_assembly/")
    print("    ├── {装配体ID1}/")
    print("    │   └── assembly.png")
    print("    ├── {装配体ID2}/")
    print("    │   └── assembly.png")
    print("    └── ...")
    print("\n示例:")
    print("datasets/fusion360_assembly/abc123-def456-789/assembly.png")


def main():
    """主函数"""
    print("装配体图片路径测试工具\n")
    
    show_sample_path_structure()
    
    success = test_assembly_image_paths()
    
    if success:
        print("\n✓ 装配体图片路径测试通过!")
        print("可以使用装配体聚类可视化功能")
    else:
        print("\n✗ 装配体图片路径测试失败")
        print("请检查装配体图片是否按照正确的目录结构存放")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
