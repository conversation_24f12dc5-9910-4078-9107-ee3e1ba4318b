"""
几何和语义查询智能体 (Geometric and Semantic Query Agent)
与Milvus向量数据库交互，实现相似度搜索功能
"""

import logging
from typing import List, Dict, Any, Optional
import numpy as np
from src.agent.base_agent import BaseAgent
from src.agent.data_models import QueryResult, UnifiedGeometrySemanticTask, SearchResultItem
from src.utils.database.milvus_utils import MilvusManager
from src.models.clip import CLIPFeatureExtractor
from src.config import Config
import torch

logger = logging.getLogger(__name__)


class GeometrySemanticAgent(BaseAgent):
    """
    几何和语义查询智能体
    
    该智能体专门处理向量相似性搜索，支持以下三种搜索模式：
    1. 几何相似性检索：基于3D形状的视觉特征向量进行相似性搜索
    2. 深层语义检索：基于文本描述的语义理解进行相似性搜索
    3. 混合检索：结合文本语义和几何形状的综合搜索
    """
    
    def __init__(self, agent_name: str = "GeometrySemanticAgent"):
        super().__init__(agent_name)
        self.milvus_manager = None
        self.clip_model = None
        # 使用通用的CAD集合名称，该集合包含装配体和零件数据
        self.collection_name = "cad_collection"
        self._connected = False
        
    async def connect(self):
        """连接到Milvus向量数据库并加载模型"""
        try:
            # 检查设备
            device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"{self.agent_name} 使用设备: {device}")
            
            # 初始化CLIP模型
            logger.info(f"{self.agent_name} 正在加载CLIP模型...")
            self.clip_model = CLIPFeatureExtractor(device=device)
            
            # 初始化Milvus管理器
            logger.info(f"{self.agent_name} 正在初始化Milvus管理器和BGE模型...")
            self.milvus_manager = MilvusManager(use_reranker=True, device=device)
            
            # 连接到Milvus
            await self._connect_milvus()
            
            self._connected = True
            logger.info(f"{self.agent_name} 成功连接到Milvus数据库并加载所有模型")
        except Exception as e:
            logger.error(f"{self.agent_name} 连接Milvus或加载模型失败: {e}")
            raise
    
    async def _connect_milvus(self):
        """异步连接Milvus"""
        self.milvus_manager.connect()
    
    async def disconnect(self):
        """断开数据库连接"""
        if self.milvus_manager:
            # Milvus连接会自动管理，这里可以释放资源
            logger.info(f"{self.agent_name} 已断开Milvus连接")
    
    async def _ensure_connected(self):
        """确保已连接到数据库"""
        if not self._connected:
            await self.connect()
            self._connected = True
    
    async def execute_task(self, task: UnifiedGeometrySemanticTask) -> QueryResult:
        """
        执行几何和语义查询任务

        Args:
            task: UnifiedGeometrySemanticTask实例，包含自然语言查询文本和/或形状特征向量

        Returns:
            QueryResult: 包含搜索结果的查询结果对象
        """
        import time
        start_time = time.time()

        try:
            await self._ensure_connected()

            # 验证输入：自然语言描述和形状特征向量不可同时为空
            if not task.query_text and not task.shape_vector:
                raise ValueError("自然语言描述和形状特征向量不可同时为空")

            search_results = []

            if task.query_text and task.shape_vector:
                # 混合搜索（文本+形状）
                raw_results = await self.search_hybrid_with_shape(
                    query_text=task.query_text,
                    shape_vector=task.shape_vector,
                    top_k=task.top_k,
                    shape_weight=task.shape_weight
                )
                search_type = "hybrid"
            elif task.query_text:
                # 纯文本语义搜索
                raw_results = await self.search_by_text(
                    query_text=task.query_text,
                    top_k=task.top_k,
                    use_reranker=task.use_reranker
                )
                search_type = "semantic"
            else:
                # 纯形状几何搜索
                raw_results = await self.search_by_shape(
                    shape_vector=task.shape_vector,
                    top_k=task.top_k
                )
                search_type = "geometry"

            # 转换为标准化的SearchResultItem格式
            for result in raw_results:
                search_results.append(SearchResultItem(
                    rank=result["rank"],
                    uuid=result["assembly_id"],
                    name=result.get("name", result["assembly_id"]),
                    description=result["description"],
                    similarity_score=result["similarity_score"],
                    search_type=search_type,
                    metadata=result
                ))

            # 如果提供了ID列表，进行过滤（使用uuid字段）
            if task.id_list:
                search_results = [r for r in search_results if r.uuid in task.id_list]
                # 重新设置排名
                for i, result in enumerate(search_results):
                    result.rank = i + 1

            execution_time = time.time() - start_time

            return QueryResult(
                task_id=task.task_id,
                status='success',
                results=search_results,
                execution_time=execution_time,
                total_results=len(search_results)
            )

        except Exception as e:
            logger.error(f"{self.agent_name} 执行任务失败: {e}")
            execution_time = time.time() - start_time
            return QueryResult(
                task_id=task.task_id,
                status='failure',
                error_message=str(e),
                results=[],
                execution_time=execution_time,
                total_results=0
            )
    

    
    async def search_by_text(self, query_text: str, top_k: int = 5, use_reranker: bool = True) -> List[Dict[str, Any]]:
        """
        基于文本查询进行语义搜索
        
        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            use_reranker: 是否使用重排序器
            
        Returns:
            搜索结果列表
        """
        try:
            if self.milvus_manager is None:
                await self.connect()
            
            # 设置重排序器使用状态
            self.milvus_manager.use_reranker = use_reranker
            
            results = self.milvus_manager.search_cad_by_text(
                collection_name=self.collection_name,
                query_text=query_text,
                top_k=top_k
            )
            
            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results):
                formatted_results.append({
                    "rank": i + 1,
                    "assembly_id": result["id"],
                    "description": result["description"],
                    "similarity_score": float(result["score"]),
                    "search_type": "semantic"
                })
            
            logger.info(f"语义搜索完成，返回{len(formatted_results)}个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"文本语义搜索失败: {e}")
            raise
    
    async def search_by_shape(self, shape_vector: List[float], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        基于形状向量进行几何搜索

        Args:
            shape_vector: 形状特征向量（768维）
            top_k: 返回结果数量

        Returns:
            搜索结果列表
        """
        try:
            # 验证向量维度
            if len(shape_vector) != 768:
                raise ValueError(f"几何向量维度应为768，实际为{len(shape_vector)}")

            if self.milvus_manager is None:
                await self.connect()

            results = self.milvus_manager.search_cad_by_shape(
                collection_name=self.collection_name,
                query_vector=shape_vector,
                top_k=top_k
            )

            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results):
                formatted_results.append({
                    "rank": i + 1,
                    "assembly_id": result["id"],
                    "description": result["description"],
                    "similarity_score": float(result["score"]),
                    "search_type": "geometry"
                })

            logger.info(f"几何搜索完成，返回{len(formatted_results)}个结果")
            return formatted_results

        except Exception as e:
            logger.error(f"几何相似性搜索失败: {e}")
            raise
    
    async def search_hybrid_with_shape(
        self, 
        query_text: str, 
        shape_vector: Optional[List[float]] = None, 
        top_k: int = 5, 
        shape_weight: float = 0.3
    ) -> List[Dict[str, Any]]:
        """
        混合搜索（文本语义 + 形状几何）
        
        Args:
            query_text: 查询文本
            shape_vector: 形状特征向量（可选）
            top_k: 返回结果数量
            shape_weight: 形状搜索权重（0-1之间）
            
        Returns:
            搜索结果列表
        """
        try:
            if self.milvus_manager is None:
                await self.connect()
            
            results = self.milvus_manager.search_cad_hybrid(
                collection_name=self.collection_name,
                query_text=query_text,
                query_shape_vector=shape_vector,
                top_k=top_k,
                shape_weight=shape_weight
            )
            
            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results):
                formatted_results.append({
                    "rank": i + 1,
                    "assembly_id": result["id"],
                    "description": result["description"],
                    "similarity_score": float(result["score"]),
                    "search_type": "hybrid",
                    "has_shape_vector": shape_vector is not None,
                    "shape_weight": shape_weight
                })
            
            logger.info(f"混合搜索完成，返回{len(formatted_results)}个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            raise
