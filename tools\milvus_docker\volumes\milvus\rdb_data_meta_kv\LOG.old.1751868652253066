2025/06/25-01:08:31.800245 46 RocksDB version: 6.29.5
2025/06/25-01:08:31.800796 46 Git sha 0
2025/06/25-01:08:31.800800 46 Compile date 2024-11-15 11:22:58
2025/06/25-01:08:31.800804 46 DB SUMMARY
2025/06/25-01:08:31.800805 46 DB Session ID:  98460IFQDGWL0GKLFD7U
2025/06/25-01:08:31.830766 46 CURRENT file:  CURRENT
2025/06/25-01:08:31.830773 46 IDENTITY file:  IDENTITY
2025/06/25-01:08:31.853255 46 MANIFEST file:  MANIFEST-000026 size: 595 Bytes
2025/06/25-01:08:31.853265 46 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 4, files: 000009.sst 000013.sst 000019.sst 000025.sst 
2025/06/25-01:08:31.853270 46 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000027.log size: 24876124 ; 
2025/06/25-01:08:31.853272 46                         Options.error_if_exists: 0
2025/06/25-01:08:31.853273 46                       Options.create_if_missing: 1
2025/06/25-01:08:31.853274 46                         Options.paranoid_checks: 1
2025/06/25-01:08:31.853274 46             Options.flush_verify_memtable_count: 1
2025/06/25-01:08:31.853275 46                               Options.track_and_verify_wals_in_manifest: 0
2025/06/25-01:08:31.853275 46                                     Options.env: 0x7fdd922dcd00
2025/06/25-01:08:31.853276 46                                      Options.fs: PosixFileSystem
2025/06/25-01:08:31.853277 46                                Options.info_log: 0x7fdc93290050
2025/06/25-01:08:31.853277 46                Options.max_file_opening_threads: 16
2025/06/25-01:08:31.853278 46                              Options.statistics: (nil)
2025/06/25-01:08:31.853278 46                               Options.use_fsync: 0
2025/06/25-01:08:31.853279 46                       Options.max_log_file_size: 0
2025/06/25-01:08:31.853279 46                  Options.max_manifest_file_size: 1073741824
2025/06/25-01:08:31.853280 46                   Options.log_file_time_to_roll: 0
2025/06/25-01:08:31.853280 46                       Options.keep_log_file_num: 1000
2025/06/25-01:08:31.853281 46                    Options.recycle_log_file_num: 0
2025/06/25-01:08:31.853281 46                         Options.allow_fallocate: 1
2025/06/25-01:08:31.853282 46                        Options.allow_mmap_reads: 0
2025/06/25-01:08:31.853282 46                       Options.allow_mmap_writes: 0
2025/06/25-01:08:31.853283 46                        Options.use_direct_reads: 0
2025/06/25-01:08:31.853283 46                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/25-01:08:31.853284 46          Options.create_missing_column_families: 0
2025/06/25-01:08:31.853284 46                              Options.db_log_dir: 
2025/06/25-01:08:31.853284 46                                 Options.wal_dir: 
2025/06/25-01:08:31.853285 46                Options.table_cache_numshardbits: 6
2025/06/25-01:08:31.853285 46                         Options.WAL_ttl_seconds: 0
2025/06/25-01:08:31.853286 46                       Options.WAL_size_limit_MB: 0
2025/06/25-01:08:31.853286 46                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/25-01:08:31.853287 46             Options.manifest_preallocation_size: 4194304
2025/06/25-01:08:31.853287 46                     Options.is_fd_close_on_exec: 1
2025/06/25-01:08:31.853288 46                   Options.advise_random_on_open: 1
2025/06/25-01:08:31.853288 46                   Options.experimental_mempurge_threshold: 0.000000
2025/06/25-01:08:31.853553 46                    Options.db_write_buffer_size: 0
2025/06/25-01:08:31.853556 46                    Options.write_buffer_manager: 0x7fdc95e400a0
2025/06/25-01:08:31.853557 46         Options.access_hint_on_compaction_start: 1
2025/06/25-01:08:31.853558 46  Options.new_table_reader_for_compaction_inputs: 0
2025/06/25-01:08:31.853558 46           Options.random_access_max_buffer_size: 1048576
2025/06/25-01:08:31.853559 46                      Options.use_adaptive_mutex: 0
2025/06/25-01:08:31.853559 46                            Options.rate_limiter: (nil)
2025/06/25-01:08:31.853573 46     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/25-01:08:31.853574 46                       Options.wal_recovery_mode: 2
2025/06/25-01:08:31.860052 46                  Options.enable_thread_tracking: 0
2025/06/25-01:08:31.860059 46                  Options.enable_pipelined_write: 0
2025/06/25-01:08:31.860060 46                  Options.unordered_write: 0
2025/06/25-01:08:31.860060 46         Options.allow_concurrent_memtable_write: 1
2025/06/25-01:08:31.860061 46      Options.enable_write_thread_adaptive_yield: 1
2025/06/25-01:08:31.860061 46             Options.write_thread_max_yield_usec: 100
2025/06/25-01:08:31.860062 46            Options.write_thread_slow_yield_usec: 3
2025/06/25-01:08:31.860062 46                               Options.row_cache: None
2025/06/25-01:08:31.860063 46                              Options.wal_filter: None
2025/06/25-01:08:31.860064 46             Options.avoid_flush_during_recovery: 0
2025/06/25-01:08:31.860064 46             Options.allow_ingest_behind: 0
2025/06/25-01:08:31.860064 46             Options.preserve_deletes: 0
2025/06/25-01:08:31.860065 46             Options.two_write_queues: 0
2025/06/25-01:08:31.860065 46             Options.manual_wal_flush: 0
2025/06/25-01:08:31.860066 46             Options.atomic_flush: 0
2025/06/25-01:08:31.860066 46             Options.avoid_unnecessary_blocking_io: 0
2025/06/25-01:08:31.860067 46                 Options.persist_stats_to_disk: 0
2025/06/25-01:08:31.860067 46                 Options.write_dbid_to_manifest: 0
2025/06/25-01:08:31.860068 46                 Options.log_readahead_size: 0
2025/06/25-01:08:31.860068 46                 Options.file_checksum_gen_factory: Unknown
2025/06/25-01:08:31.860069 46                 Options.best_efforts_recovery: 0
2025/06/25-01:08:31.860069 46                Options.max_bgerror_resume_count: 2147483647
2025/06/25-01:08:31.860070 46            Options.bgerror_resume_retry_interval: 1000000
2025/06/25-01:08:31.860070 46             Options.allow_data_in_errors: 0
2025/06/25-01:08:31.860071 46             Options.db_host_id: __hostname__
2025/06/25-01:08:31.860074 46             Options.max_background_jobs: 2
2025/06/25-01:08:31.860075 46             Options.max_background_compactions: -1
2025/06/25-01:08:31.860075 46             Options.max_subcompactions: 1
2025/06/25-01:08:31.860076 46             Options.avoid_flush_during_shutdown: 0
2025/06/25-01:08:31.860076 46           Options.writable_file_max_buffer_size: 1048576
2025/06/25-01:08:31.860077 46             Options.delayed_write_rate : 16777216
2025/06/25-01:08:31.860077 46             Options.max_total_wal_size: 0
2025/06/25-01:08:31.860078 46             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/25-01:08:31.860078 46                   Options.stats_dump_period_sec: 600
2025/06/25-01:08:31.860079 46                 Options.stats_persist_period_sec: 600
2025/06/25-01:08:31.860079 46                 Options.stats_history_buffer_size: 1048576
2025/06/25-01:08:31.860080 46                          Options.max_open_files: -1
2025/06/25-01:08:31.860080 46                          Options.bytes_per_sync: 0
2025/06/25-01:08:31.860081 46                      Options.wal_bytes_per_sync: 0
2025/06/25-01:08:31.860081 46                   Options.strict_bytes_per_sync: 0
2025/06/25-01:08:31.860082 46       Options.compaction_readahead_size: 0
2025/06/25-01:08:31.860082 46                  Options.max_background_flushes: 1
2025/06/25-01:08:31.860083 46 Compression algorithms supported:
2025/06/25-01:08:31.860084 46 	kZSTD supported: 1
2025/06/25-01:08:31.860085 46 	kXpressCompression supported: 0
2025/06/25-01:08:31.860086 46 	kBZip2Compression supported: 0
2025/06/25-01:08:31.860086 46 	kZSTDNotFinalCompression supported: 1
2025/06/25-01:08:31.860087 46 	kLZ4Compression supported: 0
2025/06/25-01:08:31.860087 46 	kZlibCompression supported: 0
2025/06/25-01:08:31.860088 46 	kLZ4HCCompression supported: 0
2025/06/25-01:08:31.860088 46 	kSnappyCompression supported: 0
2025/06/25-01:08:31.860094 46 Fast CRC32 supported: Not supported on x86
2025/06/25-01:08:31.914992 46 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000026
2025/06/25-01:08:31.919901 46 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/06/25-01:08:31.919907 46               Options.comparator: leveldb.BytewiseComparator
2025/06/25-01:08:31.919908 46           Options.merge_operator: None
2025/06/25-01:08:31.919908 46        Options.compaction_filter: None
2025/06/25-01:08:31.919909 46        Options.compaction_filter_factory: None
2025/06/25-01:08:31.919909 46  Options.sst_partitioner_factory: None
2025/06/25-01:08:31.919910 46         Options.memtable_factory: SkipListFactory
2025/06/25-01:08:31.919910 46            Options.table_factory: BlockBasedTable
2025/06/25-01:08:31.919929 46            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fdc95f000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fdc95e40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/25-01:08:31.919930 46        Options.write_buffer_size: 67108864
2025/06/25-01:08:31.919931 46  Options.max_write_buffer_number: 2
2025/06/25-01:08:31.919932 46        Options.compression[0]: NoCompression
2025/06/25-01:08:31.919933 46        Options.compression[1]: NoCompression
2025/06/25-01:08:31.919933 46        Options.compression[2]: ZSTD
2025/06/25-01:08:31.919934 46        Options.compression[3]: ZSTD
2025/06/25-01:08:31.919934 46        Options.compression[4]: ZSTD
2025/06/25-01:08:31.919935 46                  Options.bottommost_compression: Disabled
2025/06/25-01:08:31.919935 46       Options.prefix_extractor: nullptr
2025/06/25-01:08:31.919936 46   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/25-01:08:31.919936 46             Options.num_levels: 5
2025/06/25-01:08:31.919937 46        Options.min_write_buffer_number_to_merge: 1
2025/06/25-01:08:31.919937 46     Options.max_write_buffer_number_to_maintain: 0
2025/06/25-01:08:31.919938 46     Options.max_write_buffer_size_to_maintain: 0
2025/06/25-01:08:31.919938 46            Options.bottommost_compression_opts.window_bits: -14
2025/06/25-01:08:31.919939 46                  Options.bottommost_compression_opts.level: 32767
2025/06/25-01:08:31.919939 46               Options.bottommost_compression_opts.strategy: 0
2025/06/25-01:08:31.919939 46         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/25-01:08:31.919940 46         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/25-01:08:31.919940 46         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/25-01:08:31.919941 46                  Options.bottommost_compression_opts.enabled: false
2025/06/25-01:08:31.919941 46         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/25-01:08:31.919942 46            Options.compression_opts.window_bits: -14
2025/06/25-01:08:31.919942 46                  Options.compression_opts.level: 32767
2025/06/25-01:08:31.919943 46               Options.compression_opts.strategy: 0
2025/06/25-01:08:31.919943 46         Options.compression_opts.max_dict_bytes: 0
2025/06/25-01:08:31.919944 46         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/25-01:08:31.920024 46         Options.compression_opts.parallel_threads: 1
2025/06/25-01:08:31.920025 46                  Options.compression_opts.enabled: false
2025/06/25-01:08:31.920025 46         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/25-01:08:31.920026 46      Options.level0_file_num_compaction_trigger: 4
2025/06/25-01:08:31.920027 46          Options.level0_slowdown_writes_trigger: 20
2025/06/25-01:08:31.920027 46              Options.level0_stop_writes_trigger: 36
2025/06/25-01:08:31.920028 46                   Options.target_file_size_base: 67108864
2025/06/25-01:08:31.920028 46             Options.target_file_size_multiplier: 2
2025/06/25-01:08:31.920029 46                Options.max_bytes_for_level_base: 268435456
2025/06/25-01:08:31.920029 46 Options.level_compaction_dynamic_level_bytes: 0
2025/06/25-01:08:31.920030 46          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/25-01:08:31.920031 46 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/25-01:08:31.920032 46 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/25-01:08:31.920032 46 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/25-01:08:31.920033 46 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/25-01:08:31.920033 46 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/25-01:08:31.920034 46 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/25-01:08:31.920034 46 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/25-01:08:31.920034 46       Options.max_sequential_skip_in_iterations: 8
2025/06/25-01:08:31.920035 46                    Options.max_compaction_bytes: 1677721600
2025/06/25-01:08:31.920035 46                        Options.arena_block_size: 1048576
2025/06/25-01:08:31.920036 46   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/25-01:08:31.920036 46   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/25-01:08:31.920037 46       Options.rate_limit_delay_max_milliseconds: 100
2025/06/25-01:08:31.920038 46                Options.disable_auto_compactions: 0
2025/06/25-01:08:31.920039 46                        Options.compaction_style: kCompactionStyleLevel
2025/06/25-01:08:31.920040 46                          Options.compaction_pri: kMinOverlappingRatio
2025/06/25-01:08:31.920040 46 Options.compaction_options_universal.size_ratio: 1
2025/06/25-01:08:31.920041 46 Options.compaction_options_universal.min_merge_width: 2
2025/06/25-01:08:31.920041 46 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/25-01:08:31.920042 46 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/25-01:08:31.920042 46 Options.compaction_options_universal.compression_size_percent: -1
2025/06/25-01:08:31.920043 46 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/25-01:08:31.920043 46 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/25-01:08:31.920044 46 Options.compaction_options_fifo.allow_compaction: 0
2025/06/25-01:08:31.920047 46                   Options.table_properties_collectors: 
2025/06/25-01:08:31.920048 46                   Options.inplace_update_support: 0
2025/06/25-01:08:31.920048 46                 Options.inplace_update_num_locks: 10000
2025/06/25-01:08:31.920049 46               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/25-01:08:31.920049 46               Options.memtable_whole_key_filtering: 0
2025/06/25-01:08:31.920050 46   Options.memtable_huge_page_size: 0
2025/06/25-01:08:31.920050 46                           Options.bloom_locality: 0
2025/06/25-01:08:31.920051 46                    Options.max_successive_merges: 0
2025/06/25-01:08:31.920051 46                Options.optimize_filters_for_hits: 0
2025/06/25-01:08:31.920052 46                Options.paranoid_file_checks: 0
2025/06/25-01:08:31.920052 46                Options.force_consistency_checks: 1
2025/06/25-01:08:31.920053 46                Options.report_bg_io_stats: 0
2025/06/25-01:08:31.920053 46                               Options.ttl: 2592000
2025/06/25-01:08:31.920145 46          Options.periodic_compaction_seconds: 0
2025/06/25-01:08:31.920146 46                       Options.enable_blob_files: false
2025/06/25-01:08:31.920147 46                           Options.min_blob_size: 0
2025/06/25-01:08:31.920148 46                          Options.blob_file_size: 268435456
2025/06/25-01:08:31.920149 46                   Options.blob_compression_type: NoCompression
2025/06/25-01:08:31.920149 46          Options.enable_blob_garbage_collection: false
2025/06/25-01:08:31.920150 46      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/25-01:08:31.920151 46 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/25-01:08:31.920151 46          Options.blob_compaction_readahead_size: 0
2025/06/25-01:08:32.029751 46 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000026 succeeded,manifest_file_number is 26, next_file_number is 28, last_sequence is 1689971, log_number is 22,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 8
2025/06/25-01:08:32.029758 46 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 22
2025/06/25-01:08:32.091205 46 [db/version_set.cc:4409] Creating manifest 30
2025/06/25-01:08:32.492694 46 EVENT_LOG_v1 {"time_micros": 1750813712492684, "job": 1, "event": "recovery_started", "wal_files": [27]}
2025/06/25-01:08:32.492699 46 [db/db_impl/db_impl_open.cc:888] Recovering log #27 mode 2
2025/06/25-01:08:41.215170 46 EVENT_LOG_v1 {"time_micros": 1750813721215109, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 31, "file_size": 1645, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 660, "index_size": 67, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 934, "raw_average_key_size": 54, "raw_value_size": 152, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 17, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750813721, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "70bc45a2-e82b-4473-bc30-6ae251c53e8f", "db_session_id": "98460IFQDGWL0GKLFD7U", "orig_file_number": 31}}
2025/06/25-01:08:41.225130 46 [db/version_set.cc:4409] Creating manifest 32
2025/06/25-01:08:41.392157 46 EVENT_LOG_v1 {"time_micros": 1750813721392152, "job": 1, "event": "recovery_finished"}
2025/06/25-01:08:41.501657 46 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000027.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:08:41.502095 46 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fdc93390000
2025/06/25-01:08:41.502204 47 [db/compaction/compaction_job.cc:2331] [default] [JOB 3] Compacting 4@0 + 1@1 files to L1, score 1.00
2025/06/25-01:08:41.502210 47 [db/compaction/compaction_job.cc:2337] [default] Compaction start summary: Base version 3 Base level 0, inputs: [31(1645B) 25(1484B) 19(1084B) 13(1084B)], [9(1816B)]
2025/06/25-01:08:41.502234 47 EVENT_LOG_v1 {"time_micros": 1750813721502217, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [31, 25, 19, 13], "files_L1": [9], "score": 1, "input_data_size": 7113}
2025/06/25-01:08:41.503155 46 DB pointer 0x7fdc93220000
2025/06/25-01:08:41.503439 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:08:41.503448 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9.6 total, 9.6 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/4    5.17 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/1    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      5/5    6.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9.6 total, 9.6 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 1 last_copies: 0 last_secs: 2.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(6,2.77 KB,0.000283704%)

** File Read Latency Histogram By Level [default] **
2025/06/25-01:08:41.569962 47 [db/compaction/compaction_job.cc:1937] [default] [JOB 3] Generated table #36: 52 keys, 2485 bytes
2025/06/25-01:08:41.570013 47 EVENT_LOG_v1 {"time_micros": 1750813721569978, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 36, "file_size": 2485, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 1495, "index_size": 49, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2494, "raw_average_key_size": 47, "raw_value_size": 418, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 52, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750230163, "oldest_key_time": 0, "file_creation_time": 1750813721, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "70bc45a2-e82b-4473-bc30-6ae251c53e8f", "db_session_id": "98460IFQDGWL0GKLFD7U", "orig_file_number": 36}}
2025/06/25-01:08:41.603229 47 [db/compaction/compaction_job.cc:1998] [default] [JOB 3] Compacted 4@0 + 1@1 files to L1 => 2485 bytes
2025/06/25-01:08:41.635951 47 (Original Log Time 2025/06/25-01:08:41.635885) [db/compaction/compaction_job.cc:944] [default] compacted to: files[0 1 0 0 0] max score 0.00, MB/sec: 0.1 rd, 0.0 wr, level 1, files in(4, 1) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.8) write-amplify(0.5) OK, records in: 70, records dropped: 18 output_compression: NoCompression
2025/06/25-01:08:41.635954 47 (Original Log Time 2025/06/25-01:08:41.635917) EVENT_LOG_v1 {"time_micros": 1750813721635897, "job": 3, "event": "compaction_finished", "compaction_time_micros": 68218, "compaction_time_cpu_micros": 551, "output_level": 1, "num_output_files": 1, "total_output_size": 2485, "num_input_records": 70, "num_output_records": 52, "num_subcompactions": 1, "output_compression": "NoCompression", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 1, 0, 0, 0]}
2025/06/25-01:08:41.636456 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000031.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:08:41.636469 47 EVENT_LOG_v1 {"time_micros": 1750813721636467, "job": 3, "event": "table_file_deletion", "file_number": 31}
2025/06/25-01:08:41.636914 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000025.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:08:41.636924 47 EVENT_LOG_v1 {"time_micros": 1750813721636923, "job": 3, "event": "table_file_deletion", "file_number": 25}
2025/06/25-01:08:41.637471 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000019.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:08:41.637480 47 EVENT_LOG_v1 {"time_micros": 1750813721637479, "job": 3, "event": "table_file_deletion", "file_number": 19}
2025/06/25-01:08:41.637984 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000013.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:08:41.637993 47 EVENT_LOG_v1 {"time_micros": 1750813721637992, "job": 3, "event": "table_file_deletion", "file_number": 13}
2025/06/25-01:08:41.638404 47 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000009.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/25-01:08:41.638414 47 EVENT_LOG_v1 {"time_micros": 1750813721638412, "job": 3, "event": "table_file_deletion", "file_number": 9}
2025/06/25-01:18:41.511381 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:18:41.511583 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 609.6 total, 600.0 interval
Cumulative writes: 8663 writes, 8667 keys, 6096 commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 8663 writes, 0 syncs, 8663.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8663 writes, 8667 keys, 6096 commit groups, 1.4 writes per commit group, ingest: 0.45 MB, 0.00 MB/s
Interval WAL: 8663 writes, 0 syncs, 8663.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0 2485.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 609.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 2 last_copies: 0 last_secs: 0.002832 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-01:28:41.521664 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:28:41.522181 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1209.6 total, 600.0 interval
Cumulative writes: 17K writes, 17K keys, 12K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 17K writes, 0 syncs, 17663.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6276 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1209.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 3 last_copies: 0 last_secs: 0.00293 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-01:38:41.531397 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:38:41.531594 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1809.6 total, 600.0 interval
Cumulative writes: 26K writes, 26K keys, 18K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26K writes, 0 syncs, 26663.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6216 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1809.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 4 last_copies: 0 last_secs: 0.002604 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-01:48:41.548714 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:48:41.549562 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2409.6 total, 600.0 interval
Cumulative writes: 35K writes, 35K keys, 24K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 35K writes, 0 syncs, 35663.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6288 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2409.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 5 last_copies: 0 last_secs: 0.005229 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(19,3.76 MB,0.394847%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-01:58:41.562674 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-01:58:41.562959 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3009.6 total, 600.0 interval
Cumulative writes: 44K writes, 44K keys, 31K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 44K writes, 0 syncs, 44661.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8998 writes, 8998 keys, 6496 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8998 writes, 0 syncs, 8998.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3009.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 6 last_copies: 0 last_secs: 0.004011 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-02:08:41.575675 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:08:41.576228 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3609.7 total, 600.0 interval
Cumulative writes: 53K writes, 53K keys, 37K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 53K writes, 0 syncs, 53661.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6511 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3609.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 7 last_copies: 0 last_secs: 0.005172 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-02:18:41.593776 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:18:41.594441 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4209.7 total, 600.0 interval
Cumulative writes: 62K writes, 62K keys, 44K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 62K writes, 0 syncs, 62658.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 6526 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4209.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 8 last_copies: 0 last_secs: 0.004824 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-02:28:41.608355 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:28:41.608648 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4809.7 total, 600.0 interval
Cumulative writes: 71K writes, 71K keys, 50K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 71K writes, 0 syncs, 71662.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9004 writes, 9004 keys, 6486 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9004 writes, 0 syncs, 9004.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4809.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 9 last_copies: 0 last_secs: 0.004489 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-02:38:41.620717 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:38:41.621701 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5409.7 total, 600.0 interval
Cumulative writes: 80K writes, 80K keys, 57K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 80K writes, 0 syncs, 80662.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6285 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5409.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 10 last_copies: 0 last_secs: 0.003745 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-02:48:41.638823 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:48:41.639504 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6009.7 total, 600.0 interval
Cumulative writes: 89K writes, 89K keys, 63K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 89K writes, 0 syncs, 89662.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6185 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6009.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 11 last_copies: 0 last_secs: 0.004199 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-02:58:41.650465 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-02:58:41.651023 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6609.7 total, 600.0 interval
Cumulative writes: 98K writes, 98K keys, 69K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 98K writes, 0 syncs, 98662.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6197 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6609.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 12 last_copies: 0 last_secs: 0.003697 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-03:08:41.660872 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:08:41.661211 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7209.7 total, 600.0 interval
Cumulative writes: 107K writes, 107K keys, 75K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 107K writes, 0 syncs, 107662.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6159 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7209.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 13 last_copies: 0 last_secs: 0.0021 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-03:18:41.672309 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:18:41.672704 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7809.7 total, 600.0 interval
Cumulative writes: 116K writes, 116K keys, 82K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 116K writes, 0 syncs, 116662.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6283 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7809.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 14 last_copies: 0 last_secs: 0.003165 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-03:28:41.684493 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:28:41.685782 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8409.8 total, 600.0 interval
Cumulative writes: 125K writes, 125K keys, 88K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 125K writes, 0 syncs, 125662.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6274 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8409.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 15 last_copies: 0 last_secs: 0.003444 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-03:38:41.696382 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:38:41.696662 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9009.8 total, 600.0 interval
Cumulative writes: 134K writes, 134K keys, 94K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 134K writes, 0 syncs, 134665.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9003 writes, 9003 keys, 6159 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9003 writes, 0 syncs, 9003.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9009.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 16 last_copies: 0 last_secs: 0.002645 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-03:48:41.707237 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:48:41.708209 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9609.8 total, 600.0 interval
Cumulative writes: 143K writes, 143K keys, 100K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 143K writes, 0 syncs, 143665.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6327 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9609.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 17 last_copies: 0 last_secs: 0.00288 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-03:58:41.719289 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-03:58:41.722087 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10209.8 total, 600.0 interval
Cumulative writes: 152K writes, 152K keys, 107K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 152K writes, 0 syncs, 152665.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6243 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10209.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 18 last_copies: 0 last_secs: 0.003927 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-04:08:41.733098 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:08:41.734488 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10809.8 total, 600.0 interval
Cumulative writes: 161K writes, 161K keys, 113K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 161K writes, 0 syncs, 161603.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8938 writes, 8938 keys, 6191 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8938 writes, 0 syncs, 8938.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10809.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 19 last_copies: 0 last_secs: 0.002696 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-04:18:41.745035 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:18:41.745577 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11409.8 total, 600.0 interval
Cumulative writes: 170K writes, 170K keys, 119K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 170K writes, 0 syncs, 170492.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8889 writes, 8889 keys, 6105 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8889 writes, 0 syncs, 8889.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11409.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 20 last_copies: 0 last_secs: 0.003209 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-04:28:41.758623 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:28:41.759254 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12009.8 total, 600.0 interval
Cumulative writes: 179K writes, 179K keys, 125K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 179K writes, 0 syncs, 179360.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8868 writes, 8868 keys, 6130 commit groups, 1.4 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8868 writes, 0 syncs, 8868.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12009.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 21 last_copies: 0 last_secs: 0.003783 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-04:38:41.772396 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:38:42.438569 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12609.8 total, 600.0 interval
Cumulative writes: 188K writes, 188K keys, 131K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 188K writes, 0 syncs, 188297.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8937 writes, 8937 keys, 6166 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8937 writes, 0 syncs, 8937.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12609.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 22 last_copies: 0 last_secs: 0.003418 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-04:48:42.451974 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:48:42.452747 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13210.5 total, 600.7 interval
Cumulative writes: 197K writes, 197K keys, 137K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 197K writes, 0 syncs, 197111.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8814 writes, 8814 keys, 6073 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8814 writes, 0 syncs, 8814.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13210.5 total, 600.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 23 last_copies: 0 last_secs: 0.005114 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-04:58:42.468707 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-04:58:42.469300 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 13810.5 total, 600.0 interval
Cumulative writes: 206K writes, 206K keys, 143K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 206K writes, 0 syncs, 206111.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6185 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 13810.5 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 24 last_copies: 0 last_secs: 0.004591 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-05:08:42.485752 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:08:42.486165 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 14410.6 total, 600.0 interval
Cumulative writes: 215K writes, 215K keys, 150K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 215K writes, 0 syncs, 215108.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 6160 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 14410.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 25 last_copies: 0 last_secs: 0.005672 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-05:18:42.510537 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:18:42.511589 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15010.6 total, 600.0 interval
Cumulative writes: 224K writes, 224K keys, 156K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 224K writes, 0 syncs, 224105.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 6145 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15010.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 26 last_copies: 0 last_secs: 0.003762 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-05:28:42.521707 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:28:42.522153 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 15610.6 total, 600.0 interval
Cumulative writes: 233K writes, 233K keys, 162K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 233K writes, 0 syncs, 233090.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8985 writes, 8985 keys, 6361 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8985 writes, 0 syncs, 8985.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 15610.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 27 last_copies: 0 last_secs: 0.002719 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-05:38:42.533025 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:38:42.533662 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16210.6 total, 600.0 interval
Cumulative writes: 242K writes, 242K keys, 168K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 242K writes, 0 syncs, 242087.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 6362 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16210.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 28 last_copies: 0 last_secs: 0.002693 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-05:48:42.544331 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:48:42.544672 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 16810.6 total, 600.0 interval
Cumulative writes: 251K writes, 251K keys, 175K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 251K writes, 0 syncs, 251087.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6235 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 16810.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 29 last_copies: 0 last_secs: 0.003164 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-05:58:42.555837 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-05:58:42.556303 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 17410.6 total, 600.0 interval
Cumulative writes: 260K writes, 260K keys, 181K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 260K writes, 0 syncs, 260087.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6165 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 17410.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 30 last_copies: 0 last_secs: 0.003807 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-06:08:42.567178 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:08:42.567777 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18010.6 total, 600.0 interval
Cumulative writes: 269K writes, 269K keys, 187K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 269K writes, 0 syncs, 269084.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8997 writes, 8997 keys, 6118 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8997 writes, 0 syncs, 8997.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18010.6 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 31 last_copies: 0 last_secs: 0.003538 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-06:18:42.582379 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:18:42.584193 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 18610.7 total, 600.0 interval
Cumulative writes: 278K writes, 278K keys, 193K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 278K writes, 0 syncs, 278015.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8931 writes, 8931 keys, 6021 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8931 writes, 0 syncs, 8931.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 18610.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 32 last_copies: 0 last_secs: 0.005884 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-06:28:42.601874 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:28:42.602658 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19210.7 total, 600.0 interval
Cumulative writes: 286K writes, 286K keys, 199K commit groups, 1.4 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 286K writes, 0 syncs, 286982.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8967 writes, 8967 keys, 6159 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8967 writes, 0 syncs, 8967.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19210.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 33 last_copies: 0 last_secs: 0.00544 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-06:38:42.614538 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:38:42.616123 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 19810.7 total, 600.0 interval
Cumulative writes: 295K writes, 295K keys, 205K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 295K writes, 0 syncs, 295976.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8994 writes, 8994 keys, 6084 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8994 writes, 0 syncs, 8994.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 19810.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 34 last_copies: 0 last_secs: 0.003514 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-06:48:42.629154 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:48:42.629762 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20410.7 total, 600.0 interval
Cumulative writes: 304K writes, 304K keys, 211K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 304K writes, 0 syncs, 304967.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8991 writes, 8991 keys, 6142 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8991 writes, 0 syncs, 8991.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20410.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 35 last_copies: 0 last_secs: 0.003934 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-06:58:42.639803 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-06:58:42.640130 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21010.7 total, 600.0 interval
Cumulative writes: 313K writes, 313K keys, 218K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 313K writes, 0 syncs, 313935.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8968 writes, 8968 keys, 6213 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8968 writes, 0 syncs, 8968.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21010.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 36 last_copies: 0 last_secs: 0.003018 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-07:08:42.654006 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:08:42.654627 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21610.7 total, 600.0 interval
Cumulative writes: 322K writes, 322K keys, 224K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 322K writes, 0 syncs, 322909.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8974 writes, 8974 keys, 6194 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8974 writes, 0 syncs, 8974.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21610.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 37 last_copies: 0 last_secs: 0.002993 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-07:18:42.665825 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:18:42.666400 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22210.7 total, 600.0 interval
Cumulative writes: 331K writes, 331K keys, 230K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 331K writes, 0 syncs, 331834.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8925 writes, 8925 keys, 6163 commit groups, 1.4 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8925 writes, 0 syncs, 8925.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22210.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 38 last_copies: 0 last_secs: 0.00331 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-07:28:42.676767 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:28:42.676950 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22810.8 total, 600.0 interval
Cumulative writes: 340K writes, 340K keys, 236K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 340K writes, 0 syncs, 340797.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8963 writes, 8963 keys, 6111 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8963 writes, 0 syncs, 8963.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22810.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 39 last_copies: 0 last_secs: 0.003445 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-07:38:42.686400 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:38:42.686875 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 23410.8 total, 600.0 interval
Cumulative writes: 349K writes, 349K keys, 242K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 349K writes, 0 syncs, 349795.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8998 writes, 8998 keys, 6286 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8998 writes, 0 syncs, 8998.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 23410.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 40 last_copies: 0 last_secs: 0.00317 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-07:48:42.695823 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:48:42.696316 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24010.8 total, 600.0 interval
Cumulative writes: 358K writes, 358K keys, 248K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 358K writes, 0 syncs, 358795.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6194 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24010.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 41 last_copies: 0 last_secs: 0.003359 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-07:58:42.706597 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-07:58:42.707153 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24610.8 total, 600.0 interval
Cumulative writes: 367K writes, 367K keys, 255K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 367K writes, 0 syncs, 367789.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8994 writes, 8994 keys, 6173 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8994 writes, 0 syncs, 8994.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24610.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 42 last_copies: 0 last_secs: 0.003237 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-08:08:42.719399 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:08:42.720843 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 25210.8 total, 600.0 interval
Cumulative writes: 376K writes, 376K keys, 261K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 376K writes, 0 syncs, 376751.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8962 writes, 8962 keys, 6135 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8962 writes, 0 syncs, 8962.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25210.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 43 last_copies: 0 last_secs: 0.003908 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-08:18:42.736129 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:18:42.736539 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 25810.8 total, 600.0 interval
Cumulative writes: 385K writes, 385K keys, 267K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 385K writes, 0 syncs, 385715.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8964 writes, 8964 keys, 6168 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8964 writes, 0 syncs, 8964.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 25810.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 44 last_copies: 0 last_secs: 0.006465 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-08:28:42.745241 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:28:42.745773 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 26410.8 total, 600.0 interval
Cumulative writes: 394K writes, 394K keys, 273K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 394K writes, 0 syncs, 394703.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8988 writes, 8988 keys, 6165 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8988 writes, 0 syncs, 8988.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 26410.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 45 last_copies: 0 last_secs: 0.002831 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-08:38:42.751977 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:38:42.752246 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27010.8 total, 600.0 interval
Cumulative writes: 403K writes, 403K keys, 279K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 403K writes, 0 syncs, 403665.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8962 writes, 8962 keys, 6295 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8962 writes, 0 syncs, 8962.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27010.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 46 last_copies: 0 last_secs: 0.001443 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-08:48:42.762471 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:48:42.762869 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27610.8 total, 600.0 interval
Cumulative writes: 412K writes, 412K keys, 286K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 412K writes, 0 syncs, 412666.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9001 writes, 9001 keys, 6183 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9001 writes, 0 syncs, 9001.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27610.8 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 47 last_copies: 0 last_secs: 0.003022 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-08:58:42.771594 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-08:58:42.772096 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 28210.8 total, 600.0 interval
Cumulative writes: 421K writes, 421K keys, 292K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 421K writes, 0 syncs, 421666.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6136 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 28210.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 48 last_copies: 0 last_secs: 0.002777 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-09:08:42.780194 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:08:42.780647 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 28810.9 total, 600.0 interval
Cumulative writes: 430K writes, 430K keys, 298K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 430K writes, 0 syncs, 430652.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8986 writes, 8986 keys, 6229 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8986 writes, 0 syncs, 8986.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 28810.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 49 last_copies: 0 last_secs: 0.002386 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-09:18:42.796317 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:18:42.796805 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 29410.9 total, 600.0 interval
Cumulative writes: 439K writes, 439K keys, 304K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 439K writes, 0 syncs, 439652.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6425 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 29410.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 50 last_copies: 0 last_secs: 0.005489 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-09:28:42.806290 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:28:42.806608 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 30010.9 total, 600.0 interval
Cumulative writes: 448K writes, 448K keys, 311K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 448K writes, 0 syncs, 448652.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6576 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 30010.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 51 last_copies: 0 last_secs: 0.003157 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-09:38:42.814545 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:38:42.814765 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 30610.9 total, 600.0 interval
Cumulative writes: 457K writes, 457K keys, 317K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 457K writes, 0 syncs, 457655.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9003 writes, 9003 keys, 6367 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9003 writes, 0 syncs, 9003.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 30610.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 52 last_copies: 0 last_secs: 0.002446 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-09:48:42.823854 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:48:42.824259 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 31210.9 total, 600.0 interval
Cumulative writes: 466K writes, 466K keys, 324K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 466K writes, 0 syncs, 466655.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6285 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 31210.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 53 last_copies: 0 last_secs: 0.003152 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-09:58:42.833719 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-09:58:42.834229 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 31810.9 total, 600.0 interval
Cumulative writes: 475K writes, 475K keys, 330K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 475K writes, 0 syncs, 475655.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6360 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 31810.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 54 last_copies: 0 last_secs: 0.003131 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-10:08:42.845688 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:08:42.846773 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 32410.9 total, 600.0 interval
Cumulative writes: 484K writes, 484K keys, 336K commit groups, 1.4 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 484K writes, 0 syncs, 484655.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6346 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 32410.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 55 last_copies: 0 last_secs: 0.003623 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-10:18:42.854637 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:18:42.855423 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 33010.9 total, 600.0 interval
Cumulative writes: 493K writes, 493K keys, 342K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 493K writes, 0 syncs, 493509.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8854 writes, 8854 keys, 6113 commit groups, 1.4 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8854 writes, 0 syncs, 8854.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 33010.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 56 last_copies: 0 last_secs: 0.002982 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-10:28:42.864871 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:28:42.865467 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 33610.9 total, 600.0 interval
Cumulative writes: 502K writes, 502K keys, 348K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 502K writes, 0 syncs, 502233.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8724 writes, 8724 keys, 6069 commit groups, 1.4 writes per commit group, ingest: 0.45 MB, 0.00 MB/s
Interval WAL: 8724 writes, 0 syncs, 8724.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 33610.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 57 last_copies: 0 last_secs: 0.003209 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-10:38:42.872180 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:38:42.872524 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 34210.9 total, 600.0 interval
Cumulative writes: 511K writes, 511K keys, 355K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 511K writes, 0 syncs, 511107.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8874 writes, 8874 keys, 6062 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8874 writes, 0 syncs, 8874.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 34211.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 58 last_copies: 0 last_secs: 0.002357 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-10:48:42.881603 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:48:42.881881 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 34811.0 total, 600.0 interval
Cumulative writes: 520K writes, 520K keys, 361K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 520K writes, 0 syncs, 520110.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9003 writes, 9003 keys, 6144 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9003 writes, 0 syncs, 9003.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 34811.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 59 last_copies: 0 last_secs: 0.003025 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-10:58:42.891497 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-10:58:42.891998 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 35411.0 total, 600.0 interval
Cumulative writes: 529K writes, 529K keys, 367K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 529K writes, 0 syncs, 529110.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 9000 writes, 9000 keys, 6290 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 9000 writes, 0 syncs, 9000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 35411.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 60 last_copies: 0 last_secs: 0.002954 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-11:08:42.900984 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:08:42.901715 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 36011.0 total, 600.0 interval
Cumulative writes: 538K writes, 538K keys, 373K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 538K writes, 0 syncs, 538098.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8988 writes, 8988 keys, 6291 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8988 writes, 0 syncs, 8988.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 36011.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 61 last_copies: 0 last_secs: 0.003021 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-11:18:42.911621 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:18:42.912577 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 36611.0 total, 600.0 interval
Cumulative writes: 546K writes, 546K keys, 379K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 546K writes, 0 syncs, 546973.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8875 writes, 8875 keys, 6095 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8875 writes, 0 syncs, 8875.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 36611.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 62 last_copies: 0 last_secs: 0.002911 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-11:28:42.921927 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:28:42.923158 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 37211.0 total, 600.0 interval
Cumulative writes: 555K writes, 555K keys, 385K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 555K writes, 0 syncs, 555929.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8956 writes, 8956 keys, 5981 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8956 writes, 0 syncs, 8956.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 37211.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 63 last_copies: 0 last_secs: 0.003759 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-11:38:42.934043 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:38:42.939834 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 37811.0 total, 600.0 interval
Cumulative writes: 564K writes, 564K keys, 391K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 564K writes, 0 syncs, 564905.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8976 writes, 8976 keys, 6017 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8976 writes, 0 syncs, 8976.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 37811.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 64 last_copies: 0 last_secs: 0.003465 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-11:48:42.958494 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:48:42.958819 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 38411.0 total, 600.0 interval
Cumulative writes: 573K writes, 573K keys, 397K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 573K writes, 0 syncs, 573521.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8616 writes, 8616 keys, 6003 commit groups, 1.4 writes per commit group, ingest: 0.45 MB, 0.00 MB/s
Interval WAL: 8616 writes, 0 syncs, 8616.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 38411.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 65 last_copies: 0 last_secs: 0.006049 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-11:58:42.970537 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-11:58:42.971353 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 39011.0 total, 600.0 interval
Cumulative writes: 582K writes, 582K keys, 404K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 582K writes, 0 syncs, 582395.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8874 writes, 8874 keys, 6132 commit groups, 1.4 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8874 writes, 0 syncs, 8874.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 39011.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 66 last_copies: 0 last_secs: 0.004609 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-12:08:42.981969 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:08:42.982604 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 39611.1 total, 600.0 interval
Cumulative writes: 591K writes, 591K keys, 410K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 591K writes, 0 syncs, 591347.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8952 writes, 8952 keys, 6132 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8952 writes, 0 syncs, 8952.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 39611.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 67 last_copies: 0 last_secs: 0.003296 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-12:18:42.996549 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:18:42.998535 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 40211.1 total, 600.0 interval
Cumulative writes: 600K writes, 600K keys, 416K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 600K writes, 0 syncs, 600302.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8955 writes, 8955 keys, 6206 commit groups, 1.4 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8955 writes, 0 syncs, 8955.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 40211.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 68 last_copies: 0 last_secs: 0.004835 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-12:28:43.010530 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:28:43.011846 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 40811.1 total, 600.0 interval
Cumulative writes: 609K writes, 609K keys, 422K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 609K writes, 0 syncs, 609260.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8958 writes, 8958 keys, 6085 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8958 writes, 0 syncs, 8958.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 40811.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 69 last_copies: 0 last_secs: 0.00477 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-12:38:43.028855 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:38:43.030868 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 41411.1 total, 600.0 interval
Cumulative writes: 618K writes, 618K keys, 428K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 618K writes, 0 syncs, 618245.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8985 writes, 8985 keys, 6002 commit groups, 1.5 writes per commit group, ingest: 0.47 MB, 0.00 MB/s
Interval WAL: 8985 writes, 0 syncs, 8985.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 41411.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 70 last_copies: 0 last_secs: 0.00477 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-12:48:43.043141 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:48:43.047733 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 42011.1 total, 600.0 interval
Cumulative writes: 627K writes, 627K keys, 434K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 627K writes, 0 syncs, 627170.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8925 writes, 8925 keys, 6063 commit groups, 1.5 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8925 writes, 0 syncs, 8925.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 42011.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 71 last_copies: 0 last_secs: 0.003617 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/25-12:58:43.058677 65 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/25-12:58:43.059374 65 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 42611.1 total, 600.0 interval
Cumulative writes: 636K writes, 636K keys, 440K commit groups, 1.4 writes per commit group, ingest: 0.03 GB, 0.00 MB/s
Cumulative WAL: 636K writes, 0 syncs, 636077.00 writes per sync, written: 0.03 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 8907 writes, 8907 keys, 6143 commit groups, 1.4 writes per commit group, ingest: 0.46 MB, 0.00 MB/s
Interval WAL: 8907 writes, 0 syncs, 8907.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0
  L1      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.5      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
 Sum      1/0    2.43 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.5      0.0      0.0      0.20              0.00         2    0.099      70     18       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.068      70     18       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.13              0.00         1    0.130       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 42611.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.2 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fdc95e40010#8 capacity: 951.98 MB collections: 72 last_copies: 0 last_secs: 0.003451 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(21,4.70 MB,0.493341%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
