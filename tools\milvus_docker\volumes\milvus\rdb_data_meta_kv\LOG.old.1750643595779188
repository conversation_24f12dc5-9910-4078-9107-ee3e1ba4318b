2025/06/20-08:13:57.033505 37 RocksDB version: 6.29.5
2025/06/20-08:13:57.034002 37 Git sha 0
2025/06/20-08:13:57.034005 37 Compile date 2024-11-15 11:22:58
2025/06/20-08:13:57.034009 37 DB SUMMARY
2025/06/20-08:13:57.034010 37 DB Session ID:  01XBVBB5Q8PHIN08ZKZQ
2025/06/20-08:13:57.034808 37 CURRENT file:  CURRENT
2025/06/20-08:13:57.034810 37 IDENTITY file:  IDENTITY
2025/06/20-08:13:57.035052 37 MANIFEST file:  MANIFEST-000004 size: 338 Bytes
2025/06/20-08:13:57.035054 37 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 1, files: 000009.sst 
2025/06/20-08:13:57.035056 37 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000008.log size: 16137824 ; 
2025/06/20-08:13:57.035057 37                         Options.error_if_exists: 0
2025/06/20-08:13:57.035058 37                       Options.create_if_missing: 1
2025/06/20-08:13:57.035059 37                         Options.paranoid_checks: 1
2025/06/20-08:13:57.035059 37             Options.flush_verify_memtable_count: 1
2025/06/20-08:13:57.035060 37                               Options.track_and_verify_wals_in_manifest: 0
2025/06/20-08:13:57.035060 37                                     Options.env: 0x7fbc8ea59d00
2025/06/20-08:13:57.035061 37                                      Options.fs: PosixFileSystem
2025/06/20-08:13:57.035062 37                                Options.info_log: 0x7fbb91890050
2025/06/20-08:13:57.035062 37                Options.max_file_opening_threads: 16
2025/06/20-08:13:57.035063 37                              Options.statistics: (nil)
2025/06/20-08:13:57.035063 37                               Options.use_fsync: 0
2025/06/20-08:13:57.035064 37                       Options.max_log_file_size: 0
2025/06/20-08:13:57.035064 37                  Options.max_manifest_file_size: 1073741824
2025/06/20-08:13:57.035065 37                   Options.log_file_time_to_roll: 0
2025/06/20-08:13:57.035065 37                       Options.keep_log_file_num: 1000
2025/06/20-08:13:57.035066 37                    Options.recycle_log_file_num: 0
2025/06/20-08:13:57.035066 37                         Options.allow_fallocate: 1
2025/06/20-08:13:57.035067 37                        Options.allow_mmap_reads: 0
2025/06/20-08:13:57.035067 37                       Options.allow_mmap_writes: 0
2025/06/20-08:13:57.035068 37                        Options.use_direct_reads: 0
2025/06/20-08:13:57.035068 37                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/20-08:13:57.035069 37          Options.create_missing_column_families: 0
2025/06/20-08:13:57.035069 37                              Options.db_log_dir: 
2025/06/20-08:13:57.035069 37                                 Options.wal_dir: 
2025/06/20-08:13:57.035070 37                Options.table_cache_numshardbits: 6
2025/06/20-08:13:57.035070 37                         Options.WAL_ttl_seconds: 0
2025/06/20-08:13:57.035071 37                       Options.WAL_size_limit_MB: 0
2025/06/20-08:13:57.035071 37                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/20-08:13:57.035072 37             Options.manifest_preallocation_size: 4194304
2025/06/20-08:13:57.035072 37                     Options.is_fd_close_on_exec: 1
2025/06/20-08:13:57.035073 37                   Options.advise_random_on_open: 1
2025/06/20-08:13:57.035073 37                   Options.experimental_mempurge_threshold: 0.000000
2025/06/20-08:13:57.035340 37                    Options.db_write_buffer_size: 0
2025/06/20-08:13:57.035343 37                    Options.write_buffer_manager: 0x7fbb980600a0
2025/06/20-08:13:57.035344 37         Options.access_hint_on_compaction_start: 1
2025/06/20-08:13:57.035345 37  Options.new_table_reader_for_compaction_inputs: 0
2025/06/20-08:13:57.035345 37           Options.random_access_max_buffer_size: 1048576
2025/06/20-08:13:57.035346 37                      Options.use_adaptive_mutex: 0
2025/06/20-08:13:57.035346 37                            Options.rate_limiter: (nil)
2025/06/20-08:13:57.035359 37     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/20-08:13:57.035360 37                       Options.wal_recovery_mode: 2
2025/06/20-08:13:57.045757 37                  Options.enable_thread_tracking: 0
2025/06/20-08:13:57.045763 37                  Options.enable_pipelined_write: 0
2025/06/20-08:13:57.045764 37                  Options.unordered_write: 0
2025/06/20-08:13:57.045765 37         Options.allow_concurrent_memtable_write: 1
2025/06/20-08:13:57.045765 37      Options.enable_write_thread_adaptive_yield: 1
2025/06/20-08:13:57.045766 37             Options.write_thread_max_yield_usec: 100
2025/06/20-08:13:57.045766 37            Options.write_thread_slow_yield_usec: 3
2025/06/20-08:13:57.045767 37                               Options.row_cache: None
2025/06/20-08:13:57.045767 37                              Options.wal_filter: None
2025/06/20-08:13:57.045768 37             Options.avoid_flush_during_recovery: 0
2025/06/20-08:13:57.045768 37             Options.allow_ingest_behind: 0
2025/06/20-08:13:57.045769 37             Options.preserve_deletes: 0
2025/06/20-08:13:57.045769 37             Options.two_write_queues: 0
2025/06/20-08:13:57.045770 37             Options.manual_wal_flush: 0
2025/06/20-08:13:57.045770 37             Options.atomic_flush: 0
2025/06/20-08:13:57.045770 37             Options.avoid_unnecessary_blocking_io: 0
2025/06/20-08:13:57.045771 37                 Options.persist_stats_to_disk: 0
2025/06/20-08:13:57.045771 37                 Options.write_dbid_to_manifest: 0
2025/06/20-08:13:57.045772 37                 Options.log_readahead_size: 0
2025/06/20-08:13:57.045772 37                 Options.file_checksum_gen_factory: Unknown
2025/06/20-08:13:57.045773 37                 Options.best_efforts_recovery: 0
2025/06/20-08:13:57.045773 37                Options.max_bgerror_resume_count: 2147483647
2025/06/20-08:13:57.045774 37            Options.bgerror_resume_retry_interval: 1000000
2025/06/20-08:13:57.045774 37             Options.allow_data_in_errors: 0
2025/06/20-08:13:57.045775 37             Options.db_host_id: __hostname__
2025/06/20-08:13:57.045777 37             Options.max_background_jobs: 2
2025/06/20-08:13:57.045778 37             Options.max_background_compactions: -1
2025/06/20-08:13:57.045778 37             Options.max_subcompactions: 1
2025/06/20-08:13:57.045779 37             Options.avoid_flush_during_shutdown: 0
2025/06/20-08:13:57.045779 37           Options.writable_file_max_buffer_size: 1048576
2025/06/20-08:13:57.045780 37             Options.delayed_write_rate : 16777216
2025/06/20-08:13:57.045780 37             Options.max_total_wal_size: 0
2025/06/20-08:13:57.045781 37             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/20-08:13:57.045781 37                   Options.stats_dump_period_sec: 600
2025/06/20-08:13:57.045782 37                 Options.stats_persist_period_sec: 600
2025/06/20-08:13:57.045782 37                 Options.stats_history_buffer_size: 1048576
2025/06/20-08:13:57.045783 37                          Options.max_open_files: -1
2025/06/20-08:13:57.045783 37                          Options.bytes_per_sync: 0
2025/06/20-08:13:57.045784 37                      Options.wal_bytes_per_sync: 0
2025/06/20-08:13:57.045784 37                   Options.strict_bytes_per_sync: 0
2025/06/20-08:13:57.045784 37       Options.compaction_readahead_size: 0
2025/06/20-08:13:57.045785 37                  Options.max_background_flushes: 1
2025/06/20-08:13:57.045785 37 Compression algorithms supported:
2025/06/20-08:13:57.045787 37 	kZSTD supported: 1
2025/06/20-08:13:57.045788 37 	kXpressCompression supported: 0
2025/06/20-08:13:57.045789 37 	kBZip2Compression supported: 0
2025/06/20-08:13:57.045789 37 	kZSTDNotFinalCompression supported: 1
2025/06/20-08:13:57.045790 37 	kLZ4Compression supported: 0
2025/06/20-08:13:57.045790 37 	kZlibCompression supported: 0
2025/06/20-08:13:57.045791 37 	kLZ4HCCompression supported: 0
2025/06/20-08:13:57.045791 37 	kSnappyCompression supported: 0
2025/06/20-08:13:57.045795 37 Fast CRC32 supported: Not supported on x86
2025/06/20-08:13:57.053248 37 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000004
2025/06/20-08:13:57.058211 37 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/06/20-08:13:57.058217 37               Options.comparator: leveldb.BytewiseComparator
2025/06/20-08:13:57.058218 37           Options.merge_operator: None
2025/06/20-08:13:57.058218 37        Options.compaction_filter: None
2025/06/20-08:13:57.058219 37        Options.compaction_filter_factory: None
2025/06/20-08:13:57.058219 37  Options.sst_partitioner_factory: None
2025/06/20-08:13:57.058220 37         Options.memtable_factory: SkipListFactory
2025/06/20-08:13:57.058220 37            Options.table_factory: BlockBasedTable
2025/06/20-08:13:57.058239 37            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fbb980000e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fbb98060010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 998224773
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/06/20-08:13:57.058240 37        Options.write_buffer_size: 67108864
2025/06/20-08:13:57.058241 37  Options.max_write_buffer_number: 2
2025/06/20-08:13:57.058242 37        Options.compression[0]: NoCompression
2025/06/20-08:13:57.058243 37        Options.compression[1]: NoCompression
2025/06/20-08:13:57.058243 37        Options.compression[2]: ZSTD
2025/06/20-08:13:57.058244 37        Options.compression[3]: ZSTD
2025/06/20-08:13:57.058244 37        Options.compression[4]: ZSTD
2025/06/20-08:13:57.058245 37                  Options.bottommost_compression: Disabled
2025/06/20-08:13:57.058245 37       Options.prefix_extractor: nullptr
2025/06/20-08:13:57.058246 37   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/20-08:13:57.058246 37             Options.num_levels: 5
2025/06/20-08:13:57.058247 37        Options.min_write_buffer_number_to_merge: 1
2025/06/20-08:13:57.058247 37     Options.max_write_buffer_number_to_maintain: 0
2025/06/20-08:13:57.058248 37     Options.max_write_buffer_size_to_maintain: 0
2025/06/20-08:13:57.058248 37            Options.bottommost_compression_opts.window_bits: -14
2025/06/20-08:13:57.058249 37                  Options.bottommost_compression_opts.level: 32767
2025/06/20-08:13:57.058249 37               Options.bottommost_compression_opts.strategy: 0
2025/06/20-08:13:57.058250 37         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/20-08:13:57.058250 37         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/20-08:13:57.058251 37         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/20-08:13:57.058251 37                  Options.bottommost_compression_opts.enabled: false
2025/06/20-08:13:57.058252 37         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/20-08:13:57.058252 37            Options.compression_opts.window_bits: -14
2025/06/20-08:13:57.058253 37                  Options.compression_opts.level: 32767
2025/06/20-08:13:57.058253 37               Options.compression_opts.strategy: 0
2025/06/20-08:13:57.058253 37         Options.compression_opts.max_dict_bytes: 0
2025/06/20-08:13:57.058254 37         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/20-08:13:57.058254 37         Options.compression_opts.parallel_threads: 1
2025/06/20-08:13:57.058336 37                  Options.compression_opts.enabled: false
2025/06/20-08:13:57.058337 37         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/20-08:13:57.058338 37      Options.level0_file_num_compaction_trigger: 4
2025/06/20-08:13:57.058338 37          Options.level0_slowdown_writes_trigger: 20
2025/06/20-08:13:57.058339 37              Options.level0_stop_writes_trigger: 36
2025/06/20-08:13:57.058339 37                   Options.target_file_size_base: 67108864
2025/06/20-08:13:57.058340 37             Options.target_file_size_multiplier: 2
2025/06/20-08:13:57.058340 37                Options.max_bytes_for_level_base: 268435456
2025/06/20-08:13:57.058341 37 Options.level_compaction_dynamic_level_bytes: 0
2025/06/20-08:13:57.058341 37          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/20-08:13:57.058343 37 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/20-08:13:57.058343 37 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/20-08:13:57.058344 37 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/20-08:13:57.058344 37 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/20-08:13:57.058345 37 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/20-08:13:57.058345 37 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/20-08:13:57.058346 37 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/20-08:13:57.058346 37       Options.max_sequential_skip_in_iterations: 8
2025/06/20-08:13:57.058347 37                    Options.max_compaction_bytes: 1677721600
2025/06/20-08:13:57.058347 37                        Options.arena_block_size: 1048576
2025/06/20-08:13:57.058348 37   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/20-08:13:57.058348 37   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/20-08:13:57.058349 37       Options.rate_limit_delay_max_milliseconds: 100
2025/06/20-08:13:57.058349 37                Options.disable_auto_compactions: 0
2025/06/20-08:13:57.058351 37                        Options.compaction_style: kCompactionStyleLevel
2025/06/20-08:13:57.058351 37                          Options.compaction_pri: kMinOverlappingRatio
2025/06/20-08:13:57.058352 37 Options.compaction_options_universal.size_ratio: 1
2025/06/20-08:13:57.058352 37 Options.compaction_options_universal.min_merge_width: 2
2025/06/20-08:13:57.058353 37 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/20-08:13:57.058353 37 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/20-08:13:57.058354 37 Options.compaction_options_universal.compression_size_percent: -1
2025/06/20-08:13:57.058355 37 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/20-08:13:57.058355 37 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/20-08:13:57.058356 37 Options.compaction_options_fifo.allow_compaction: 0
2025/06/20-08:13:57.058359 37                   Options.table_properties_collectors: 
2025/06/20-08:13:57.058360 37                   Options.inplace_update_support: 0
2025/06/20-08:13:57.058360 37                 Options.inplace_update_num_locks: 10000
2025/06/20-08:13:57.058361 37               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/20-08:13:57.058362 37               Options.memtable_whole_key_filtering: 0
2025/06/20-08:13:57.058362 37   Options.memtable_huge_page_size: 0
2025/06/20-08:13:57.058362 37                           Options.bloom_locality: 0
2025/06/20-08:13:57.058363 37                    Options.max_successive_merges: 0
2025/06/20-08:13:57.058363 37                Options.optimize_filters_for_hits: 0
2025/06/20-08:13:57.058364 37                Options.paranoid_file_checks: 0
2025/06/20-08:13:57.058364 37                Options.force_consistency_checks: 1
2025/06/20-08:13:57.058365 37                Options.report_bg_io_stats: 0
2025/06/20-08:13:57.058365 37                               Options.ttl: 2592000
2025/06/20-08:13:57.058366 37          Options.periodic_compaction_seconds: 0
2025/06/20-08:13:57.061810 37                       Options.enable_blob_files: false
2025/06/20-08:13:57.061814 37                           Options.min_blob_size: 0
2025/06/20-08:13:57.061815 37                          Options.blob_file_size: 268435456
2025/06/20-08:13:57.061816 37                   Options.blob_compression_type: NoCompression
2025/06/20-08:13:57.061816 37          Options.enable_blob_garbage_collection: false
2025/06/20-08:13:57.061817 37      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/20-08:13:57.061819 37 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/20-08:13:57.061819 37          Options.blob_compaction_readahead_size: 0
2025/06/20-08:13:57.102109 37 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 11, last_sequence is 755647, log_number is 8,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 8
2025/06/20-08:13:57.102116 37 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 8
2025/06/20-08:13:57.109568 37 [db/version_set.cc:4409] Creating manifest 12
2025/06/20-08:13:57.222630 37 EVENT_LOG_v1 {"time_micros": 1750407237222619, "job": 1, "event": "recovery_started", "wal_files": [8]}
2025/06/20-08:13:57.222635 37 [db/db_impl/db_impl_open.cc:888] Recovering log #8 mode 2
2025/06/20-08:13:58.229907 37 EVENT_LOG_v1 {"time_micros": 1750407238229879, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 13, "file_size": 1084, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 116, "index_size": 52, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 128, "raw_average_key_size": 42, "raw_value_size": 21, "raw_average_value_size": 7, "num_data_blocks": 1, "num_entries": 3, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1750407238, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "70bc45a2-e82b-4473-bc30-6ae251c53e8f", "db_session_id": "01XBVBB5Q8PHIN08ZKZQ", "orig_file_number": 13}}
2025/06/20-08:13:58.230146 37 [db/version_set.cc:4409] Creating manifest 14
2025/06/20-08:13:58.323103 37 EVENT_LOG_v1 {"time_micros": 1750407238323098, "job": 1, "event": "recovery_finished"}
2025/06/20-08:13:58.448049 37 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000008.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/20-08:13:58.450664 37 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fbb91990000
2025/06/20-08:13:58.451354 37 DB pointer 0x7fbb91820000
2025/06/20-08:13:58.453332 63 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/20-08:13:58.453342 63 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1.4 total, 1.4 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.06 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.06              0.00         1    0.057       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0    2.83 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.06              0.00         1    0.057       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.06              0.00         1    0.057       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.06              0.00         1    0.057       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1.4 total, 1.4 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fbb98060010#8 capacity: 951.98 MB collections: 1 last_copies: 0 last_secs: 4.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/20-08:23:58.462840 63 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/06/20-08:23:58.463390 63 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 601.4 total, 600.0 interval
Cumulative writes: 5707 writes, 5707 keys, 5253 commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 5707 writes, 0 syncs, 5707.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5707 writes, 5707 keys, 5253 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 5707 writes, 0 syncs, 5707.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.06 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.06              0.00         1    0.057       0      0       0.0       0.0
  L1      1/0    1.77 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      2/0    2.83 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.06              0.00         1    0.057       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.06              0.00         1    0.057       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 601.4 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fbb98060010#8 capacity: 951.98 MB collections: 2 last_copies: 0 last_secs: 0.003079 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,311.62 KB,0.0319671%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
