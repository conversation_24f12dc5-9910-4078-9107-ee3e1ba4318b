import os
import sys
import json
from tqdm import tqdm

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.fusion360_extractor import Fusion360Extractor
from src.utils.llm_utils import call_llm, call_mllm
from src.utils.file_utils import get_fusion360_test_paths
from src.models.LLM import MultiProviderLLM
from src.config import Config

mllm_system_prompt = """
    你是一名兼具机械设计与技术写作背景的资深工程师。请根据提供的装配渲染图和结构化信息，为非专业读者生成一段准确、通俗、连贯的中文文字说明，突出整体外形、主要组成、功能用途和材料特色。"
"""

# 初始化大模型实例（支持多供应商）
llm = MultiProviderLLM(
    provider="gemini",
    api_url="https://generativelanguage.googleapis.com",
    api_key="AIzaSyDjMk2PlUwbhZfoQDZ9NMhH6X4zsZNiak8",
    model="gemini-2.5-flash-preview-05-20",
)

def describe_assembly(json_path: str, image_path: str):
    """
    通过cad_model.to_json()的json信息和渲染图片，得到装配体的文本描述
    """
    extractor = Fusion360Extractor(json_path, extract_shape_embedding=False)
    cad_model = extractor.convert()
    cad_model_json = cad_model.to_json()

    mllm_prompt = f"""
**图片**：<IMAGE>
**装配体结构化信息**：
{cad_model_json}

**命名清洗规则**
- 忽略"Untitled""Component+数字""Body+数字"等无意义名称。
- 必要时基于图像与上下文推断更常见的称谓（如"机身""轮组"等）。

**输出格式要求**
请以如下JSON格式输出：
```
{{
  "assembly_name": "（推断出的装配体名称，简洁明了）",
  "description": "（整体概述，2-3句，突出用途和外观）",
  "components": [
    {{"name": "部件名称", "feature": "简要特征/作用"}},
    ...
  ],
  "materials and techniques": "（如可判断，简要描述主要材料和工艺亮点）"
}}
```
注意：所有字段均需填写，components为部件列表，feature为该部件的简要说明。
风格：简洁专业但易懂，避免长复句，适当使用空间位置词。
"""

    # 合并 system prompt 到 user prompt
    full_prompt = mllm_system_prompt.strip() + "\n" + mllm_prompt.strip()
    messages = [
        {"role": "user", "content": full_prompt}
    ]

    # 使用 MultiProviderLLM 进行多模态对话
    description = llm.chat_with_images(messages=messages, image_paths=[image_path])
    return description


def batch_describe_assemblies():
    """
    批量处理所有测试集中的装配体，生成描述并保存到description.json
    """
    # 获取所有测试路径
    test_paths = get_fusion360_test_paths()
    print(f"发现 {len(test_paths)} 个测试集文件夹")
    
    # 统计结果
    results = {
        "total": len(test_paths),
        "processed": 0,
        "success": 0,
        "failed": 0,
        "details": {}
    }
    
    # 使用tqdm显示进度条
    for test_path in tqdm(test_paths, desc="处理装配体描述"):
        # 构建文件路径
        json_path = os.path.join(test_path, "assembly.json")
        image_path = os.path.join(test_path, "assembly.png")
        description_path = os.path.join(test_path, "description.json")
        
        # 检查必要文件是否存在
        if not os.path.exists(json_path):
            print(f"警告: assembly.json不存在于路径: {test_path}")
            results["details"][test_path] = {"status": "failed", "error": "assembly.json不存在"}
            results["failed"] += 1
            continue
            
        if not os.path.exists(image_path):
            print(f"警告: assembly.png不存在于路径: {test_path}")
            results["details"][test_path] = {"status": "failed", "error": "assembly.png不存在"}
            results["failed"] += 1
            continue
        
        # 如果description.json已存在，跳过处理
        if os.path.exists(description_path):
            print(f"跳过（已存在）: {description_path}")
            results["details"][test_path] = {"status": "skipped", "reason": "description.json已存在"}
            results["processed"] += 1
            continue
        
        try:
            # 生成装配体描述
            print(f"正在处理: {test_path}")
            description = describe_assembly(json_path, image_path)
            
            # 尝试解析JSON格式的描述
            try:
                # 如果返回的是JSON字符串，尝试解析
                if isinstance(description, str):
                    # 提取JSON部分（如果有```json标记）
                    if "```json" in description:
                        start = description.find("```json") + 7
                        end = description.find("```", start)
                        json_str = description[start:end].strip()
                        description_data = json.loads(json_str)
                    elif description.strip().startswith("{"):
                        description_data = json.loads(description)
                    else:
                        # 如果不是JSON格式，包装成标准格式
                        description_data = {
                            "assembly_name": "未知装配体",
                            "description": description,
                            "components": [],
                            "materials and techniques": "未指定"
                        }
                else:
                    description_data = description
            except json.JSONDecodeError:
                # 如果解析失败，包装成标准格式
                description_data = {
                    "assembly_name": "未知装配体",
                    "description": str(description),
                    "components": [],
                    "materials and techniques": "未指定"
                }
            
            # 添加元数据
            description_data["metadata"] = {
                "source_json": json_path,
                "source_image": image_path,
                "generated_at": __import__('datetime').datetime.now().isoformat()
            }
            
            # 保存到description.json
            with open(description_path, 'w', encoding='utf-8') as f:
                json.dump(description_data, f, ensure_ascii=False, indent=2)
            
            print(f"成功生成描述: {description_path}")
            results["details"][test_path] = {"status": "success", "output_file": description_path}
            results["success"] += 1
            results["processed"] += 1
            
        except Exception as e:
            print(f"处理失败 {test_path}: {str(e)}")
            results["details"][test_path] = {"status": "failed", "error": str(e)}
            results["failed"] += 1
    
    # 输出最终统计结果
    print("\n" + "="*50)
    print("批量处理完成！")
    print(f"总计: {results['total']} 个文件夹")
    print(f"已处理: {results['processed']} 个")
    print(f"成功: {results['success']} 个")
    print(f"失败: {results['failed']} 个")
    print(f"跳过: {results['total'] - results['processed'] - results['failed']} 个")
    
    # 保存详细结果到日志文件
    log_path = "batch_describe_results.json"
    with open(log_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"详细结果已保存到: {log_path}")
    
    return results


if __name__ == "__main__":
    # 运行批量处理
    results = batch_describe_assemblies()
